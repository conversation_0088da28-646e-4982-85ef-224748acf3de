"""应用主入口"""

import logging

import uvicorn

from app.db.init_db import init_db
from app.db.session import SessionLocal

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def init() -> None:
    """初始化应用"""
    # 初始化数据库表结构
    from app.db.session import Base, engine

    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    logger.info("数据库表已创建")

    # 验证枚举定义是否正确
    try:
        from app.notifications.models import validate_notification_type_enum

        logger.info("开始验证NotificationType枚举定义...")
        validate_notification_type_enum()
        logger.info("NotificationType枚举定义验证完成")
    except Exception as e:
        logger.error(f"NotificationType枚举验证失败: {e}")

    # 初始化权限和测试数据
    async with SessionLocal() as db:
        # 初始化权限数据
        await init_db(db)


def main() -> None:
    """应用主入口"""
    logger.info("初始化应用...")
    import asyncio

    asyncio.run(init())
    logger.info("初始化完成")

    logger.info("启动应用服务器...")
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
    )


if __name__ == "__main__":
    main()
