# 权限迁移模板 - app/api/endpoints/backpack.py

## 当前问题
### 手写权限检查
- 第 31 行: `def verify_user_permission(user_id: int, current_user: models.User) -> None:`
- 第 33 行: `if current_user.id != user_id and not current_user.is_superuser:`
- 第 57 行: `verify_user_permission(user_id, current_user)`
- 第 105 行: `verify_user_permission(user_id, current_user)`
- 第 166 行: `verify_user_permission(user_id, current_user)`
- 第 218 行: `verify_user_permission(user_id, current_user)`

## 迁移步骤

### 2. 标准化权限检查
```python
# 移除手写检查，使用统一权限系统
# 权限检查已在依赖注入中处理
```

## 验证清单
- [ ] 移除所有废弃服务调用
- [ ] 添加必要的导入语句
- [ ] 更新路由函数参数
- [ ] 移除手写权限检查
- [ ] 运行测试确保功能正常