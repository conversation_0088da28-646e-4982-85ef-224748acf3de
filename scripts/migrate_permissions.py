#!/usr/bin/env python3
"""
权限迁移辅助脚本

帮助自动化权限系统的迁移过程，包括代码转换和验证。
"""

import re
import ast
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import argparse

class PermissionMigrator:
    """权限迁移助手"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.app_dir = self.project_root / "app"
        
        # 迁移规则映射
        self.migration_rules = {
            # UnifiedPermissionService 方法映射
            "check_article_access": {
                "pattern": r"unified_permission_service\.check_article_access\(([^)]+)\)",
                "replacement": "require_permission(Permissions.ARTICLE_READ_PUBLIC, resource_type=ResourceType.ARTICLE, resource_id_key=\"article_id\", optional_user=True)"
            },
            "check_video_access": {
                "pattern": r"unified_permission_service\.check_video_access\(([^)]+)\)",
                "replacement": "require_permission(Permissions.VIDEO_READ_PUBLIC, resource_type=ResourceType.VIDEO, resource_id_key=\"video_id\", optional_user=True)"
            },
            # 手写权限检查映射
            "superuser_check": {
                "pattern": r"if\s+current_user\.is_superuser:",
                "replacement": "# 超级用户权限已在权限系统中处理"
            },
            "owner_check": {
                "pattern": r"if\s+current_user\.id\s*==\s*(\w+)\.(\w+)_id:",
                "replacement": "# 所有权检查已在权限系统中处理"
            }
        }
        
        # 需要添加的导入
        self.required_imports = {
            "require_permission": "from app.api.permission_deps import require_permission",
            "Permissions": "from app.core.permissions import Permissions",
            "ResourceType": "from app.core.permission_system import ResourceType",
            "Action": "from app.core.permission_system import Action",
            "Scope": "from app.core.permission_system import Scope"
        }
    
    def analyze_file(self, file_path: Path) -> Dict:
        """分析文件中的权限使用情况"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return {"error": str(e)}
        
        analysis = {
            "file": str(file_path.relative_to(self.project_root)),
            "needs_migration": False,
            "deprecated_usage": [],
            "manual_checks": [],
            "missing_imports": [],
            "suggestions": []
        }
        
        lines = content.split('\n')
        
        # 检查废弃的服务使用
        for line_num, line in enumerate(lines, 1):
            if "UnifiedPermissionService" in line:
                analysis["deprecated_usage"].append({
                    "line": line_num,
                    "content": line.strip(),
                    "type": "deprecated_service"
                })
                analysis["needs_migration"] = True
            
            # 检查手写权限检查
            if re.search(r"current_user\.id\s*==|is_superuser|verify_user_permission", line):
                analysis["manual_checks"].append({
                    "line": line_num,
                    "content": line.strip(),
                    "type": "manual_check"
                })
                analysis["needs_migration"] = True
        
        # 检查缺失的导入
        for import_name, import_statement in self.required_imports.items():
            if import_name in content and import_statement not in content:
                analysis["missing_imports"].append(import_statement)
        
        return analysis
    
    def generate_migration_suggestions(self, analysis: Dict) -> List[str]:
        """生成迁移建议"""
        suggestions = []
        
        if analysis["deprecated_usage"]:
            suggestions.append("🔴 移除废弃的 UnifiedPermissionService 使用:")
            for usage in analysis["deprecated_usage"]:
                suggestions.append(f"  - 第 {usage['line']} 行: {usage['content']}")
            suggestions.append("  建议: 使用 require_permission 依赖注入替换")
            suggestions.append("")
        
        if analysis["manual_checks"]:
            suggestions.append("🟡 标准化手写权限检查:")
            for check in analysis["manual_checks"]:
                suggestions.append(f"  - 第 {check['line']} 行: {check['content']}")
            suggestions.append("  建议: 使用统一的权限检查模式")
            suggestions.append("")
        
        if analysis["missing_imports"]:
            suggestions.append("📦 添加缺失的导入:")
            for import_stmt in analysis["missing_imports"]:
                suggestions.append(f"  - {import_stmt}")
            suggestions.append("")
        
        return suggestions
    
    def create_migration_template(self, file_path: Path) -> str:
        """为文件创建迁移模板"""
        analysis = self.analyze_file(file_path)
        
        if not analysis["needs_migration"]:
            return "# 此文件无需迁移"
        
        template = []
        template.append(f"# 权限迁移模板 - {analysis['file']}")
        template.append("")
        template.append("## 当前问题")
        
        if analysis["deprecated_usage"]:
            template.append("### 废弃服务使用")
            for usage in analysis["deprecated_usage"]:
                template.append(f"- 第 {usage['line']} 行: `{usage['content']}`")
            template.append("")
        
        if analysis["manual_checks"]:
            template.append("### 手写权限检查")
            for check in analysis["manual_checks"]:
                template.append(f"- 第 {check['line']} 行: `{check['content']}`")
            template.append("")
        
        template.append("## 迁移步骤")
        template.append("")
        
        # 生成具体的迁移步骤
        if analysis["deprecated_usage"]:
            template.append("### 1. 替换废弃服务")
            template.append("```python")
            template.append("# 旧方式")
            template.append("unified_service.check_article_access(user, article)")
            template.append("")
            template.append("# 新方式 - 在路由函数参数中添加")
            template.append("current_user: User = Depends(require_permission(")
            template.append("    Permissions.ARTICLE_READ_PUBLIC,")
            template.append("    resource_type=ResourceType.ARTICLE,")
            template.append("    resource_id_key=\"article_id\",")
            template.append("    optional_user=True")
            template.append("))")
            template.append("```")
            template.append("")
        
        if analysis["manual_checks"]:
            template.append("### 2. 标准化权限检查")
            template.append("```python")
            template.append("# 移除手写检查，使用统一权限系统")
            template.append("# 权限检查已在依赖注入中处理")
            template.append("```")
            template.append("")
        
        if analysis["missing_imports"]:
            template.append("### 3. 添加必要导入")
            template.append("```python")
            for import_stmt in analysis["missing_imports"]:
                template.append(import_stmt)
            template.append("```")
            template.append("")
        
        template.append("## 验证清单")
        template.append("- [ ] 移除所有废弃服务调用")
        template.append("- [ ] 添加必要的导入语句")
        template.append("- [ ] 更新路由函数参数")
        template.append("- [ ] 移除手写权限检查")
        template.append("- [ ] 运行测试确保功能正常")
        
        return "\n".join(template)
    
    def scan_project(self) -> Dict:
        """扫描整个项目"""
        results = {
            "total_files": 0,
            "files_need_migration": 0,
            "files_analysis": [],
            "summary": {
                "deprecated_usage_count": 0,
                "manual_checks_count": 0,
                "files_with_issues": []
            }
        }
        
        for py_file in self.app_dir.rglob("*.py"):
            if "__pycache__" in str(py_file):
                continue
            
            results["total_files"] += 1
            analysis = self.analyze_file(py_file)
            
            if "error" not in analysis:
                results["files_analysis"].append(analysis)
                
                if analysis["needs_migration"]:
                    results["files_need_migration"] += 1
                    results["summary"]["files_with_issues"].append(analysis["file"])
                
                results["summary"]["deprecated_usage_count"] += len(analysis["deprecated_usage"])
                results["summary"]["manual_checks_count"] += len(analysis["manual_checks"])
        
        return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="权限系统迁移助手")
    parser.add_argument("--scan", action="store_true", help="扫描项目中需要迁移的文件")
    parser.add_argument("--file", type=str, help="分析特定文件")
    parser.add_argument("--template", type=str, help="为特定文件生成迁移模板")
    parser.add_argument("--output", type=str, help="输出文件路径")
    
    args = parser.parse_args()
    
    migrator = PermissionMigrator()
    
    if args.scan:
        print("🔍 扫描项目中的权限使用情况...")
        results = migrator.scan_project()
        
        print(f"📊 扫描结果:")
        print(f"  - 总文件数: {results['total_files']}")
        print(f"  - 需要迁移: {results['files_need_migration']}")
        print(f"  - 废弃服务使用: {results['summary']['deprecated_usage_count']} 处")
        print(f"  - 手写权限检查: {results['summary']['manual_checks_count']} 处")
        
        if results["summary"]["files_with_issues"]:
            print(f"\n📝 需要迁移的文件:")
            for file_path in results["summary"]["files_with_issues"]:
                print(f"  - {file_path}")
    
    elif args.file:
        file_path = Path(args.file)
        if not file_path.exists():
            print(f"❌ 文件不存在: {file_path}")
            return
        
        print(f"🔍 分析文件: {file_path}")
        analysis = migrator.analyze_file(file_path)
        
        if "error" in analysis:
            print(f"❌ 分析失败: {analysis['error']}")
            return
        
        suggestions = migrator.generate_migration_suggestions(analysis)
        if suggestions:
            print("\n📋 迁移建议:")
            for suggestion in suggestions:
                print(suggestion)
        else:
            print("✅ 此文件无需迁移")
    
    elif args.template:
        file_path = Path(args.template)
        if not file_path.exists():
            print(f"❌ 文件不存在: {file_path}")
            return
        
        print(f"📝 生成迁移模板: {file_path}")
        template = migrator.create_migration_template(file_path)
        
        if args.output:
            output_path = Path(args.output)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(template)
            print(f"✅ 模板已保存到: {output_path}")
        else:
            print(template)
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
