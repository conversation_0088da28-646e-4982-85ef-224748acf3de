# 权限系统重构工具集

这个工具集帮助你完成权限系统的重构工作，包括代码审计、迁移辅助、测试生成等功能。

## 🛠️ 工具列表

### 1. 权限审计工具 (`audit_permissions.py`)
分析当前权限系统的使用情况，识别需要重构的代码。

```bash
# 运行权限审计
python scripts/audit_permissions.py

# 查看生成的报告
cat permission_audit_report.md
```

**功能**:
- 扫描所有 Python 文件中的权限使用
- 识别废弃的服务调用
- 发现手写的权限检查逻辑
- 生成详细的审计报告

### 2. 权限迁移助手 (`migrate_permissions.py`)
提供自动化的权限迁移建议和模板生成。

```bash
# 扫描整个项目
python scripts/migrate_permissions.py --scan

# 分析特定文件
python scripts/migrate_permissions.py --file app/api/endpoints/articles.py

# 生成迁移模板
python scripts/migrate_permissions.py --template app/api/endpoints/articles.py --output migration_template.md
```

**功能**:
- 自动识别需要迁移的代码
- 生成具体的迁移建议
- 创建迁移模板和检查清单
- 提供优先级排序

### 3. 权限测试生成器 (`generate_permission_tests.py`)
自动生成权限系统的测试用例。

```bash
# 生成所有资源的权限测试
python scripts/generate_permission_tests.py --all

# 生成特定资源的测试
python scripts/generate_permission_tests.py --resource article

# 指定输出目录
python scripts/generate_permission_tests.py --all --output-dir tests/permissions
```

**功能**:
- 自动分析 API 端点
- 生成完整的权限测试用例
- 包含各种权限场景测试
- 支持资源所有权和公开访问测试

## 📋 使用流程

### 阶段 1: 现状分析
```bash
# 1. 运行权限审计
python scripts/audit_permissions.py

# 2. 查看审计报告
cat permission_audit_report.md

# 3. 扫描项目获取概览
python scripts/migrate_permissions.py --scan
```

### 阶段 2: 制定迁移计划
```bash
# 1. 为关键文件生成迁移模板
python scripts/migrate_permissions.py --template app/api/endpoints/articles.py --output docs/migration_articles.md

# 2. 分析具体文件的迁移需求
python scripts/migrate_permissions.py --file app/services/unified_permission_service.py
```

### 阶段 3: 执行迁移
根据生成的模板和建议，手动执行代码迁移：

1. **移除废弃服务**
   - 删除 `UnifiedPermissionService` 的引用
   - 替换为 `require_permission` 依赖注入

2. **标准化权限检查**
   - 移除手写权限检查逻辑
   - 使用统一的权限常量

3. **更新导入语句**
   - 添加必要的权限相关导入
   - 移除不再使用的导入

### 阶段 4: 生成测试
```bash
# 生成权限测试用例
python scripts/generate_permission_tests.py --all --output-dir tests/permissions

# 运行测试验证
pytest tests/permissions/ -v
```

### 阶段 5: 权限同步
```python
# 在 Python 环境中运行权限同步
from app.core.permission_sync import PermissionSyncService
from app.db.session import get_db

async def sync_permissions():
    sync_service = PermissionSyncService()
    async with get_db() as db:
        result = await sync_service.sync_permissions_to_db(db)
        print(f"同步结果: {result}")

# 运行同步
import asyncio
asyncio.run(sync_permissions())
```

## 📊 输出文件说明

### 审计报告 (`permission_audit_report.md`)
- 权限使用统计概览
- 详细的问题分析
- 重构建议和优先级

### 迁移模板 (`migration_*.md`)
- 当前问题描述
- 具体的迁移步骤
- 验证检查清单

### 权限测试 (`tests/permissions/test_*_permissions.py`)
- 完整的权限测试用例
- 覆盖各种权限场景
- 包含边界情况测试

## ⚠️ 注意事项

### 安全考虑
- 在生产环境执行权限同步前，请先在测试环境验证
- 备份现有的权限配置
- 仔细审查生成的迁移建议

### 性能影响
- 权限审计工具会扫描所有 Python 文件，可能需要一些时间
- 权限同步会修改数据库，建议在维护窗口执行

### 兼容性
- 工具基于当前的权限系统架构设计
- 如果权限系统有重大变更，可能需要更新工具

## 🔧 自定义配置

### 修改扫描范围
编辑 `audit_permissions.py` 中的 `patterns` 字典来调整扫描模式：

```python
self.patterns = {
    "unified_service": r"UnifiedPermissionService",
    "custom_pattern": r"your_custom_pattern",
    # 添加更多模式...
}
```

### 自定义测试模板
编辑 `generate_permission_tests.py` 中的 `test_templates` 来自定义测试模板：

```python
self.test_templates = {
    "custom_test": '''
    async def test_custom_scenario():
        # 你的自定义测试逻辑
        pass
    '''
}
```

## 📞 支持

如果在使用过程中遇到问题：

1. 检查工具的输出日志
2. 查看生成的报告文件
3. 参考重构文档 (`docs/permission_system_refactoring.md`)
4. 联系开发团队获取支持

## 📝 更新日志

- **v1.0** (2025-01-23): 初始版本，包含基本的审计、迁移和测试生成功能
- 后续版本将根据使用反馈进行优化和功能增强
