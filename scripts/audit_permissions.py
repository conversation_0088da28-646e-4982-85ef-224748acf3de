#!/usr/bin/env python3
"""
权限系统审计脚本

用于分析当前权限系统的使用情况，识别需要重构的代码。
"""

import ast
import os
import re
from collections import defaultdict
from pathlib import Path
from typing import Dict, List, Set, Tuple

class PermissionAuditor:
    """权限使用审计器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.app_dir = self.project_root / "app"
        
        # 需要检查的模式
        self.patterns = {
            "unified_service": r"UnifiedPermissionService",
            "permission_checker": r"PermissionChecker\.(check_permission|require_permission)",
            "require_permission": r"require_permission\(",
            "manual_permission": r"(is_superuser|current_user\.id\s*==|verify_user_permission)",
            "permission_creation": r"Permission\(",
            "permission_constants": r"Permissions\.",
        }
        
        self.results = defaultdict(list)
        
    def scan_files(self) -> None:
        """扫描所有 Python 文件"""
        for py_file in self.app_dir.rglob("*.py"):
            if "__pycache__" in str(py_file):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self._analyze_file(py_file, content)
            except Exception as e:
                print(f"Error reading {py_file}: {e}")
    
    def _analyze_file(self, file_path: Path, content: str) -> None:
        """分析单个文件"""
        relative_path = file_path.relative_to(self.project_root)
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            for pattern_name, pattern in self.patterns.items():
                if re.search(pattern, line):
                    self.results[pattern_name].append({
                        'file': str(relative_path),
                        'line': line_num,
                        'content': line.strip(),
                        'pattern': pattern_name
                    })
    
    def generate_report(self) -> str:
        """生成审计报告"""
        report = []
        report.append("# 权限系统使用审计报告")
        report.append(f"扫描目录: {self.app_dir}")
        report.append(f"生成时间: {__import__('datetime').datetime.now()}")
        report.append("")
        
        # 统计概览
        report.append("## 📊 统计概览")
        report.append("")
        total_issues = sum(len(matches) for matches in self.results.values())
        report.append(f"- 总计发现问题: {total_issues}")
        
        for pattern_name, matches in self.results.items():
            if matches:
                report.append(f"- {pattern_name}: {len(matches)} 处")
        report.append("")
        
        # 详细分析
        report.append("## 🔍 详细分析")
        report.append("")
        
        # 废弃服务使用
        if self.results["unified_service"]:
            report.append("### ❌ 废弃服务使用 (需要移除)")
            report.append("")
            for match in self.results["unified_service"]:
                report.append(f"- `{match['file']}:{match['line']}` - {match['content']}")
            report.append("")
        
        # 直接调用权限检查器
        if self.results["permission_checker"]:
            report.append("### ⚠️ 直接调用权限检查器 (建议改为依赖注入)")
            report.append("")
            for match in self.results["permission_checker"]:
                report.append(f"- `{match['file']}:{match['line']}` - {match['content']}")
            report.append("")
        
        # 手写权限检查
        if self.results["manual_permission"]:
            report.append("### 🔧 手写权限检查 (需要标准化)")
            report.append("")
            for match in self.results["manual_permission"]:
                report.append(f"- `{match['file']}:{match['line']}` - {match['content']}")
            report.append("")
        
        # 正确使用的模式
        if self.results["require_permission"]:
            report.append("### ✅ 正确使用依赖注入")
            report.append("")
            report.append(f"发现 {len(self.results['require_permission'])} 处正确使用")
            report.append("")
        
        if self.results["permission_constants"]:
            report.append("### ✅ 使用权限常量")
            report.append("")
            report.append(f"发现 {len(self.results['permission_constants'])} 处使用权限常量")
            report.append("")
        
        # 重构建议
        report.append("## 🎯 重构建议")
        report.append("")
        
        if self.results["unified_service"]:
            report.append("### 高优先级")
            report.append("1. 移除所有 `UnifiedPermissionService` 的使用")
            report.append("2. 替换为 `require_permission` 依赖注入")
            report.append("")
        
        if self.results["manual_permission"]:
            report.append("### 中优先级")
            report.append("1. 标准化手写权限检查逻辑")
            report.append("2. 使用统一的权限常量")
            report.append("")
        
        if self.results["permission_checker"]:
            report.append("### 低优先级")
            report.append("1. 避免直接调用 `PermissionChecker`")
            report.append("2. 优先使用依赖注入模式")
            report.append("")
        
        return "\n".join(report)
    
    def get_migration_suggestions(self) -> List[Dict]:
        """获取迁移建议"""
        suggestions = []
        
        # 废弃服务迁移建议
        for match in self.results["unified_service"]:
            suggestions.append({
                'type': 'remove_deprecated',
                'priority': 'high',
                'file': match['file'],
                'line': match['line'],
                'current': match['content'],
                'suggestion': '使用 require_permission 依赖注入替换'
            })
        
        # 手写权限检查迁移建议
        for match in self.results["manual_permission"]:
            suggestions.append({
                'type': 'standardize_check',
                'priority': 'medium',
                'file': match['file'],
                'line': match['line'],
                'current': match['content'],
                'suggestion': '使用标准权限检查模式'
            })
        
        return suggestions

def main():
    """主函数"""
    auditor = PermissionAuditor()
    
    print("🔍 开始扫描权限系统使用情况...")
    auditor.scan_files()
    
    print("📝 生成审计报告...")
    report = auditor.generate_report()
    
    # 保存报告
    report_file = Path("permission_audit_report.md")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"✅ 审计完成，报告已保存到: {report_file}")
    
    # 输出简要统计
    total_issues = sum(len(matches) for matches in auditor.results.values())
    print(f"📊 发现 {total_issues} 个需要关注的权限使用点")
    
    # 输出迁移建议
    suggestions = auditor.get_migration_suggestions()
    high_priority = [s for s in suggestions if s['priority'] == 'high']
    
    if high_priority:
        print(f"🔴 高优先级问题: {len(high_priority)} 个")
        print("建议立即处理以下文件:")
        for suggestion in high_priority[:5]:  # 只显示前5个
            print(f"  - {suggestion['file']}:{suggestion['line']}")

if __name__ == "__main__":
    main()
