#!/usr/bin/env python3
"""
权限测试生成器

自动生成权限系统的测试用例，确保重构后的权限检查正确性。
"""

import ast
import re
from pathlib import Path
from typing import Dict, List, Set
import argparse

class PermissionTestGenerator:
    """权限测试生成器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.app_dir = self.project_root / "app"
        
        # 权限测试模板
        self.test_templates = {
            "basic_permission": '''
async def test_{resource}_{action}_permission(
    client: AsyncClient,
    db: AsyncSession,
    {user_fixtures}
):
    """测试 {resource} {action} 权限"""
    # 测试未登录用户
    response = await client.{method}("{endpoint}")
    assert response.status_code == 401
    
    # 测试无权限用户
    response = await client.{method}(
        "{endpoint}",
        headers={{"Authorization": f"Bearer {{normal_user_token}}"}}
    )
    assert response.status_code == 403
    
    # 测试有权限用户
    response = await client.{method}(
        "{endpoint}",
        headers={{"Authorization": f"Bearer {{authorized_user_token}}"}}
    )
    assert response.status_code in [200, 201, 204]
''',
            
            "resource_ownership": '''
async def test_{resource}_ownership_permission(
    client: AsyncClient,
    db: AsyncSession,
    normal_user: User,
    other_user: User,
    {resource}_factory
):
    """测试 {resource} 所有权权限"""
    # 创建资源
    {resource}_obj = await {resource}_factory(author_id=normal_user.id)
    
    # 测试所有者访问
    response = await client.{method}(
        f"/{resource}s/{{{resource}_obj.id}}",
        headers={{"Authorization": f"Bearer {{normal_user_token}}"}}
    )
    assert response.status_code in [200, 201, 204]
    
    # 测试非所有者访问
    response = await client.{method}(
        f"/{resource}s/{{{resource}_obj.id}}",
        headers={{"Authorization": f"Bearer {{other_user_token}}"}}
    )
    assert response.status_code == 403
''',
            
            "public_resource": '''
async def test_{resource}_public_access(
    client: AsyncClient,
    db: AsyncSession,
    {resource}_factory
):
    """测试 {resource} 公开访问权限"""
    # 创建公开资源
    {resource}_obj = await {resource}_factory(
        is_published=True,
        is_approved=True
    )
    
    # 测试未登录用户访问公开资源
    response = await client.get(f"/{resource}s/{{{resource}_obj.id}}")
    assert response.status_code == 200
    
    # 创建私有资源
    private_{resource} = await {resource}_factory(
        is_published=False,
        is_approved=False
    )
    
    # 测试未登录用户访问私有资源
    response = await client.get(f"/{resource}s/{{private_{resource}.id}}")
    assert response.status_code == 401
''',
            
            "admin_permission": '''
async def test_{resource}_admin_permission(
    client: AsyncClient,
    db: AsyncSession,
    admin_user: User,
    normal_user: User,
    {resource}_factory
):
    """测试 {resource} 管理员权限"""
    # 创建资源
    {resource}_obj = await {resource}_factory(author_id=normal_user.id)
    
    # 测试管理员访问他人资源
    response = await client.{method}(
        f"/{resource}s/{{{resource}_obj.id}}",
        headers={{"Authorization": f"Bearer {{admin_user_token}}"}}
    )
    assert response.status_code in [200, 201, 204]
    
    # 测试管理员删除权限
    response = await client.delete(
        f"/{resource}s/{{{resource}_obj.id}}",
        headers={{"Authorization": f"Bearer {{admin_user_token}}"}}
    )
    assert response.status_code in [200, 204]
'''
        }
    
    def extract_api_endpoints(self) -> List[Dict]:
        """提取 API 端点信息"""
        endpoints = []
        
        for py_file in (self.app_dir / "api" / "endpoints").rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 解析路由定义
                router_matches = re.findall(
                    r'@router\.(get|post|put|delete|patch)\(["\']([^"\']+)["\'].*?\)\s*async def (\w+)',
                    content,
                    re.MULTILINE | re.DOTALL
                )
                
                for method, path, func_name in router_matches:
                    # 检查是否使用了权限检查
                    func_content = self._extract_function_content(content, func_name)
                    if "require_permission" in func_content or "Depends" in func_content:
                        endpoints.append({
                            "file": py_file.name,
                            "method": method.upper(),
                            "path": path,
                            "function": func_name,
                            "has_permission": True,
                            "resource_type": self._infer_resource_type(path, func_name)
                        })
            
            except Exception as e:
                print(f"Error parsing {py_file}: {e}")
        
        return endpoints
    
    def _extract_function_content(self, content: str, func_name: str) -> str:
        """提取函数内容"""
        try:
            tree = ast.parse(content)
            for node in ast.walk(tree):
                if isinstance(node, ast.AsyncFunctionDef) and node.name == func_name:
                    return ast.get_source_segment(content, node) or ""
        except:
            pass
        return ""
    
    def _infer_resource_type(self, path: str, func_name: str) -> str:
        """推断资源类型"""
        # 从路径推断
        if "/articles" in path:
            return "article"
        elif "/videos" in path:
            return "video"
        elif "/users" in path:
            return "user"
        elif "/comments" in path:
            return "comment"
        
        # 从函数名推断
        if "article" in func_name.lower():
            return "article"
        elif "video" in func_name.lower():
            return "video"
        elif "user" in func_name.lower():
            return "user"
        elif "comment" in func_name.lower():
            return "comment"
        
        return "resource"
    
    def generate_test_file(self, resource_type: str, endpoints: List[Dict]) -> str:
        """生成测试文件内容"""
        test_content = []
        
        # 文件头部
        test_content.append(f'''"""
{resource_type.title()} 权限测试

自动生成的权限测试用例，确保 {resource_type} 相关权限检查正确。
"""

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User
from tests.utils.utils import random_lower_string


class Test{resource_type.title()}Permissions:
    """测试 {resource_type} 权限"""
''')
        
        # 为每个端点生成测试
        for endpoint in endpoints:
            if endpoint["resource_type"] == resource_type:
                test_method = self._generate_endpoint_test(endpoint)
                test_content.append(test_method)
        
        # 添加通用权限测试
        test_content.append(self._generate_common_tests(resource_type))
        
        return "\n".join(test_content)
    
    def _generate_endpoint_test(self, endpoint: Dict) -> str:
        """为特定端点生成测试"""
        method = endpoint["method"].lower()
        path = endpoint["path"]
        func_name = endpoint["function"]
        resource = endpoint["resource_type"]
        
        # 判断测试类型
        if "{" in path and method in ["put", "delete", "patch"]:
            # 资源所有权测试
            return self.test_templates["resource_ownership"].format(
                resource=resource,
                method=method
            )
        elif method == "get" and "{" in path:
            # 公开资源访问测试
            return self.test_templates["public_resource"].format(
                resource=resource
            )
        else:
            # 基本权限测试
            action = self._infer_action(method, func_name)
            return self.test_templates["basic_permission"].format(
                resource=resource,
                action=action,
                method=method,
                endpoint=path.replace("{", "{").replace("}", "}"),
                user_fixtures="normal_user: User, admin_user: User"
            )
    
    def _infer_action(self, method: str, func_name: str) -> str:
        """推断操作类型"""
        if method == "post" or "create" in func_name:
            return "create"
        elif method == "get" or "read" in func_name or "get" in func_name:
            return "read"
        elif method in ["put", "patch"] or "update" in func_name:
            return "update"
        elif method == "delete" or "delete" in func_name:
            return "delete"
        else:
            return "access"
    
    def _generate_common_tests(self, resource_type: str) -> str:
        """生成通用权限测试"""
        return f'''
    async def test_{resource_type}_permission_inheritance(
        self,
        client: AsyncClient,
        db: AsyncSession,
        superuser: User,
        {resource_type}_factory
    ):
        """测试权限继承 - 超级用户应该有所有权限"""
        {resource_type}_obj = await {resource_type}_factory()
        
        # 测试超级用户的所有权限
        for method in ["GET", "PUT", "DELETE"]:
            response = await client.request(
                method,
                f"/{resource_type}s/{{{resource_type}_obj.id}}",
                headers={{"Authorization": f"Bearer {{superuser_token}}"}}
            )
            assert response.status_code != 403, f"超级用户应该有 {{method}} 权限"
    
    async def test_{resource_type}_permission_scope(
        self,
        client: AsyncClient,
        db: AsyncSession,
        normal_user: User,
        other_user: User,
        {resource_type}_factory
    ):
        """测试权限范围 - OWN vs ALL"""
        # 创建用户自己的资源
        own_{resource_type} = await {resource_type}_factory(author_id=normal_user.id)
        
        # 创建他人的资源
        other_{resource_type} = await {resource_type}_factory(author_id=other_user.id)
        
        # 测试访问自己的资源
        response = await client.get(
            f"/{resource_type}s/{{own_{resource_type}.id}}",
            headers={{"Authorization": f"Bearer {{normal_user_token}}"}}
        )
        assert response.status_code == 200
        
        # 测试访问他人的私有资源（应该失败）
        response = await client.get(
            f"/{resource_type}s/{{other_{resource_type}.id}}",
            headers={{"Authorization": f"Bearer {{normal_user_token}}"}}
        )
        # 根据资源是否公开决定期望结果
        assert response.status_code in [200, 403]
'''
    
    def generate_all_tests(self) -> Dict[str, str]:
        """生成所有权限测试"""
        endpoints = self.extract_api_endpoints()
        
        # 按资源类型分组
        resources = set(endpoint["resource_type"] for endpoint in endpoints)
        
        test_files = {}
        for resource in resources:
            resource_endpoints = [ep for ep in endpoints if ep["resource_type"] == resource]
            test_content = self.generate_test_file(resource, resource_endpoints)
            test_files[f"test_{resource}_permissions.py"] = test_content
        
        return test_files

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="权限测试生成器")
    parser.add_argument("--resource", type=str, help="生成特定资源的测试")
    parser.add_argument("--output-dir", type=str, default="tests/permissions", help="输出目录")
    parser.add_argument("--all", action="store_true", help="生成所有资源的测试")
    
    args = parser.parse_args()
    
    generator = PermissionTestGenerator()
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    if args.all:
        print("🧪 生成所有权限测试...")
        test_files = generator.generate_all_tests()
        
        for filename, content in test_files.items():
            file_path = output_dir / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 生成测试文件: {file_path}")
        
        print(f"📊 总计生成 {len(test_files)} 个测试文件")
    
    elif args.resource:
        print(f"🧪 生成 {args.resource} 权限测试...")
        endpoints = generator.extract_api_endpoints()
        resource_endpoints = [ep for ep in endpoints if ep["resource_type"] == args.resource]
        
        if not resource_endpoints:
            print(f"❌ 未找到 {args.resource} 相关的端点")
            return
        
        test_content = generator.generate_test_file(args.resource, resource_endpoints)
        file_path = output_dir / f"test_{args.resource}_permissions.py"
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"✅ 生成测试文件: {file_path}")
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
