#!/usr/bin/env python3
"""
数据库迁移脚本
用于创建点赞和收藏功能所需的数据库表

使用方法:
python migrate_database.py
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import logging

from app.db.migrate_like_favorite import migrate_like_favorite_tables

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    """主函数"""
    try:
        logger.info("开始执行数据库迁移...")
        migrate_like_favorite_tables()
        logger.info("数据库迁移完成！")
        print("\n✅ 数据库迁移成功完成！")
        print("现在您可以使用点赞和收藏功能了。")
        
    except Exception as e:
        logger.error(f"数据库迁移失败: {e}")
        print(f"\n❌ 数据库迁移失败: {e}")
        print("请检查数据库连接和权限设置。")
        sys.exit(1)


if __name__ == "__main__":
    main()
