# 权限系统使用审计报告
扫描目录: app
生成时间: 2025-09-23 03:10:52.499960

## 📊 统计概览

- 总计发现问题: 255
- permission_creation: 130 处
- manual_permission: 46 处
- require_permission: 44 处
- permission_checker: 13 处
- unified_service: 5 处
- permission_constants: 17 处

## 🔍 详细分析

### ❌ 废弃服务使用 (需要移除)

- `app/services/unified_permission_service.py:24` - class UnifiedPermissionService:
- `app/services/service_factory.py:38` - from app.services.unified_permission_service import UnifiedPermissionService
- `app/services/service_factory.py:402` - def get_unified_permission_service(db: AsyncSession = Depends(get_db)) -> UnifiedPermissionService:
- `app/services/service_factory.py:404` - 获取 UnifiedPermissionService 的实例。
- `app/services/service_factory.py:406` - return UnifiedPermissionService(db)

### ⚠️ 直接调用权限检查器 (建议改为依赖注入)

- `app/core/permission_system.py:418` - if not await PermissionChecker.check_permission(db, user, permission, resource):
- `app/core/permission_system.py:447` - return await PermissionChecker.check_permission(db, user, permission)
- `app/core/permission_system.py:487` - return await PermissionChecker.check_permission(db, user, permission)
- `app/services/unified_permission_service.py:39` - return PermissionChecker.check_permission(user, permission, resource)
- `app/services/unified_permission_service.py:45` - PermissionChecker.require_permission(user, permission, resource)
- `app/api/permission_deps.py:78` - await PermissionChecker.require_permission(db, current_user, permission, resource)
- `app/api/endpoints/users.py:107` - await PermissionChecker.require_permission(
- `app/api/endpoints/recommendations.py:157` - is_admin = await PermissionChecker.check_permission(
- `app/api/endpoints/videos.py:299` - has_view_all_permission = await PermissionChecker.check_permission(
- `app/api/endpoints/comments.py:696` - can_update_own = await PermissionChecker.check_permission(
- `app/api/endpoints/comments.py:701` - can_manage = await PermissionChecker.check_permission(
- `app/api/endpoints/comments.py:735` - can_delete_own = await PermissionChecker.check_permission(
- `app/api/endpoints/comments.py:740` - can_manage = await PermissionChecker.check_permission(

### 🔧 手写权限检查 (需要标准化)

- `app/core/permission_system.py:367` - if user and user.is_superuser:
- `app/schemas/user.py:52` - is_superuser: bool = Field(False, description="是否超级用户")
- `app/services/article_aggregation_service.py:230` - is_admin = current_user and current_user.is_superuser
- `app/services/article_aggregation_service.py:298` - is_owner = current_user and current_user.id == article_base.author_id
- `app/services/scratch_adaptation_service.py:240` - if project.author_id != user.id and not user.is_superuser:
- `app/services/video_aggregation_service.py:200` - is_owner = current_user and current_user.id == video_base.author_id
- `app/services/video_aggregation_service.py:266` - is_owner = current_user and current_user.id == folder.user_id
- `app/services/video_aggregation_service.py:267` - is_admin = current_user and current_user.is_superuser
- `app/crud/user.py:30` - if "is_superuser" in filters:
- `app/crud/user.py:31` - query = query.where(self.model.is_superuser == filters["is_superuser"])
- `app/crud/user.py:159` - return user.is_superuser
- `app/api/deps.py:101` - if not current_user.is_superuser:
- `app/db/init_permissions.py:459` - is_superuser=True,
- `app/models/user.py:110` - is_superuser = Column(Boolean, default=False)
- `app/models/user.py:170` - if self.is_superuser:
- `app/api/endpoints/users.py:115` - if user.is_superuser and not current_user.is_superuser:
- `app/api/endpoints/users.py:184` - if user.is_superuser and not current_user.is_superuser:
- `app/api/endpoints/users.py:303` - is_author = current_user and current_user.id == user_id
- `app/api/endpoints/users.py:304` - is_admin = current_user and current_user.is_superuser
- `app/api/endpoints/users.py:380` - is_owner = current_user and current_user.id == user_id
- `app/api/endpoints/users.py:381` - is_admin = current_user and current_user.is_superuser
- `app/api/endpoints/users.py:457` - is_author = current_user and current_user.id == user_id
- `app/api/endpoints/users.py:458` - is_admin = current_user and current_user.is_superuser
- `app/api/endpoints/users.py:534` - is_author = current_user and current_user.id == user_id
- `app/api/endpoints/users.py:535` - is_admin = current_user and current_user.is_superuser
- `app/api/endpoints/users.py:602` - is_owner = current_user and current_user.id == user_id
- `app/api/endpoints/users.py:603` - is_admin = current_user and current_user.is_superuser
- `app/api/endpoints/users.py:657` - is_author = current_user and current_user.id == user_id
- `app/api/endpoints/users.py:658` - is_admin = current_user and current_user.is_superuser
- `app/api/endpoints/users.py:730` - is_owner = current_user and current_user.id == user_id
- `app/api/endpoints/users.py:731` - is_admin = current_user and current_user.is_superuser
- `app/api/endpoints/scratch.py:463` - is_owner = current_user and current_user.id == user_id
- `app/api/endpoints/scratch.py:464` - is_admin = current_user and current_user.is_superuser
- `app/api/endpoints/recommendations.py:162` - if not (is_admin or current_user.id == user_id):
- `app/api/endpoints/articles.py:614` - is_author = current_user and current_user.id == user_id
- `app/api/endpoints/articles.py:615` - is_admin = current_user and current_user.is_superuser
- `app/api/endpoints/videos.py:291` - is_own_folders = current_user and current_user.id == user_id
- `app/api/endpoints/videos.py:450` - is_own_folder = current_user and current_user.id == folder.user_id
- `app/api/endpoints/videos.py:451` - is_admin = current_user and current_user.is_superuser
- `app/api/endpoints/banners.py:28` - if not current_user.is_superuser:
- `app/api/endpoints/backpack.py:31` - def verify_user_permission(user_id: int, current_user: models.User) -> None:
- `app/api/endpoints/backpack.py:33` - if current_user.id != user_id and not current_user.is_superuser:
- `app/api/endpoints/backpack.py:57` - verify_user_permission(user_id, current_user)
- `app/api/endpoints/backpack.py:105` - verify_user_permission(user_id, current_user)
- `app/api/endpoints/backpack.py:166` - verify_user_permission(user_id, current_user)
- `app/api/endpoints/backpack.py:218` - verify_user_permission(user_id, current_user)

### ✅ 正确使用依赖注入

发现 44 处正确使用

### ✅ 使用权限常量

发现 17 处使用权限常量

## 🎯 重构建议

### 高优先级
1. 移除所有 `UnifiedPermissionService` 的使用
2. 替换为 `require_permission` 依赖注入

### 中优先级
1. 标准化手写权限检查逻辑
2. 使用统一的权限常量

### 低优先级
1. 避免直接调用 `PermissionChecker`
2. 优先使用依赖注入模式
