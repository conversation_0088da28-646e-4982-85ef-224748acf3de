# Backpack 模块 API 文档

面向前端的 Scratch 背包接口说明，涵盖列表、创建、删除与统计四个端点，便于在 `backpackHost` 集成中直接对接。

## 1. 基础信息
- **基础路径**：`/api/v1/backpack`
- **鉴权方式**：
  - HTTP Header `Authorization: Bearer <token>`（推荐）；
  - 或 Cookie `access_token=<token>`。
- **访问控制**：路径参数 `username` 必须等于登录用户的 username；仅超级管理员可越权访问其他用户。
- **统一响应包装**（除 `204 No Content` 外）：
  ```json
  {
    "success": true,
    "code": "OK",
    "message": "操作成功",
    "data": {},
    "timestamp": "2024-06-01T12:00:00.000123"
  }
  ```
  前端应从 `data` 字段读取真实业务数据。
- **速率限制**：基于客户端 IP，列表/创建/删除为 `30 次/分钟`，统计为 `10 次/分钟`。超限返回 `429 Too Many Requests`。
- **通用首部**：`Content-Type: application/json`；上传 Base64 时无需 `data:image/...;base64,` 前缀。

## 2. 数据模型与约束

### 2.1 背包项目类型 `BackpackItemType`

| 枚举值 | 场景 |
| -- | -- |
| `costume` | 造型（SVG/PNG/JPEG/GIF） |
| `sound` | 声音（WAV/MP3/OGG） |
| `sprite` | 精灵（压缩包，最终以 `.dat` 保存） |
| `script` | 脚本（JSON） |

### 2.2 MIME 白名单

| type | 允许的 `mime` |
| -- | -- |
| `costume` | `image/svg+xml`, `image/png`, `image/jpeg`, `image/gif` |
| `sound` | `audio/wav`, `audio/mp3`, `audio/mpeg`, `audio/ogg` |
| `sprite` | `application/zip`, `application/octet-stream` |
| `script` | `application/json`, `text/json` |

若 `mime` 不在对应白名单内，返回 `400 Bad Request`。

### 2.3 `BackpackItemResponse`

| 字段 | 类型 | 说明 |
| -- | -- | -- |
| `id` | `string (UUID)` | 唯一标识 |
| `type` | `string` | 同 `BackpackItemType` |
| `mime` | `string` | 上传时的 MIME |
| `name` | `string` | 用户自定义名称，<=255 字符 |
| `thumbnail` | `string` | 缩略图相对路径或 OSS 绝对 URL，默认 `.jpg` |
| `body` | `string` | 资源文件路径；`sprite` 会返回 `.dat` |
| `createdAt` | `string (ISO-8601)` | 创建时间（自动转为驼峰） |
| `size` | `integer` | 主体文件字节数 |

列表接口按 `createdAt` 倒序返回。`thumbnail`、`body` 可能是相对路径（如 `backpack/objects/...`），需要前端用 API Host 或 CDN 前缀拼接；若配置了 `OSS_CDN_DOMAIN`，将直接返回完整 URL。

### 2.4 `BackpackItemStats`

| 字段 | 类型 | 说明 |
| -- | -- | -- |
| `costume_count` | `integer` | 造型数量 |
| `sound_count` | `integer` | 声音数量 |
| `sprite_count` | `integer` | 精灵数量 |
| `script_count` | `integer` | 脚本数量 |
| `total_count` | `integer` | 条目总数 |
| `total_size` | `integer` | 全部主体文件大小之和（字节） |

## 3. 接口详解

### 3.1 获取背包列表
- **HTTP**：`GET /api/v1/backpack/{username}`
- **速率限制**：30 次/分钟
- **权限**：需要 `CONTENT_READ_OWN`

#### 查询参数
| 名称 | 类型 | 默认 | 说明 |
| -- | -- | -- | -- |
| `limit` | `int` | `20` | 取值范围 1-50，超出自动裁剪 |
| `offset` | `int` | `0` | >=0 |
| `type` | `string` | 无 | 过滤类型，可传 `costume/sound/sprite/script`

#### 成功响应
```json
{
  "success": true,
  "code": "OK",
  "message": "操作成功",
  "data": [
    {
      "id": "0e3d8b3c-0cf7-4e8e-9a8b-1fd715708d0f",
      "type": "sprite",
      "mime": "application/zip",
      "name": "plane",
      "thumbnail": "backpack/thumbnails/user_20240601_...jpg",
      "body": "backpack/objects/user_20240601_...dat",
      "createdAt": "2024-06-01T12:01:15.123456",
      "size": 48219
    }
  ],
  "timestamp": "2024-06-01T12:02:00.000000"
}
```

#### 典型错误
- `401/403`：未登录或越权访问其他用户。
- `429`：超过每分钟 30 次。
- `500`：服务器内部错误。

### 3.2 创建背包条目
- **HTTP**：`POST /api/v1/backpack/{username}`
- **速率限制**：30 次/分钟
- **权限**：需要 `CONTENT_CREATE_OWN`

#### 请求体
```json
{
  "type": "costume",
  "mime": "image/png",
  "name": "cat",
  "body": "<Base64，无前缀>",
  "thumbnail": "<Base64 JPEG，无前缀>"
}
```

约束：
- Base64 解码后内容不能为空。
- 单文件大小默认 <= `10 MB`（来自配置 `MAX_FILE_SIZE`）。
- 文件上传会去重：重复资源复用旧路径。
- 缩略图需为 JPEG 数据，服务端统一保存 `.jpg`。

#### 成功响应
HTTP 200，`data` 为 `BackpackItemResponse`；字段同列表接口。

#### 典型错误
- `400`：MIME 与类型不匹配、Base64 无效或超出大小限制、UUID/参数非法。
- `401/403`：未登录/越权。
- `429`：超过速率限制。
- `500`：存储或数据库异常。

### 3.3 删除背包条目
- **HTTP**：`DELETE /api/v1/backpack/{username}/{item_id}`
- **速率限制**：30 次/分钟
- **权限**：需要 `CONTENT_DELETE_OWN`

#### 路径参数
| 名称 | 类型 | 说明 |
| -- | -- | -- |
| `item_id` | `string (UUID)` | 待删除条目 ID |

#### 成功响应
`204 No Content`，无响应体（中间件不会包装）。数据库记录删除后会尝试清理 OSS 文件，若文件仍被其他条目引用则保留。

#### 典型错误
- `400`：`item_id` 不是合法 UUID。
- `404`：条目不存在或不属于当前用户。
- `401/403`：未登录/越权。
- `500`：数据库或存储异常。

### 3.4 获取背包统计
- **HTTP**：`GET /api/v1/backpack/{username}/stats`
- **速率限制**：10 次/分钟
- **权限**：需要 `CONTENT_READ_OWN`

#### 成功响应
```json
{
  "success": true,
  "code": "OK",
  "message": "操作成功",
  "data": {
    "costume_count": 12,
    "sound_count": 3,
    "sprite_count": 5,
    "script_count": 1,
    "total_count": 21,
    "total_size": 10485760
  },
  "timestamp": "2024-06-01T12:05:00.000000"
}
```

#### 典型错误
- `401/403`：未登录或越权。
- `429`：超过速率限制。
- `500`：统计查询失败。

## 4. 前端对接建议
- 统一从 `data` 字段读取真实数据，出现错误时关注响应中的 `success=false`、`code` 与 `message`。
- 构造静态资源 URL：
  - 若返回为相对路径 `backpack/...`，使用 `https://<api-host>/{path}` 或配置的 CDN 域拼接；
  - 若返回本身为 `http(s)://`，可直接使用。
- 上传前建议：
  - 将缩略图统一转换为 JPEG；
  - 控制单文件大小，防止 413/400 错误；
  - 避免在 Base64 字符串中携带换行符。
- 对分页加载可通过 `limit + offset` 触底加载，后端按创建时间倒序返回。
- 利用统计接口展示容量、类型占比，或做容量校验（`total_size` 单位为字节）。
- 遇到 `429` 可提示用户稍后重试，或实现指数退避重试策略。

## 5. 环境相关配置提示
- `MAX_FILE_SIZE`（默认 10MB）和 `OSS_CDN_DOMAIN` 等值从运行时配置读取，可在不同环境调整。
- `X-Request-ID` 会自动写入响应头，前端可用于日志关联。
- 如需本地调试，可将 `Authorization` 头中的 token 设置为后台登录后获取的 JWT。
