# 通用事件驱动缓存架构指南 (v2.0)

## 1. 概述

本文档阐述了一套通用的、基于事件驱动的缓存更新架构。该架构旨在解决数据变更与缓存同步的核心问题，通过结合 Cache-Aside 模式、事务性发件箱（Outbox Pattern）、版本控制和**通用基类**，确保系统在高并发场景下的性能、数据一致性和可维护性。

该架构已在**文章 (Article)**、**用户 (User)** 和 **视频 (Video)** 模块成功实施，并可作为未来其他模块的标准化实现方案。

## 2. 核心设计原则

- **单一职责原则 (SRP)**: 每个服务都有其明确、单一的职责。缓存服务只管缓存，聚合服务只管聚合，CRUD服务只管数据库操作。
- **代码复用 (DRY)**: 通过通用的 `BaseCacheService` 抽象出所有缓存服务的公共逻辑，避免在每个模块中重复编写相同的代码。
- **单一缓存写入者**: 对于任何一种缓存资源（如 `article:base:{id}`），只应存在一个权威的写入服务，以避免权限冲突和数据不一致。
- **最终一致性**: 接受数据在极短时间内的不一致性（从数据库更新到缓存更新的毫秒级延迟），以换取API的高性能和系统的解耦。
- **版本控制**: 通过在数据库模型中引入 `cache_version` 字段，解决并发事件处理中可能出现的竞态条件，防止旧数据覆盖新数据。

## 3. 核心组件及其职责

### 3.1. 数据库模型 (e.g., `Article`, `Video`, `User`)

- **必须**包含一个 `cache_version` 字段，用于乐观锁控制。
  ```python
  cache_version = Column(Integer, nullable=False, server_default='1', comment="缓存版本号")
  ```

### 3.2. CRUD 服务 (e.g., `CRUDArticle`)

- **职责**:
    1.  处理核心的数据库 `create`, `update`, `delete` 操作。
    2.  在**同一个数据库事务**中，原子性地**递增 `cache_version`** 并**发布领域事件**到 `Outbox` 表。
- **实现**:
    - 在 `update` 和 `remove` (软删除) 方法中，必须包含 `db_obj.cache_version += 1`。
    - 调用基类 `CRUDBase` 中通用的 `_create_event()` 方法来创建 `OutboxMessage`。

### 3.3. 通用基础缓存服务 (`BaseCacheService`)

- **职责**:
    1.  作为所有实体缓存服务的**通用逻辑抽象**。
    2.  完整实现 **Cache-Aside** 模式，包括缓存的读取、未命中时的回填。
    3.  提供带**版本校验**的缓存写入方法（使用 Lua 脚本）。
    4.  封装所有与 Redis 的直接交互，包括 Hash 操作、空值缓存、批量处理等。
- **实现**:
    - 这是一个泛型类 `BaseCacheService[ModelType, SchemaType, CRUDType]`，通过构造函数接收特定于实体的配置（CRUD 实例、Schema、布隆过滤器等）。

### 3.4. 实体缓存服务 (e.g., `ArticleCacheService`, `UserCacheService`)

- **职责**:
    1.  作为实体基础数据（如 `ArticleBase`）的**唯一、权威的缓存源**。
    2.  继承 `BaseCacheService`，并为其提供特定于实体的配置。
- **实现**:
    - 定义一个继承自 `BaseCacheService` 的子类。
    - 在 `__init__` 方法中，调用 `super().__init__(...)` 并传入该实体的 `crud_model`, `schema_model`, `bloom_filter`, `cache_key_prefix` 等配置。
    - **自身不包含任何缓存逻辑代码**。

### 3.5. 聚合服务 (e.g., `ArticleAggregationService`)

- **职责**:
    1.  作为一个纯粹的**数据消费者和聚合器**。
    2.  **绝不**直接访问数据库来获取其负责聚合的实体基础数据。
    3.  **绝不**执行任何缓存写入或回填操作。
- **实现**:
    - 在需要实体基础数据时，**必须**调用相应的实体缓存服务（如 `article_cache_service.get_entity`）。

### 3.6. 异步任务 (e.g., `task_update_article_cache`)

- **职责**:
    1.  作为由数据库变更事件驱动的**唯一权威的缓存更新者**。
- **实现**:
    - 任务被 `Outbox` 中继任务触发。
    - 从数据库获取最新的实体数据。
    - 调用实体缓存服务的 `set_entity(..., check_version=True)` 方法，执行带版本校验的缓存更新。

## 4. 实施步骤 (以新模块 "Product" 为例)

1.  **模型层**:
    - 在 `models/product.py` 的 `Product` 模型中添加 `cache_version` 字段。
2.  **数据库迁移**:
    - 创建一个新的 Alembic 迁移脚本，为 `products` 表添加 `cache_version` 列。
3.  **CRUD 层**:
    - 在 `crud/product.py` 的 `CRUDProduct` 中：
        - 重写 `update` 和 `remove` 方法，添加 `db_obj.cache_version += 1`。
        - 在 `create`, `update`, `remove` 方法中，调用 `self._create_event()` 发布相应的 `product.*` 事件。
4.  **缓存服务层**:
    - 创建 `services/product_cache_service.py`。
    - 定义 `ProductCacheService` 类，使其**继承** `BaseCacheService`。
    - 在 `__init__` 方法中，调用 `super().__init__` 并传入 `product` 模块的所有特定配置（CRUD 实例, Schema, 布隆过滤器等）。
5.  **异步任务层**:
    - 在 `tasks/cache_tasks.py` (或新的任务文件) 中：
        - 创建 `task_update_product_cache` 和 `task_invalidate_product_cache` 任务。
        - 在 `TASK_MAP` 中添加 `product.*` 事件的路由。
6.  **聚合服务层 (如果需要)**:
    - 创建 `services/product_aggregation_service.py`。
    - 在需要 `ProductBase` 数据时，确保**只调用** `product_cache_service.get_entity`。

遵循以上步骤，即可为任何新模块快速、可靠、且代码极简地实现这套健壮的事件驱动缓存架构。