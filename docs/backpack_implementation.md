# Scratch 背包功能实现说明

## 概述

根据 `docs/backpack-backend-requirements.md` 文档要求，实现了完整的 Scratch 背包功能后端服务，包括背包内容的上传、列表、删除以及静态资源访问。

## 实现的组件

### 1. 数据模型 (Models)

**文件**: `app/models/backpack.py`

- `BackpackItemType`: 背包项目类型枚举（costume/sound/sprite/script）
- `BackpackItem`: 背包项目数据模型，包含所有必要字段

**主要字段**:
- `id`: UUID主键
- `username`: 所属用户名
- `type`: 项目类型
- `mime`: MIME类型
- `name`: 用户自定义名称
- `body_path`: 资源文件路径
- `thumbnail_path`: 缩略图路径
- `size_bytes`: 文件大小
- `metadata`: 预留扩展字段（JSONB）
- `created_at`: 创建时间

### 2. 数据传输对象 (Schemas)

**文件**: `app/schemas/backpack.py`

- `BackpackItemCreate`: 创建背包项目的请求模型
- `BackpackItemResponse`: 背包项目响应模型
- `BackpackItemQuery`: 查询参数模型
- `BackpackItemStats`: 统计信息模型
- `BackpackStorageInfo`: 存储信息模型

**特性**:
- Base64数据验证
- 类型与MIME兼容性验证
- UUID字符串转换
- 字段别名映射（createdAt）

### 3. 数据库操作 (CRUD)

**文件**: `app/crud/backpack.py`

- `create_item`: 创建背包项目
- `get_items_by_username`: 获取用户背包列表
- `get_item_by_id`: 根据ID获取项目
- `delete_item`: 删除背包项目
- `count_items_by_username`: 统计项目数量
- `get_user_storage_stats`: 获取存储统计

**特性**:
- 异步数据库操作
- 分页支持
- 类型筛选
- 权限验证（用户只能访问自己的背包）

### 4. 存储服务 (Storage)

**文件**: `app/services/backpack_storage.py`

- Base64解码和验证
- 文件大小限制检查
- 文件扩展名处理
- **阿里云OSS存储**
- 文件删除管理
- CDN URL生成

**OSS存储结构**:
```
OSS Bucket/
└── backpack/
    ├── objects/          # 主体文件
    │   └── username_20250922_uuid.dat
    └── thumbnails/       # 缩略图
        └── username_20250922_uuid.jpg
```

**OSS特性**:
- 使用阿里云OSS V4签名认证
- 支持环境变量配置认证信息
- 自动处理文件上传和删除
- 支持CDN加速访问
- **文件去重**: 基于SHA256哈希值自动去重，避免重复存储
- **引用计数**: 智能删除，只有当文件不被其他项目使用时才真正删除
- **缓存优化**: Redis缓存文件哈希映射，提升去重检查性能

### 5. API端点 (Endpoints)

**文件**: `app/api/endpoints/backpack.py`

实现了文档要求的三个核心接口：

#### GET /{username}
- 获取用户背包项目列表
- 支持分页（limit/offset）
- 支持类型筛选
- 返回符合文档格式的JSON数组

#### POST /{username}
- 创建新的背包项目
- Base64数据处理
- 文件存储和数据库记录
- 返回创建的项目信息

#### DELETE /{username}/{itemId}
- 删除指定背包项目
- 同时删除文件和数据库记录
- 权限验证

#### GET /{username}/stats
- 获取用户背包统计信息
- 按类型分组统计
- 总数量和大小统计

### 6. 数据库迁移

**文件**: `alembic/versions/add_backpack_items_table.py`

- 创建 `backpack_items` 表
- 创建必要的索引
- 创建枚举类型

### 7. 配置更新

**文件**: `app/config/storage_config.py`

添加了本地文件存储配置：
- `UPLOAD_DIR`: 上传目录路径
- `MAX_FILE_SIZE`: 最大文件大小限制

## API 使用示例

### 1. 获取背包列表

```bash
GET /api/backpack/testuser?limit=20&offset=0
Headers: x-token: <jwt_token>

Response:
[
  {
    "id": "uuid-string",
    "type": "costume",
    "name": "My Sprite",
    "mime": "image/png",
    "thumbnail": "thumbnails/uuid.jpg",
    "body": "objects/uuid.dat",
    "createdAt": "2024-05-01T00:00:00Z",
    "size": 12345
  }
]
```

### 2. 创建背包项目

```bash
POST /api/backpack/testuser
Headers: x-token: <jwt_token>
Content-Type: application/json

{
  "type": "sprite",
  "mime": "image/png",
  "name": "My Sprite",
  "body": "<Base64字符串>",
  "thumbnail": "<Base64 JPEG>"
}
```

### 3. 删除背包项目

```bash
DELETE /api/backpack/testuser/uuid-string
Headers: x-token: <jwt_token>

Response: 204 No Content
```

## 安全特性

1. **认证验证**: 所有接口都需要有效的JWT token
2. **权限控制**: 用户只能访问自己的背包
3. **文件大小限制**: 默认10MB限制
4. **MIME类型验证**: 确保文件类型与项目类型匹配
5. **Base64验证**: 确保上传数据的有效性

## 文件去重机制

### 去重原理
1. **哈希计算**: 对上传的文件计算SHA256哈希值
2. **缓存检查**: 首先检查Redis缓存中是否存在相同哈希的文件
3. **数据库检查**: 如果缓存未命中，查询file_hashes表
4. **秒传**: 如果文件已存在，直接返回已有文件路径
5. **新文件上传**: 只有当文件不存在时才真正上传到OSS

### 引用计数删除
1. **删除检查**: 删除背包项目时，检查文件是否被其他项目使用
2. **安全删除**: 只有当引用计数为0时才从OSS删除文件
3. **数据一致性**: 同时清理file_hashes表中的记录

### 性能优化
- **Redis缓存**: 文件哈希映射缓存7天，减少数据库查询
- **批量操作**: 支持大文件分片上传
- **异步删除**: 文件删除操作不阻塞API响应

## 扩展性设计

1. **元数据字段**: 使用JSONB存储扩展信息
2. **存储抽象**: 复用PartialUpload服务，支持多种存储后端
3. **配额管理**: 预留了配额检查接口
4. **版本支持**: 数据结构支持后续版本化需求
5. **去重机制**: 大幅节省存储空间和带宽成本

## 部署注意事项

### 1. 阿里云OSS配置

**环境变量设置**:
```bash
# 阿里云认证信息
export ALIBABA_CLOUD_ACCESS_KEY_ID="your-access-key-id"
export ALIBABA_CLOUD_ACCESS_KEY_SECRET="your-access-key-secret"

# OSS配置
export OSS_ENDPOINT="https://oss-cn-hangzhou.aliyuncs.com"
export OSS_REGION="cn-hangzhou"
export OSS_BUCKET_NAME="your-bucket-name"
export OSS_CDN_DOMAIN="https://your-cdn-domain.com"
```

**OSS Bucket设置**:
1. 创建OSS Bucket
2. 设置Bucket为私有读写（通过CDN访问）
3. 配置CORS规则允许前端访问
4. 设置CDN加速域名

### 2. 应用配置

1. 配置文件大小限制（MAX_FILE_SIZE）
2. 运行数据库迁移创建表结构
3. 确保OSS认证信息正确配置

## 测试

**文件**: `tests/test_backpack.py`

包含了完整的单元测试：
- 模型测试
- Schema验证测试
- 存储服务测试
- 集成测试

运行测试：
```bash
pytest tests/test_backpack.py -v
```

## 与前端集成

前端需要：
1. 设置 `backpackHost` 指向后端服务
2. 在请求头中包含 `x-token`
3. 使用返回的 `thumbnail` 和 `body` 路径拼接完整URL
4. 处理Base64编码的文件上传

## 性能优化建议

1. 为大文件上传实现分片上传
2. 添加文件缓存策略
3. 实现异步文件删除
4. 添加文件去重机制
5. 使用CDN加速静态资源访问
