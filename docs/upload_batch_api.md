# 批量上传接口文档

基于代码分析，以下是两个接口的详细文档：批量上传 (`/api/upload/batch`) 和批量状态查询 (`/api/upload/status/batch`)。文档包括接口描述、请求/响应格式、示例和错误处理。所有接口需要用户认证（通过Depends(get_current_active_user)）。

## 1. 批量上传接口 (POST /api/upload/batch)

### 接口描述
- **方法**：POST
- **路径**：`/api/upload/batch`
- **功能**：批量上传视频文件到指定文件夹，支持秒传（基于文件哈希检查）、异步处理（使用Celery任务）和视频质量选择。文件大小限制为5GB（通过check_file_size依赖项检查）。上传后创建视频记录，并启动后台任务进行处理。返回任务ID列表，前端可通过状态查询接口跟踪进度。
- **依赖**：
  - 用户认证：需要活跃用户（get_current_active_user）。
  - 文件大小检查：Content-Length 不能超过5GB。
  - 数据库会话：AsyncSession。
  - 服务：TaskStatusService（任务状态）、VideoFolderService（视频文件夹）。
- **逻辑流程**：
  1. 验证文件列表不为空，folder_id有效且属于当前用户。
  2. 为每个文件创建视频记录（使用VideoCreate schema）。
  3. 保存文件到临时目录，计算哈希。
  4. 检查是否秒传（哈希已存在）。
  5. 如果秒传：链接到现有文件，删除临时文件。
  6. 否则：启动Celery任务（process_batch_video_and_update_db 或 link_video_to_existing_file）。
  7. 更新任务状态为UPLOADING (progress=0.05)。
  8. 返回任务ID列表。

### 请求参数
- **files** (必填，multipart/form-data)：文件列表（list[UploadFile]），仅支持视频文件。每个文件包括filename。
- **folder_id** (必填，query参数)：目标文件夹ID (int)，必须属于当前用户。
- **video_quality** (可选，form参数)：视频质量枚举 (VideoQuality)，默认 "medium"。选项：low、medium、high。

### 响应格式 (BatchUploadResponse)
使用Pydantic BaseModel：
```python
class TaskInfo(BaseModel):
    fileName: str  # 文件名
    taskId: str    # Celery任务ID

class BatchUploadResponseData(BaseModel):
    tasks: list[TaskInfo]  # 任务列表

class BatchUploadResponse(BaseModel):
    data: BatchUploadResponseData
```

**示例响应** (JSON)：
```json
{
  "data": {
    "tasks": [
      {
        "fileName": "video1.mp4",
        "taskId": "celery-task-uuid-123"
      },
      {
        "fileName": "video2.mp4",
        "taskId": "celery-task-uuid-456"
      }
    ]
  }
}
```

### 错误处理
- **400 Bad Request**：
  - "No files provided"：文件列表为空。
  - "Folder ID is required"：folder_id缺失。
  - "Invalid folder ID"：文件夹不存在或不属于用户。
  - "No valid files to process"：无有效文件。
- **411 Length Required**：Content-Length头缺失。
- **413 Request Entity Too Large**：文件总大小超过5GB。
- **401 Unauthorized**：用户未认证。
- **其他**：500 Internal Server Error（后台任务启动失败）。

### 示例 (Curl)
```bash
curl -X POST "http://localhost:8000/api/upload/batch?folder_id=1&video_quality=medium" \
  -H "Authorization: Bearer <your-jwt-token>" \
  -F "files=@/path/to/video1.mp4" \
  -F "files=@/path/to/video2.mp4"
```

## 2. 批量状态查询接口 (POST /api/upload/status/batch)

### 接口描述
- **方法**：POST
- **路径**：`/api/upload/status/batch`
- **功能**：根据任务ID列表批量查询上传任务状态。支持空列表（返回空数组）。状态包括进度、元数据和消息。
- **依赖**：
  - 服务：TaskStatusService（获取状态）。
- **逻辑流程**：
  1. 如果task_ids为空，返回空任务列表。
  2. 调用TaskStatusService.get_statuses(task_ids) 获取状态。
  3. 返回TaskStatus列表。

### 请求参数
- **status_request** (必填，JSON body)：BatchStatusRequest schema。
```python
class BatchStatusRequest(BaseModel):
    task_ids: list[str]  # 任务ID列表
```

**示例请求** (JSON)：
```json
{
  "task_ids": ["celery-task-uuid-123", "celery-task-uuid-456"]
}
```

### 响应格式 (BatchStatusResponse)
使用Pydantic BaseModel：
```python
class UploadStatus(str, Enum):  # 状态枚举
    IDLE = "idle"
    FILE_SELECTED = "file_selected"
    FILE_UPLOAD_STARTED = "file_upload_started"
    UPLOADING = "uploading"
    PROCESSING = "processing"
    UPLOAD_SUCCESS = "upload_success"
    UPLOAD_ERROR = "upload_error"
    CREATING = "creating"
    CREATE_ERROR = "create_error"
    COMPLETE = "complete"

class TaskStatus(BaseModel):
    taskId: str           # 任务ID
    status: UploadStatus  # 当前状态
    progress: float | None  # 进度 (0.0-1.0)
    file_metadata: dict | None  # 文件元数据
    message: str | None   # 错误消息或描述

class BatchStatusResponse(BaseModel):
    tasks: list[TaskStatus]  # 状态列表
```

**示例响应** (JSON)：
```json
{
  "tasks": [
    {
      "taskId": "celery-task-uuid-123",
      "status": "uploading",
      "progress": 0.05,
      "file_metadata": null,
      "message": null
    },
    {
      "taskId": "celery-task-uuid-456",
      "status": "processing",
      "progress": 0.5,
      "file_metadata": {"duration": 120, "width": 1920, "height": 1080},
      "message": "视频转码中"
    }
  ]
}
```

### 错误处理
- **400 Bad Request**：task_ids无效（但代码中未显式抛出）。
- **401 Unauthorized**：用户未认证（继承自依赖）。
- **其他**：500 Internal Server Error（服务查询失败）。

### 示例 (Curl)
```bash
curl -X POST "http://localhost:8000/api/upload/status/batch" \
  -H "Authorization: Bearer <your-jwt-token>" \
  -H "Content-Type: application/json" \
  -d '{"task_ids": ["celery-task-uuid-123", "celery-task-uuid-456"]}'
```

此文档基于当前代码实现。如有更新，请重新生成。