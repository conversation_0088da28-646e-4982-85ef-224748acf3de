# User Stats Population Mechanism

This document outlines how the `user_stats` data is populated and updated within the application. There are two primary mechanisms: **direct invocation** and **event-driven updates via the Outbox Pattern**.

## 1. Initialization and Batch Backfilling

### Initial Record Creation
When a new user is created, an initial `user_stats` record is created with all values set to zero. This is handled by the `get_or_create` method in `CRUDUserStats`, which is called from services like `UserManagementService`.

- **Relevant File:** [`app/crud/crud_user_stats.py`](app/crud/crud_user_stats.py)
- **Relevant Service:** [`app/services/user_management_service.py`](app/services/user_management_service.py)

### Batch Backfilling
For existing users or for data correction, the `backfill_user_stats.py` script can be executed. This script calculates all user statistics from scratch and populates the `user_stats` table.

- **Relevant File:** [`app/scripts/backfill_user_stats.py`](app/scripts/backfill_user_stats.py)

## 2. Real-time / Incremental Updates

### Direct Invocation
For certain immediate and critical operations, the statistics are updated directly in a synchronous manner. A good example is updating the `article_count` when a user creates or deletes an article. This is done by directly calling the `increment` method.

- **Example:** In [`app/api/endpoints/articles.py`](app/api/endpoints/articles.py), the `article_count` is incremented or decremented upon article creation/deletion.
- **Core Method:** The `increment` method in [`app/crud/crud_user_stats.py`](app/crud/crud_user_stats.py) handles the atomic database update and cache invalidation.

### Event-Driven Updates (Outbox Pattern)
For actions that can tolerate a slight delay and benefit from decoupling (like, follow, favorite), the system uses the Outbox Pattern to ensure reliable, asynchronous updates.

The process is as follows:
1.  **Event Creation:** When a user performs an action (e.g., likes a post), a new `OutboxMessage` is created in the same database transaction as the primary action. The `topic` of this message is set to `"stats.update"`.
    - **Relevant Files:**
        - [`app/crud/like.py`](app/crud/like.py)
        - [`app/crud/favorite.py`](app/crud/favorite.py)
        - [`app/crud/user.py`](app/crud/user.py) (for follow/unfollow)