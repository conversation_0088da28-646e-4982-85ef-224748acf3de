# Redis 架构文档：主从读写分离模式

## 1. 模式概述

为了提升应用的性能和可扩展性，本项目的 Redis 基础设施采用主从（Master/Slave）读写分离架构。该模式的核心思想是将数据操作分为两类：

-   **写操作 (Write Operations)**: 所有的数据创建、修改和删除操作（如 `SET`, `HSET`, `SADD`, `ZADD`, `DEL`）都只发送到 **主节点 (Master)**。主节点负责保证数据的一致性，并将其更改同步到一个或多个从节点。
-   **读操作 (Read Operations)**: 所有的数 据查询操作（如 `GET`, `HGET`, `SMEMBERS`, `ZRANGE`）都发送到 **从节点 (Slave)**。通过将读请求分散到多个从节点，可以显著降低主节点的负载，从而提高应用的整体读取性能和吞吐量。

此架构在 `Sentinel` 模式下能够实现高可用性，当主节点故障时，Sentinel 会自动将一个从节点提升为新的主节点。在 `Standalone` 或 `Cluster` 模式下，系统会优雅地降级，将所有读写操作都指向唯一的节点，从而保证了代码的灵活性。

## 2. 核心服务层 (`app/db/redis.py`)

为了在整个应用中统一并强制执行读写分离模式，我们抽象出了一个中心化的 Redis 服务层：[`app/db/redis.py`](app/db/redis.py)。

**设计原则**:

-   **禁止直接访问**: 应用中的任何其他模块（服务、任务、端点等）都 **不应** 直接调用 `get_redis_master()` 或 `get_redis_slave()`。
-   **统一入口**: 所有的 Redis 交互都 **必须** 通过 `app/db/redis.py` 中提供的服务函数进行。
-   **封装实现**: 服务函数内部封装了选择主节点（用于写）或从节点（用于读）的逻辑，对调用者透明。
-   **原子性保证**: 对于需要多个 Redis 命令组合的原子操作（如 `INCR` + `EXPIRE`），服务层使用 `pipeline` 提供了原子性的函数接口。

## 3. 函数参考

以下是当前服务层支持的所有 Redis 操作。

### 键 (Keys)

| 函数名 | 操作类型 | Redis 命令 | 描述 |
| --- | --- | --- | --- |
| `set_key` | **写** | `SET`, `SETEX` | 设置一个键的值，并可选地设置过期时间。 |
| `get_key` | **读** | `GET` | 获取一个键的值。 |
| `delete_key` | **写** | `DEL` | 删除一个或多个键。 |
| `key_exists` | **读** | `EXISTS` | 检查一个键是否存在。 |
| `get_key_ttl` | **读** | `TTL` | 获取一个键的剩余生存时间。 |
| `set_key_expire` | **写** | `EXPIRE` | 为一个键设置过期时间。 |
| `get_and_delete_key` | **写** | `GETDEL` | 获取并删除一个键。 |
| `scan_keys` | **读** | `SCAN` | 安全地迭代匹配模式的键。 |

### 字符串 (Strings)

| 函数名 | 操作类型 | Redis 命令 | 描述 |
| --- | --- | --- | --- |
| `increment_key` | **写** | `INCR`, `EXPIRE` | 原子性地将键的值加一，并可选地设置过期时间。 |
| `increment_key_by` | **写** | `INCRBY`, `EXPIRE` | 原子性地将键的值增加指定整数，并可选地设置过期时间。 |
| `decrement_key` | **写** | `DECR`, `EXPIRE` | 原子性地将键的值减一，并可选地设置过期时间。 |

### 哈希 (Hashes)

| 函数名 | 操作类型 | Redis 命令 | 描述 |
| --- | --- | --- | --- |
| `hash_set` | **写** | `HSET` | 设置哈希表中的字段值。 |
| `hash_set_with_expire` | **写** | `HSET`, `EXPIRE` | 原子性地设置哈希字段并为整个哈希键设置过期时间。 |
| `hash_get` | **读** | `HGET` | 获取哈希表中的字段值。 |
| `hash_get_many` | **读** | `HMGET` | 获取哈希表中多个字段的值。 |
| `hash_get_all` | **读** | `HGETALL` | 获取哈希表中所有字段和值。 |

### 集合 (Sets)

| 函数名 | 操作类型 | Redis 命令 | 描述 |
| --- | --- | --- | --- |
| `set_add` | **写** | `SADD` | 向集合添加一个或多个成员。 |
| `set_remove` | **写** | `SREM` | 从集合中移除一个或多个成员。 |
| `set_is_member` | **读** | `SISMEMBER` | 判断成员是否是集合的成员。 |
| `set_cardinality` | **读** | `SCARD` | 获取集合的成员数。 |
| `set_get_all_members` | **读** | `SMEMBERS` | 获取集合中的所有成员。 |

### 有序集合 (Sorted Sets)

| 函数名 | 操作类型 | Redis 命令 | 描述 |
| --- | --- | --- | --- |
| `sorted_set_add` | **写** | `ZADD` | 向有序集合添加一个或多个成员。 |
| `sorted_set_remove` | **写** | `ZREM` | 从有序集合中移除一个或多个成员。 |
| `sorted_set_increment_by` | **写** | `ZINCRBY` | 为有序集合中成员的分数加上增量。 |
| `sorted_set_get_score` | **读** | `ZSCORE` | 获取有序集合中成员的分数。 |
| `sorted_set_get_range` | **读** | `ZRANGE`, `ZREVRANGE` | 按排名区间获取有序集合的成员。 |
| `sorted_set_remove_range_by_rank` | **写** | `ZREMRANGEBYRANK` | 移除有序集合中排名区间的成员。 |
| `sorted_set_union_store` | **写** | `ZUNIONSTORE` | 计算并存储多个有序集合的并集。 |
| `sorted_set_inter_store` | **写** | `ZINTERSTORE` | 计算并存储多个有序集合的交集。 |

### 布隆过滤器 (Bloom Filters)

| 函数名 | 操作类型 | Redis 命令 | 描述 |
| --- | --- | --- | --- |
| `bloom_filter_reserve` | **写** | `BF.RESERVE` | 创建并保留一个布隆过滤器。 |
| `bloom_filter_add` | **写** | `BF.ADD` | 向布隆过滤器添加一个元素。 |
| `bloom_filter_exists` | **读** | `BF.EXISTS` | 检查元素是否存在于布隆过滤器中。 |
| `bloom_filter_madd` | **写** | `BF.MADD` | 批量向布隆过滤器添加元素。 |

### Pipeline

| 函数名 | 操作类型 | Redis 命令 | 描述 |
| --- | --- | --- | --- |
| `execute_pipeline` | **读/写** | (多种) | 执行一个包含多个命令的原子事务。 |

## 4. 使用指南

### 错误示例 (禁止)

```python
# a_service.py
from app.db.redis import get_redis_master # 禁止导入

class SomeService:
    async def do_something(self):
        redis = await get_redis_master() # 禁止直接调用
        await redis.set("my_key", "my_value")
```

### 正确示例

```python
# a_service.py
from app.db.redis import set_key, get_key # 正确：只导入需要的服务函数

class SomeService:
    async def do_something(self):
        # 写操作
        await set_key("my_key", "my_value", expire=3600)
        
        # 读操作
        value = await get_key("my_key")
```

**最佳实践**:

-   **最小导入**: 只从 `app.db.redis` 导入你需要的特定函数。
-   **明确意图**: 通过选择正确的函数（如 `increment_key` 而不是 `get` + `set`），使代码意图更加清晰。
-   **信任服务层**: 无需在业务逻辑中关心连接的是主节点还是从节点，服务层会自动处理。