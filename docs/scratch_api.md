# Scratch API 文档

## 概述

Scratch API 是一个用于管理Scratch项目（编程教育项目）的RESTful API，支持项目的创建、更新、删除、搜索和改编（remix/copy）功能。该API基于FastAPI框架构建，支持异步操作、权限控制和分页响应。主要用于前端应用接入，实现Scratch项目的在线编辑、分享和社区互动。

### 基础信息
- **Base URL**: `/api/v1/scratch` (假设API路由挂载在`/api/v1`下)
- **认证**: 使用JWT Token，通过`Authorization: Bearer <token>`头传递。依赖`app.api.deps.get_current_user`获取当前用户。
- **权限系统**: 使用`require_permission`装饰器控制访问，支持`Permissions.CONTENT_CREATE_OWN`、`Permissions.CONTENT_READ_PUBLIC`等。资源类型为`ResourceType.SCRATCH_PROJECT`。
- **响应格式**: JSON，使用Pydantic模型序列化。
- **错误处理**: 标准HTTP状态码，如404 (项目不存在)、403 (权限不足)、422 (验证失败)。

### 认证流程
1. 用户通过`/api/token`端点获取JWT Token（使用用户名/密码或微信登录）。
2. 在后续请求中添加`Authorization: Bearer <token>`。
3. API会自动验证Token并注入`current_user`依赖。
4. 对于公开项目，游客（无Token）可使用`get_current_user_optional`访问。

**示例 (JavaScript Fetch)**:
```javascript
const token = 'your-jwt-token';
const response = await fetch('/api/v1/scratch', {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

## 端点详情

### 1. 创建Scratch项目
- **方法**: POST
- **路径**: `/create`
- **描述**: 创建新项目，支持原创、复制(is_copy=1)或重混(is_remix=1)。请求体为JSON代码对象。
- **权限**: `Permissions.CONTENT_CREATE_OWN`
- **参数**:
  - `original_id` (Query, int, optional): 原项目ID (复制或重混时必填)
  - `is_copy` (Query, int, optional): 1表示复制
  - `is_remix` (Query, int, optional): 1表示重混
  - `title` (Query, str, required): 项目标题
- **请求体**: JSON (code: dict) - 项目代码
- **响应**: 200 OK, `{"project_id": int}`
- **示例请求**:
  ```javascript
  fetch('/api/v1/scratch/create?title=My Project&is_remix=1&original_id=123', {
    method: 'POST',
    headers: { 'Authorization': 'Bearer <token>', 'Content-Type': 'application/json' },
    body: JSON.stringify({ sprites: [...], stage: {...} })
  });
  ```
- **错误码**:
  - 400: 缺少original_id (复制/重混)
  - 403: 不能复制他人项目或原项目不允许改编
  - 422: 无效JSON

### 2. 获取Scratch项目详情
- **方法**: GET
- **路径**: `/{project_id}`
- **描述**: 获取单个项目详情，包括作者、改编关系和统计。
- **权限**: `Permissions.CONTENT_READ_PUBLIC` (公开项目游客可访问)
- **参数**:
  - `project_id` (Path, int, required): 项目ID
- **响应**: 200 OK, [ScratchProductOut](schemas/scratch.py)
- **示例响应**:
  ```json
  {
    "project_id": 123,
    "title": "My Project",
    "code": {...},
    "author": {"id": 1, "username": "user"},
    "adaptation_info": {"adapt_level": 1, "adaptation_type": "remix"},
    "stats": {"visit_count": 100, "adapt_count": 5}
  }
  ```
- **错误码**:
  - 404: 项目不存在

### 3. 更新Scratch项目 (代码和标题)
- **方法**: PUT
- **路径**: `/{project_id}`
- **描述**: 更新项目标题和代码。
- **权限**: `Permissions.CONTENT_UPDATE_OWN`
- **参数**:
  - `project_id` (Path, int, required)
  - `title` (Query, str, required)
- **请求体**: JSON (code: dict)
- **响应**: 200 OK, `{"project_id": int}`
- **示例**: 类似创建，但使用PUT方法。

### 4. 更新Scratch项目详情
- **方法**: PUT
- **路径**: `/{project_id}/detail`
- **描述**: 更新描述、封面等详情字段。
- **权限**: `Permissions.CONTENT_UPDATE_OWN`
- **参数**:
  - `project_id` (Path, int, required)
- **请求体**: [ScratchProductUpdate](schemas/scratch.py)
- **响应**: 200 OK, `{"project_id": int}`

### 5. 删除Scratch项目
- **方法**: DELETE
- **路径**: `/{project_id}`
- **描述**: 删除项目，检查无改编子项目。
- **权限**: `Permissions.CONTENT_DELETE_OWN`
- **参数**:
  - `project_id` (Path, int, required)
- **响应**: 204 No Content
- **错误码**:
  - 400: 有子改编项目

### 6. 获取Scratch项目列表 (分页)
- **方法**: GET
- **路径**: `/`
- **描述**: 分页获取项目，支持筛选。
- **权限**: 公开访问
- **参数**:
  - `cursor` (Query, str, optional): 游标分页
  - `limit` (Query, int, default=20): 每页数量
  - `difficulty_level` (Query, int, 1-5): 难度筛选
  - `category` (Query, str): 分类
  - `author_id` (Query, int): 作者ID
  - `adaptation_type` (Query, enum: original/remix/copy): 改编类型
- **响应**: 200 OK, [CursorPaginationResponse[ScratchProductOut]](core/pagination.py)
- **示例**: GET `/scratch?limit=10&difficulty_level=2`

### 7. 批量获取项目
- **方法**: POST
- **路径**: `/multiple`
- **描述**: 批量获取多个项目详情，支持顺序保持。
- **权限**: 公开访问
- **参数**:
  - `preserve_order` (Query, bool, default=true)
  - `include_adaptations` (Query, bool, default=false)
- **请求体**: `{"projectIds": [1,2,3]}`
- **响应**: 200 OK, list[ScratchProductOut]
- **错误码**:
  - 400: 超过100个ID

### 8. 获取用户项目列表
- **方法**: GET
- **路径**: `/user/{user_id}/projects`
- **描述**: 获取指定用户项目，分页。
- **权限**: 公开或作者访问
- **参数**:
  - `user_id` (Path, int, required)
  - `include_adaptations` (Query, bool)
  - 分页参数
- **响应**: 200 OK, CursorPaginationResponse[ScratchProductOut]

### 9. 搜索项目
- **方法**: GET
- **路径**: `/search`
- **描述**: 关键词搜索项目。
- **权限**: 公开访问
- **参数**:
  - `q` (Query, str, required): 搜索词
  - 筛选和分页参数
- **响应**: 200 OK, CursorPaginationResponse[ScratchProductOut]

### 10. 获取改编历史
- **方法**: GET
- **路径**: `/{project_id}/adaptation-history`
- **描述**: 获取项目改编历史。
- **权限**: 公开访问
- **参数**:
  - `project_id` (Path, int, required)
  - `limit` (Query, int, default=50)
- **响应**: 200 OK, list[dict] (包含时间、作者、类型)

## 前端接入指南

### 认证与权限
- 使用Token认证所有写操作。检查响应头`X-Permissions`以了解用户权限。
- 处理403错误：重定向登录或显示"无权限"。
- 改编检查：创建前验证`can_adapt`字段。

### 处理改编链
- 使用`adaptation_info`字段显示"Remixed from ..."链接。
- 获取完整链：调用`/scratch/{id}/adaptation-history`或服务层构建。
- 示例 (Vue.js):
  ```javascript
  if (project.adaptation_type === 'remix') {
    // 显示改编链接
    navigateTo(`/scratch/${project.original_project_id}`);
  }
  ```

### 分页处理
- 使用`CursorPaginationResponse`: `has_next`、`next_cursor`、`data`。
- 示例 (无限滚动):
  ```javascript
  let cursor = null;
  async function loadMore() {
    const res = await fetch(`/scratch?cursor=${cursor}&limit=20`);
    const { data, next_cursor } = await res.json();
    // 追加data到列表
    cursor = next_cursor;
  }
  ```

### 错误处理
- 全局捕获HTTP错误，显示用户友好消息。
- 常见错误：404重定向首页，422验证表单。

## 改编流程图

```mermaid
graph TD
    A[原创项目创建] --> B{允许改编?}
    B -->|是| C[用户发起重混/复制]
    B -->|否| D[拒绝改编]
    C --> E[验证权限 & 层级]
    E -->|通过| F[创建新项目<br/>设置original_project_id<br/>adapt_level +1]
    E -->|失败| G[返回错误]
    F --> H[更新原项目adapt_count]
    H --> I[返回新项目ID]
    style A fill:#90EE90
    style F fill:#ADD8E6
```

此文档基于当前API实现生成，如有更新请参考OpenAPI自动生成。