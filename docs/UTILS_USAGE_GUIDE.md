# 工具类使用指南

本指南介绍如何在项目中使用新创建的时间管理和手机号处理工具类。

## 目录

1. [时间工具类 (TimeUtils)](#时间工具类-timeutils)
2. [JWT时间辅助类 (JWTTimeHelper)](#jwt时间辅助类-jwttimehelper)
3. [手机号工具类 (PhoneUtils)](#手机号工具类-phoneutils)
4. [最佳实践](#最佳实践)
5. [常见用例](#常见用例)
6. [注意事项](#注意事项)

## 时间工具类 (TimeUtils)

### 导入方式

```python
from app.utils.time_utils import TimeUtils, JWTTimeHelper
# 或者使用便捷函数
from app.utils.time_utils import utc_now, local_now, calculate_expiry, is_expired
```

### 基本用法

#### 1. 获取当前时间

```python
# 获取UTC时间（推荐用于数据库存储和JWT）
current_utc = TimeUtils.get_utc_now()

# 获取本地时间（用于用户界面显示）
current_local = TimeUtils.get_local_now()

# 便捷函数方式
current_utc = utc_now()
current_local = local_now()
```

#### 2. 时区转换

```python
import datetime

# 转换为UTC时间
local_time = datetime.datetime.now()
utc_time = TimeUtils.to_utc(local_time)

# 转换为本地时间（用于显示）
display_time = TimeUtils.to_local(utc_time)
```

#### 3. 计算过期时间

```python
# 计算30分钟后的过期时间
expiry_time = TimeUtils.calculate_expiry(30)

# 基于特定时间计算过期时间
base_time = TimeUtils.get_utc_now()
expiry_time = TimeUtils.calculate_expiry(15, base_time)

# 按秒计算
expiry_time = TimeUtils.calculate_expiry_seconds(300)  # 5分钟

# 便捷函数方式
expiry_time = calculate_expiry(30)
```

#### 4. 检查是否过期

```python
# 检查时间是否已过期
expiry_time = TimeUtils.calculate_expiry(30)
if TimeUtils.is_expired(expiry_time):
    print("已过期")
else:
    print("仍然有效")

# 便捷函数方式
if is_expired(expiry_time):
    print("已过期")
```

#### 5. 时间格式化

```python
current_time = TimeUtils.get_utc_now()

# 格式化用于显示（自动转换为本地时间）
display_str = TimeUtils.format_display(current_time)
# 结果：2025-08-25 18:30:15

# 自定义格式
custom_format = TimeUtils.format_display(current_time, "%Y年%m月%d日 %H:%M")
# 结果：2025年08月25日 18:30

# 格式化用于JWT
jwt_format = TimeUtils.format_for_jwt(current_time)
# 结果：2025-08-25T10:30:15.123456+00:00
```

#### 6. 时间差计算

```python
start_time = TimeUtils.get_utc_now()
end_time = TimeUtils.calculate_expiry(30, start_time)

# 计算分钟差
minutes_diff = TimeUtils.get_time_diff_minutes(start_time, end_time)
# 结果：30

# 计算秒差
seconds_diff = TimeUtils.get_time_diff_seconds(start_time, end_time)
# 结果：1800
```

## JWT时间辅助类 (JWTTimeHelper)

### 基本用法

#### 1. 创建JWT时间字段

```python
# 创建30分钟过期的JWT时间字段
jwt_time_fields = JWTTimeHelper.create_jwt_payload_time(30)

# 结果
{
    "iat": 1724550546.876603,  # 签发时间戳
    "exp": 1724552346.876603,  # 过期时间戳
    "issued_at": "2025-08-25T10:30:15.123456+00:00"  # ISO格式签发时间
}

# 基于特定时间创建
issued_at = TimeUtils.get_utc_now()
jwt_time_fields = JWTTimeHelper.create_jwt_payload_time(15, issued_at)
```

#### 2. 验证JWT时间

```python
# 验证JWT载荷中的时间字段
payload = {
    "user_id": 12345,
    "exp": 1724552346.876603,
    "iat": 1724550546.876603
}

time_valid, error_message = JWTTimeHelper.validate_jwt_time(payload)
if time_valid:
    print("时间有效")
else:
    print(f"时间无效: {error_message}")
```

### 在服务中的使用示例

```python
from jose import jwt
from app.utils.time_utils import JWTTimeHelper

# 在设备验证服务中生成令牌
def generate_device_verification_token(user_id: int, phone: str):
    # 创建JWT时间字段（30分钟过期）
    jwt_time_fields = JWTTimeHelper.create_jwt_payload_time(30)
    
    payload = {
        "sub": str(user_id),
        "user_id": user_id,
        "phone_number": phone,
        "type": "device_verification",
        **jwt_time_fields  # 展开时间字段
    }
    
    return jwt.encode(payload, SECRET_KEY, algorithm="HS256")

# 验证令牌
def verify_device_token(token: str):
    try:
        # 解码JWT
        payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        
        # 验证时间有效性
        time_valid, time_error = JWTTimeHelper.validate_jwt_time(payload)
        if not time_valid:
            raise TokenExpiredError(f"令牌时间无效: {time_error}")
        
        return payload
    except jwt.ExpiredSignatureError:
        raise TokenExpiredError("令牌已过期")
```

## 手机号工具类 (PhoneUtils)

### 导入方式

```python
from app.utils.phone_utils import PhoneUtils
# 或者使用便捷函数
from app.utils.phone_utils import (
    validate_and_normalize_phone, 
    mask_phone_for_display, 
    resolve_phone_number
)
```

### 基本用法

#### 1. 验证手机号

```python
# 验证手机号格式
phone = "17604840253"
is_valid = PhoneUtils.validate_phone(phone)
# 结果：True

# 各种格式都能正确验证
test_phones = [
    "17604840253",      # True
    "176-0484-0253",    # True
    "176 0484 0253",    # True  
    "+86 17604840253",  # True
    "176****0253",      # False (掩码格式)
    "12345678901"       # False (无效格式)
]
```

#### 2. 标准化手机号

```python
# 标准化各种格式的手机号
phone1 = PhoneUtils.normalize_phone("176-0484-0253")
phone2 = PhoneUtils.normalize_phone("176 0484 0253")
phone3 = PhoneUtils.normalize_phone("+86 17604840253")
# 结果都是："17604840253"

# 无效格式返回None
invalid = PhoneUtils.normalize_phone("123456")
# 结果：None
```

#### 3. 生成掩码手机号

```python
# 生成掩码用于前端显示
phone = "17604840253"
masked = PhoneUtils.mask_phone(phone)
# 结果："176****0253"

# 自定义掩码字符
masked_custom = PhoneUtils.mask_phone(phone, mask_char="x")
# 结果："176xxxx0253"
```

#### 4. 手机号匹配和解析

```python
full_phone = "17604840253"
masked_phone = "176****0253"

# 比较手机号（支持掩码比较）
is_same = PhoneUtils.compare_phones(full_phone, masked_phone)
# 结果：True

# 从掩码恢复完整手机号
resolved = PhoneUtils.unmask_phone(masked_phone, full_phone)
# 结果："17604840253"
```

#### 5. 提取手机号信息

```python
# 分析手机号信息
phone = "176****0253"
is_valid, is_masked, normalized = PhoneUtils.extract_phone_info(phone)
# 结果：(True, True, None)

phone2 = "17604840253"
is_valid2, is_masked2, normalized2 = PhoneUtils.extract_phone_info(phone2)
# 结果：(True, False, "17604840253")
```

### 便捷函数使用

```python
# 验证并标准化手机号
try:
    normalized_phone = validate_and_normalize_phone("176-0484-0253")
    # 结果："17604840253"
except PhoneValidationError as e:
    print(f"手机号验证失败: {e.message}")

# 为显示生成掩码
display_phone = mask_phone_for_display("17604840253")
# 结果："176****0253"

# 解析可能的掩码手机号
resolved_phone = resolve_phone_number("176****0253", "17604840253")
# 结果："17604840253"
```

## 最佳实践

### 1. 时间处理最佳实践

```python
# ✅ 推荐：统一使用TimeUtils
from app.utils.time_utils import TimeUtils

# 数据库存储时间
created_at = TimeUtils.get_utc_now()

# 计算过期时间
expires_at = TimeUtils.calculate_expiry(30, created_at)

# 检查是否过期
if TimeUtils.is_expired(expires_at):
    # 处理过期逻辑
    pass

# ❌ 不推荐：直接使用datetime
import datetime
created_at = datetime.datetime.now()  # 时区不明确
```

### 2. JWT令牌处理最佳实践

```python
# ✅ 推荐：使用JWTTimeHelper
from app.utils.time_utils import JWTTimeHelper

def create_access_token(user_id: int, expire_minutes: int = 30):
    # 统一的时间字段生成
    jwt_time_fields = JWTTimeHelper.create_jwt_payload_time(expire_minutes)
    
    payload = {
        "sub": str(user_id),
        "user_id": user_id,
        **jwt_time_fields
    }
    
    return jwt.encode(payload, SECRET_KEY, algorithm="HS256")

# ❌ 不推荐：手动处理时间
def create_token_manual(user_id: int):
    now = datetime.datetime.utcnow()  # 容易出错
    expire = now + datetime.timedelta(minutes=30)
    payload = {
        "sub": str(user_id),
        "exp": expire.timestamp(),
        "iat": now.timestamp()
    }
```

### 3. 手机号处理最佳实践

```python
# ✅ 推荐：统一使用PhoneUtils
from app.utils.phone_utils import PhoneUtils

def process_user_phone(input_phone: str):
    # 验证和标准化
    if not PhoneUtils.validate_phone(input_phone):
        raise ValueError("手机号格式不正确")
    
    # 存储标准化版本
    normalized_phone = PhoneUtils.normalize_phone(input_phone)
    
    # 返回掩码版本用于显示
    display_phone = PhoneUtils.mask_phone(normalized_phone)
    
    return normalized_phone, display_phone

# ❌ 不推荐：自己写正则验证
import re

def validate_phone_manual(phone: str):
    # 容易遗漏边缘情况
    pattern = r"^1[3-9]\d{9}$"
    return bool(re.match(pattern, phone))
```

## 常见用例

### 1. 设备验证令牌生成

```python
from app.utils.time_utils import TimeUtils, JWTTimeHelper
from app.utils.phone_utils import PhoneUtils

def generate_device_verification_token(user_id: int, device_id: int, phone: str):
    # 标准化手机号
    normalized_phone = PhoneUtils.normalize_phone(phone)
    if not normalized_phone:
        raise ValueError("无效的手机号格式")
    
    # 生成30分钟过期的令牌
    jwt_time_fields = JWTTimeHelper.create_jwt_payload_time(30)
    
    payload = {
        "sub": str(user_id),
        "user_id": user_id,
        "device_id": device_id,
        "phone_number": normalized_phone,
        "type": "device_verification",
        **jwt_time_fields
    }
    
    return jwt.encode(payload, SECRET_KEY, algorithm="HS256")
```

### 2. 短信验证码过期检查

```python
from app.utils.time_utils import TimeUtils

class SMSCodeManager:
    def __init__(self):
        self.codes = {}  # 存储验证码信息
    
    def generate_code(self, phone: str) -> str:
        code = "123456"  # 实际应该生成随机码
        
        # 记录生成时间和过期时间
        created_at = TimeUtils.get_utc_now()
        expires_at = TimeUtils.calculate_expiry(5, created_at)  # 5分钟过期
        
        self.codes[phone] = {
            "code": code,
            "created_at": created_at,
            "expires_at": expires_at
        }
        
        return code
    
    def verify_code(self, phone: str, input_code: str) -> bool:
        if phone not in self.codes:
            return False
        
        code_info = self.codes[phone]
        
        # 检查是否过期
        if TimeUtils.is_expired(code_info["expires_at"]):
            del self.codes[phone]  # 清理过期验证码
            return False
        
        return code_info["code"] == input_code
```

### 3. 用户界面时间显示

```python
from app.utils.time_utils import TimeUtils

def format_user_time(utc_time: datetime) -> dict:
    """为前端提供格式化的时间信息"""
    return {
        "display_time": TimeUtils.format_display(utc_time),
        "relative_time": get_relative_time(utc_time),  # 如"5分钟前"
        "iso_format": TimeUtils.format_for_jwt(utc_time),
        "timestamp": TimeUtils.datetime_to_timestamp(utc_time)
    }

def get_relative_time(utc_time: datetime) -> str:
    current = TimeUtils.get_utc_now()
    diff_seconds = TimeUtils.get_time_diff_seconds(utc_time, current)
    
    if diff_seconds < 60:
        return "刚刚"
    elif diff_seconds < 3600:
        return f"{diff_seconds // 60}分钟前"
    elif diff_seconds < 86400:
        return f"{diff_seconds // 3600}小时前"
    else:
        return f"{diff_seconds // 86400}天前"
```

## 注意事项

### 1. 时间相关注意事项

- **始终使用UTC时间进行存储和计算**
- **仅在显示时转换为本地时间**
- **避免直接使用`datetime.now()`，使用`TimeUtils.get_utc_now()`**
- **JWT令牌时间字段统一使用`JWTTimeHelper`**

### 2. 手机号相关注意事项

- **数据库中存储标准化的完整手机号**
- **前端显示时使用掩码版本**
- **接收用户输入时先验证再标准化**
- **比较手机号时使用`PhoneUtils.compare_phones()`支持掩码**

### 3. 性能考虑

- **时间工具类方法都是轻量级的，可以频繁调用**
- **手机号标准化涉及正则表达式，避免在高频循环中使用**
- **JWT时间验证相对较快，适合在中间件中使用**

### 4. 错误处理

```python
# 时间处理错误
try:
    expiry_time = TimeUtils.calculate_expiry(30)
    if TimeUtils.is_expired(expiry_time):
        # 处理过期情况
        pass
except Exception as e:
    logger.error(f"时间处理异常: {e}")

# 手机号处理错误
try:
    normalized = validate_and_normalize_phone(user_input)
except PhoneValidationError as e:
    return {"error": e.message, "code": e.error_code}

# JWT时间验证错误
time_valid, error_msg = JWTTimeHelper.validate_jwt_time(payload)
if not time_valid:
    raise TokenValidationError(f"令牌时间无效: {error_msg}")
```

## 总结

这些工具类提供了统一、可靠的时间和手机号处理能力，帮助解决了项目中的时间一致性和数据格式问题。正确使用这些工具类可以：

1. **避免时区相关的Bug**
2. **确保JWT令牌时间处理的一致性**
3. **统一手机号格式验证和处理逻辑**
4. **提高代码的可维护性和可读性**

建议在开发新功能时优先使用这些工具类，逐步迁移现有代码以确保项目的一致性。
