# Scratch 背包功能后端需求说明

## 一、背景与目标
- 前端基于 `external/scratch-gui`，当提供 `backpackHost` 时会展示背包入口并调用 REST 接口。
- 需要一个 FastAPI 服务，实现背包内容的上传、列表、删除以及静态资源访问，确保与现有 GUI 行为完全兼容。
- 服务应支持后续扩展（条目标签、版本等），因此需留出数据模型和接口扩展点。

## 二、用户场景
1. **用户登录并展开背包**：客户端以 `GET /:username?limit=20&offset=0` 请求列表，用于首次加载与分页加载。
2. **拖拽资源到背包**：客户端根据资源类型（造型/声音/精灵/脚本）发送 `POST /:username` 保存，成功后立即回填返回的条目。
3. **删除背包条目**：用户在 UI 中删除资源，客户端调用 `DELETE /:username/:id`。
4. **资源引用**：前端使用响应中的 `thumbnail`、`body` 路径拼接 `host` 渲染缩略图、下载 ZIP/JSON。

## 三、接口契约
### 1. 请求认证
- 所有写/读接口都要求请求头 `x-token: <jwt|session token>`。
- 路径参数 `:username` 必须与 token 中的用户标识一致，否则返回 `403`。

### 2. 列表接口 `GET /{username}`
- 请求参数：`limit`（默认 20，最大 50）、`offset`（默认 0）。
- 响应：`200 OK`，`Content-Type: application/json`。
- 数据结构：
  ```json
  [
    {
      "id": "string",
      "type": "costume|sound|sprite|script",
      "name": "用户命名",
      "mime": "image/svg+xml",
      "thumbnail": "thumbnails/uuid.jpg",
      "body": "objects/uuid.dat",
      "createdAt": "2024-05-01T00:00:00Z",
      "size": 12345
    }
  ]
  ```
- 前端会将 `thumbnail` 和 `body` 与 host 拼接成绝对 URL 使用；保留字段可加在响应中，但需保证现有字段名称与类型兼容。

### 3. 创建接口 `POST /{username}`
- 请求体：
  ```json
  {
    "type": "sprite",
    "mime": "image/png",
    "name": "My Sprite",
    "body": "<Base64 字符串，无 data URI 前缀>",
    "thumbnail": "<Base64 JPEG，无 data URI 前缀>"
  }
  ```
- 校验：
  - `type` 必须在白名单内。
  - `body`、`thumbnail` 需为有效 Base64，解码后大小需在配置阈值内（默认 ≤ 10MB，可配置）。
  - 根据 `mime` 校验类型与 `type` 是否匹配。
- 处理流程：
  1. 解析 token，确认请求人。
  2. Base64 解码资源，保存到文件系统或对象存储，生成可访问路径。
  3. 记录数据库条目（含 `username`、`type`、`mime`、`name`、`body_path`、`thumbnail_path`、`size`、`created_at` 等）。
  4. 返回 `201 Created`，响应体与列表条目结构相同。

### 4. 删除接口 `DELETE /{username}/{itemId}`
- 校验 `itemId` 是否属于当前用户；若不存在返回 `404`。
- 删除数据库记录与对应的文件（可异步）。
- 返回 `204 No Content`。

### 5. 静态资源访问
- 前端通过 `${host}/${item.thumbnail}`、`${host}/${item.body}` 请求资源；需提供静态文件路由或挂载对象存储 CDN。
- `sprite` 类型的 body 需以 ZIP（`application/octet-stream`）返回；`script` 类型应返回 JSON（`application/json`）。

## 四、数据模型设计
### 1. 数据表 `backpack_items`
| 字段 | 类型 | 说明 |
| ---- | ---- | ---- |
| `id` | UUID/VARCHAR | 主键 |
| `username` | VARCHAR | 所属用户 |
| `type` | ENUM | `costume`/`sound`/`sprite`/`script` |
| `mime` | VARCHAR | 资源 MIME 类型 |
| `name` | VARCHAR | 用户自定义名称 |
| `body_path` | VARCHAR | 资源文件相对路径 |
| `thumbnail_path` | VARCHAR | 缩略图路径 |
| `size_bytes` | BIGINT | 主体资源大小 |
| `metadata` | JSONB | 预留字段（如分辨率、持续时长等） |
| `created_at` | TIMESTAMP | 创建时间 |

### 2. FastAPI/Pydantic 模型
- `BackpackItemType(Enum)`: `costume/sound/sprite/script`
- `BackpackItemBase`: `type`, `mime`, `name`
- `BackpackItemCreate`: 继承 Base，新增 `body: str`, `thumbnail: str`
- `BackpackItemResponse`: 继承 Base，新增 `id`, `thumbnail`, `body`, `createdAt`, `size`

## 五、系统组件与依赖
- **认证模块**：解析 `x-token`，建议使用 JWT 或服务端 session 签发的哈希。
- **存储模块**：可选方案
  1. 本地磁盘：按用户名/日期分层保存；需提供静态文件服务。
  2. 对象存储（S3、OSS 等）：上传后返回外链，同时存储 `body_path`、`thumbnail_path`。
- **数据库**：PostgreSQL 优先（支持 JSONB），也可使用 MySQL。
- **日志与监控**：记录上传/下载行为、异常与耗时；暴露 Prometheus 指标。

## 六、安全与风控
- 限制每个用户的总空间与单次上传大小；考虑配额表或周期性清理策略。
- 校验 MIME 类型，阻止可执行文件或恶意脚本上传。
- 配置 CORS：仅允许可信前端域访问。
- 防止 DOS：对上传接口做速率限制（如每分钟 30 次）。
- 所有错误返回统一 JSON 结构，包含 `code`, `message`, `details`。

## 七、开发任务拆分
1. **基础设施**：搭建 FastAPI 项目骨架，配置数据库 ORM、依赖注入、日志。
2. **认证模块**：实现 `x-token` 验证、用户解析中间件。
3. **存储实现**：完成 Base64 解码、文件落盘、路径生成、静态服务配置。
4. **CRUD API**：按照契约实现三个接口，完善错误处理与单元测试。
5. **部署与配置**：提供 `.env` 配置样例（host、bucket、DB、限流等）。
6. **监控与运维**：接入指标、日志切割、告警策略。

## 八、测试建议
- 单元测试：覆盖 Base64 解码、枚举校验、权限校验、DAO。
- 集成测试：模拟前端上传 -> 列表 -> 删除全链路；校验静态资源可访问。
- 压力测试：验证并发上传/下载时的性能和资源占用。
- 安全测试：Token 伪造、越权访问、异常文件上传。

## 九、上线注意事项
- 预先创建目标对象存储目录/桶，设置跨域。
- 配置反向代理（Nginx）处理静态资源缓存、限流。
- 与前端约定 `backpackHost` 与 Token 来源；确保 prod/staging 配置一致。
- 提供回滚方案：支持快速切换到旧服务或禁用背包功能。

## 十、后续扩展建议
- 增加条目搜索/标签分组。
- 接入版本化（同名资源覆盖时保留历史）。
- 引入 WebSocket 通知，实现多端背包同步刷新。
- 统计背包使用行为，用于容量规划。
