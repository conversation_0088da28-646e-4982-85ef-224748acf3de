# 统一响应与异常处理重构方案

## 1. 背景与动机

当前项目的响应处理和异常捕获机制存在一些问题，导致代码一致性不足、逻辑重叠且难以维护。主要体现在：

- **响应结构不统一**: `response_wrapper.py` 中定义的 `APIResponse` 模型与实际创建响应的辅助函数所生成的结构不完全匹配（`success` vs `status` 字段）。
- **异常处理冲突**: `api/middleware.py` 中的 `ExceptionHandlerMiddleware` 与 `api/auth_exception_handler.py` 中注册的专用异常处理器存在功能重叠和执行顺序冲突，导致专用处理器无法按预期工作。
- **响应格式化逻辑复杂**: `ResponseFormatterMiddleware` 尝试通过检查响应体来避免双重包装，实现方式复杂且不够健壮。

为了提升代码质量、可读性和长期可维护性，有必要进行一次全面的重构。

## 2. 设计目标

本次重构旨在达成以下目标：

- **统一化**: 建立唯一的、标准化的 API 响应模型，适用于所有成功、失败和异常情况。
- **清晰化**: 响应结构清晰，字段含义明确，业务状态码与 HTTP 状态码分离。
- **健壮性**: 移除冲突的实现，采用 FastAPI 官方推荐的最佳实践来处理异常。
- **简洁化**: 简化业务代码，使开发者能更专注于业务逻辑本身，而非响应格式。
- **最佳实践**: 遵循“在服务层抛出具体异常，在全局处理器中捕获”的模式，避免在API端点使用 `try...except`，保持端点代码的干净与职责单一。

## 3. 核心设计

### 3.1. 标准 API 响应模型 (`APIResponse`)

所有 API 响应都将遵循此 Pydantic 模型结构：

```python
from pydantic import BaseModel, Field
from typing import TypeVar, Generic

T = TypeVar("T")

class APIResponse(BaseModel, Generic[T]):
    success: bool = Field(..., description="请求是否成功")
    code: int = Field(..., description="业务状态码")
    message: str | None = Field(None, description="响应消息")
    data: T | None = Field(None, description="响应数据")
    timestamp: str = Field(..., description="服务器时间戳")
```

### 3.2. 业务状态码 (`ResponseCode`)

我们将引入一个枚举来集中管理所有业务状态码。为提高可读性和可维护性，业务状态码将由 **HTTP状态码** 和 **自定义的、结构化的错误码字符串** 组成。

```python
from enum import Enum

class ResponseCode(Enum):
    # HTTP Status, Error Code, Message
    
    # General Success & Errors
    SUCCESS = (200, "OK", "操作成功")
    FAILURE = (400, "FAIL", "操作失败")
    
    # Common Client Errors
    BAD_REQUEST = (400, "COMMON_BAD_REQUEST", "无效的请求")
    UNAUTHORIZED = (401, "COMMON_UNAUTHORIZED", "未经授权")
    FORBIDDEN = (403, "COMMON_FORBIDDEN", "禁止访问")
    NOT_FOUND = (404, "COMMON_NOT_FOUND", "资源未找到")
    VALIDATION_ERROR = (422, "COMMON_VALIDATION_ERROR", "参数验证失败")

    # Common Server Errors
    INTERNAL_SERVER_ERROR = (500, "SERVER_INTERNAL_ERROR", "服务器内部错误")
    SERVICE_UNAVAILABLE = (503, "SERVER_SERVICE_UNAVAILABLE", "服务不可用")

    # --- Authentication Module Errors (AUTH_xxx) ---
    # User Sub-module
    AUTH_USER_NOT_FOUND = (404, "AUTH_USER_001", "用户不存在")
    AUTH_INVALID_CREDENTIALS = (401, "AUTH_USER_002", "无效的凭据或密码错误")
    AUTH_USER_DISABLED = (403, "AUTH_USER_003", "用户已被禁用")
    AUTH_USER_ALREADY_EXISTS = (409, "AUTH_USER_004", "用户已存在")
    
    # Device Sub-module
    AUTH_DEVICE_NOT_TRUSTED = (403, "AUTH_DEVICE_001", "设备不受信任")
    AUTH_DEVICE_VERIFICATION_REQUIRED = (403, "AUTH_DEVICE_002", "需要设备验证")
    
    # SMS Sub-module
    AUTH_SMS_INVALID_CODE = (400, "AUTH_SMS_001", "无效的验证码")
    AUTH_SMS_CODE_EXPIRED = (400, "AUTH_SMS_002", "验证码已过期")
    AUTH_SMS_RATE_LIMIT = (429, "AUTH_SMS_003", "短信发送频率过高")
    
    # WeChat Sub-module
    AUTH_WECHAT_API_ERROR = (502, "AUTH_WECHAT_001", "微信服务调用失败")
    AUTH_WECHAT_INVALID_OPENID = (400, "AUTH_WECHAT_002", "无效的OpenID")

    # --- Article Module Errors (ARTICLE_xxx) ---
    ARTICLE_NOT_FOUND = (404, "ARTICLE_001", "文章不存在")
    ARTICLE_PERMISSION_DENIED = (403, "ARTICLE_002", "无权操作此文章")
    ARTICLE_ALREADY_EXISTS = (409, "ARTICLE_003", "具有相同标题的文章已存在")

    # --- Video Module Errors (VIDEO_xxx) ---
    VIDEO_NOT_FOUND = (404, "VIDEO_001", "视频不存在")
    VIDEO_PERMISSION_DENIED = (403, "VIDEO_002", "无权操作此视频")
    VIDEO_FOLDER_NOT_FOUND = (404, "VIDEO_003", "视频文件夹不存在")

    def __init__(self, status_code, error_code, message):
        self._status_code = status_code
        self._error_code = error_code
        self._message = message

    @property
    def status_code(self):
        return self._status_code
        
    @property
    def error_code(self):
        return self._error_code

    @property
    def message(self):
        return self._message
```

### 3.3. 响应构建辅助工具 (`ResponseWrapper`)

创建一个辅助类，用于便捷地生成标准格式的响应字典。

```python
class ResponseWrapper:
    @staticmethod
    def success(data: Any, code: ResponseCode = ResponseCode.SUCCESS, message: str | None = None) -> dict:
        return {
            "success": True,
            "code": code.code,
            "message": message or code.message,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }

    @staticmethod
    def fail(code: ResponseCode, message: str | None = None, data: Any = None) -> dict:
        return {
            "success": False,
            "code": code.code,
            "message": message or code.message,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
```

## 4. 新的目录结构规划

为了实现高度的模块化和关注点分离，我们将采用以下新的目录结构：

```
app/
├── core/
│   └── response_wrapper.py     # 职责不变：定义响应模型、状态码和构建工具
│
├── exceptions/
│   ├── __init__.py
│   ├── base.py                 # 定义所有业务异常的基类 BusinessException
│   ├── auth.py                 # 存放认证授权相关的异常
│   ├── article.py              # 存放文章相关的异常
│   ├── video.py                # 存放视频相关的异常
│   └── ...                     # 其他业务模块的异常
│
└── exception_handlers/
    ├── __init__.py
    ├── auth.py                 # 存放认证异常的处理器
    ├── article.py              # 存放文章异常的处理器
    ├── video.py                # 存放视频异常的处理器
    └── general.py              # 存放通用异常 (HTTPException, ValidationError) 的处理器
```

## 5. 实施步骤

### 第一阶段：建立核心响应模块

1.  **重构 `app/core/response_wrapper.py`**:
    *   此文件的职责保持不变，专注于响应格式化。
    *   实现方案文档第 3 节中定义的 `APIResponse` 模型, `ResponseCode` 枚举, `ResponseWrapper` 类, 以及 `success_response`/`error_response` 辅助函数。

### 第二阶段：建立新的异常定义体系

1.  **创建 `app/exceptions/` 目录**。
2.  在 `app/exceptions/base.py` 中创建 `BusinessException` 基类。
3.  **迁移与重构**:
    *   将 `app/core/auth_exceptions.py` 的内容迁移并重构至 `app/exceptions/auth.py`。
    *   将 `app/exception/exceptions.py` 中有用的部分（如微信异常）迁移并重构至 `app/exceptions/` 下的新文件中（如 `app/exceptions/wechat.py`）。
4.  **清理**: 删除旧的 `app/core/auth_exceptions.py` 和 `app/exception/exceptions.py` 文件。

### 第三阶段：建立新的异常处理体系

1.  **创建 `app/exception_handlers/` 目录**。
2.  **迁移与重构**:
    *   将 `app/api/auth_exception_handler.py` 的逻辑拆分并迁移到 `app/exception_handlers/` 目录下的相应文件中（如 `auth.py`, `general.py`）。
    *   确保所有处理器都使用第一阶段创建的 `error_response` 辅助函数来生成响应。
3.  **清理**: 删除旧的 `app/api/auth_exception_handler.py` 文件。

### 第四阶段：重构中间件与应用注册

1.  **重写 `ResponseFormatterMiddleware`**:
    *   在 `app/api/middleware.py` 中，重写中间件，使其逻辑更简洁、健壮。它将拦截端点返回的原始数据，并使用 `ResponseWrapper.success()` 进行包装。
2.  **移除 `ExceptionHandlerMiddleware`**: 这个中间件的功能已被新的异常处理体系完全取代。
3.  **更新应用入口 (`main.py`)**:
    *   确保注册了重写后的 `ResponseFormatterMiddleware`。
    *   确保从 `app/exception_handlers/` 导入并注册了所有新的异常处理器。

### 第五阶段：业务逻辑回归与职责重塑

1.  **废弃响应构建器**:
    *   彻底删除 `app/core/auth_utils.py` 中的 `AuthResponseBuilder` 和 `AuthResultConverter` 类。
    *   它们的逻辑将回归到业务服务层和异常处理器中。
2.  **重构业务服务**:
    *   服务层（如 `SmsAuthService`）将负责完整的业务流程。
    *   成功时，服务层方法直接返回定义在 `app/schemas/` 中的 Pydantic 响应模型。
    *   失败时，服务层方法直接 `raise` 在 `app/exceptions/` 中定义的具体业务异常。
3.  **简化 API 端点**:
    *   API 端点将变得非常“薄”，仅负责：
        *   接收请求。
        *   调用业务服务。
        *   直接返回服务层的结果（该结果会被 `ResponseFormatterMiddleware` 自动包装）。
4.  **重定位工具类**:
    *   将 `app/core/auth_utils.py` 中有用的 `AuthValidator` 和 `AuthConstants` 移动到更合适的、非核心的位置（如 `app/utils/auth_helpers.py`），以保持 `core` 目录的纯粹性。

### 第六阶段：全项目推广

1.  使用全局搜索，找到所有旧的实现方式。
2.  全面应用新的模式：**端点 -> 服务（返回 Pydantic 模型或抛出异常） -> 中间件/异常处理器（统一格式化响应）**。

## 7. 响应示例

### 成功响应

**HTTP Status**: `200 OK`
```json
{
  "success": true,
  "code": 20000,
  "message": "操作成功",
  "data": { "user_id": 123 },
  "timestamp": "2023-10-27T10:00:00.123456"
}
```

### 失败响应（业务逻辑）

**HTTP Status**: `401 Unauthorized`
```json
{
  "success": false,
  "code": "AUTH_USER_002",
  "message": "无效的凭据或密码错误",
  "data": null,
  "timestamp": "2023-10-27T10:05:00.567890"
}
```

### 失败响应（异常捕获）

**HTTP Status**: `404 Not Found`
```json
{
  "success": false,
  "code": "ARTICLE_001",
  "message": "文章不存在",
  "data": {
    "article_id": 999
  },
  "timestamp": "2023-10-27T10:10:00.123456"
}
```

## 8. 未来展望

本次重构为后续优化奠定了坚实的基础。未来可以考虑：

- **集成问题跟踪系统**: 在通用异常处理器中，将 5xx 错误自动上报到 Sentry 等平台。
- **细化错误码规范**: 建立更详细的、分模块的错误码体系。
- **多语言支持**: 将错误消息移至配置文件，以支持国际化。
