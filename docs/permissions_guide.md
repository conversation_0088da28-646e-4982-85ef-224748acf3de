# 权限系统重构指南

## 1. 设计理念

为了提高代码的可维护性、可读性并减少冗余，我们对权限系统的应用方式进行了重构。新的设计遵循以下核心理念：

-   **声明式 (Declarative):** API端点的权限需求应该像声明路由、请求体一样清晰地声明出来，而不是在业务逻辑中通过代码实现。
-   **集中化 (Centralized):** 所有的权限检查逻辑都收敛到一个统一的依赖注入工厂函数中，避免逻辑分散在各个服务中。
-   **可维护性 (Maintainable):** 权限定义以常量的形式集中管理，消除了“魔法字符串”，使得权限的修改和查找更加方便和安全。

## 2. 核心组件

### 2.1. 权限常量 (`app/core/permissions.py`)

所有在系统中使用的权限都被定义为 `Permission` 类的实例常量，存放在 `app.core.permissions.Permissions` 类中。

**命名规范:** `RESOURCE_ACTION_SCOPE`

**示例:**

```python
from app.core.permissions import Permissions

# 获取“更新自己的文章”权限
perm = Permissions.ARTICLE_UPDATE_OWN
```

### 2.2. 统一依赖注入工厂 (`app/api/permission_deps.py`)

我们引入了一个统一的、功能强大的依赖注入工厂函数 `require_permission`，它是未来所有API权限检查的唯一入口。

```python
def require_permission(
    permission: Permission,
    resource_type: ResourceType | None = None,
    resource_id_key: str | None = None,
    optional_user: bool = False,
) -> Callable:
    # ... implementation
```

-   `permission`: **必需**。从 `Permissions` 类中获取的权限常量。
-   `resource_type`: **可选**。当需要检查特定资源（如所有权）时，提供资源的类型（`ResourceType.ARTICLE`）。
-   `resource_id_key`: **可选**。与 `resource_type` 配合使用，指明从请求路径参数中提取资源ID的键名。
-   `optional_user`: **可选**。默认为 `False`（需要登录）。设为 `True` 时，允许游客访问（如果权限本身允许，如 `PUBLIC` 范围）。

## 3. 如何使用

### 场景1: 简单的权限检查（无需资源）

检查用户是否有权创建文章。

**旧方式:**

```python
from app.api.permission_deps import PermissionDeps
# ...
current_user: models.User = Depends(PermissionDeps.article_create)
```

**新方式:**

```python
from app.api.permission_deps import require_permission
from app.core.permissions import Permissions
# ...
current_user: models.User = Depends(require_permission(Permissions.ARTICLE_CREATE_OWN))
```

### 场景2: 需要所有权检查的权限

检查用户是否有权更新某篇文章。这要求用户要么是文章的作者（`OWN` 范围），要么有权更新所有文章。

**旧方式:**

```python
# 在函数体内调用 ArticlePermissionService
article_permission_service: ArticlePermissionService = Depends(...)
# ...
can_update = await article_permission_service.check_article_update_permission(...)
if not can_update:
    raise HTTPException(...)
```

**新方式:**

```python
from app.api.permission_deps import require_permission
from app.core.permissions import Permissions
from app.core.permission_system import ResourceType
# ...
@router.put("/{article_id}")
async def update_article(
    article_id: int,
    # ...
    current_user: models.User = Depends(require_permission(
        permission=Permissions.ARTICLE_UPDATE_OWN,
        resource_type=ResourceType.ARTICLE,
        resource_id_key="article_id", # 对应路径参数 "{article_id}"
    )),
):
    # 业务逻辑...
```

### 场景3: 允许游客访问的公共资源

获取一篇文章的详情。如果文章是公开的，游客可以访问；如果是非公开的，则需要相应的权限（如作者本人）。

**旧方式:**

```python
# 在函数体内调用 ArticlePermissionService
article_permission_service: ArticlePermissionService = Depends(...)
# ...
can_access = await article_permission_service.check_article_access(...)
if not can_access:
    raise HTTPException(...)
```

**新方式:**

```python
from app.api.permission_deps import require_permission
from app.core.permissions import Permissions
from app.core.permission_system import ResourceType
# ...
@router.get("/{article_id}")
async def get_article(
    article_id: int,
    # ...
    current_user: models.User | None = Depends(require_permission(
        permission=Permissions.ARTICLE_READ_PUBLIC,
        resource_type=ResourceType.ARTICLE,
        resource_id_key="article_id",
        optional_user=True, # 允许游客
    )),
):
    # 业务逻辑...
```

## 4. 迁移指南

-   **废弃的服务:** `ArticlePermissionService`, `VideoPermissionService`, `UnifiedPermissionService` 已被标记为 `@deprecated`。请逐步将对这些服务的所有调用替换为新的 `require_permission` 依赖注入。
-   **查找目标:** 在代码库中搜索对上述废弃服务的引用。
-   **替换逻辑:** 根据上述场景示例，将函数体内的权限检查代码移除，并在路由函数的依赖项中添加相应的 `require_permission` 调用。