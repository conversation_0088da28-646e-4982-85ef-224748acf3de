# 项目架构总览

本文档旨在提供对当前项目架构的全面概述，包括其核心分层设计、关键数据流和采用的主要设计模式。

## 1. 宏观分层架构

项目采用经典的分层架构，确保了职责分离和高可维护性。从客户端请求到数据存储，整个流程分为四个主要层次：

```mermaid
graph TD
    A[Client] -->|HTTP Request| B(API Gateway / FastAPI);

    subgraph Service Layer
        B --> C{Aggregation Services};
        C --> D[Cache & Stats Services];
    end

    subgraph Data Layer
        D --> E[CRUD Services];
        E --> F((PostgreSQL));
        D --> G((Redis));
    end

    style B fill:#f9f,stroke:#333,stroke-width:2px
    style C fill:#bbf,stroke:#333,stroke-width:2px
    style D fill:#ccf,stroke:#333,stroke-width:2px
    style E fill:#cdf,stroke:#333,stroke-width:2px
```

*   **API Gateway (FastAPI)**: 作为系统的入口，负责处理所有传入的HTTP请求、验证数据格式（通过Pydantic模型）并将请求路由到相应的聚合服务。
*   **Aggregation Services**: 业务逻辑的核心编排层。它们不执行具体的数据库或缓存操作，而是负责调用下层的缓存和统计服务，并将返回的数据聚合成最终的、面向客户端的数据结构。
*   **Cache & Stats Services**: 提供基础业务逻辑和缓存功能。这一层直接与Redis交互，实现了旁路缓存、高性能统计等关键功能。
*   **CRUD Services**: 数据访问层，封装了所有对PostgreSQL数据库的SQL操作，为上层服务提供了简洁、统一的数据访问接口。
## 2. 核心数据流分析：获取单篇文章

为了更深入地理解服务间的交互，我们以“获取单篇文章详情”为例，展示其完整的数据流。该流程的核心在于 **并行I/O**，通过 `asyncio.gather` 同时获取文章、作者和统计数据，极大地优化了响应时间。

```mermaid
sequenceDiagram
    participant Client
    participant ArticleAPI
    participant ArticleAggregationService
    participant asyncio as asyncio.gather
    participant ArticleCache
    participant UserCache
    participant StatsService
    participant Redis
    participant PostgreSQL

    Client-&gt;&gt;ArticleAPI: GET /api/articles/{id}
    ArticleAPI-&gt;&gt;ArticleAggregationService: get_aggregated_article(id)
    
    ArticleAggregationService-&gt;&gt;asyncio: Launch parallel tasks
    par
        asyncio-&gt;&gt;ArticleCache: get_article(id)
        ArticleCache--&gt;&gt;Redis: GET article:{id}
        Redis--&gt;&gt;ArticleCache: (Cache Hit) Article Base
        ArticleCache--&gt;&gt;asyncio: Return Article
    and
        asyncio-&gt;&gt;UserCache: get_user(author_id)
        UserCache--&gt;&gt;Redis: GET user:{id}
        Redis--&gt;&gt;UserCache: (Cache Hit) User Info
        UserCache--&gt;&gt;asyncio: Return Author
    and
        asyncio-&gt;&gt;StatsService: get_stats(id)
        StatsService--&gt;&gt;Redis: MGET article:{id}:*
        Redis--&gt;&gt;StatsService: Stats Data
        StatsService--&gt;&gt;asyncio: Return Stats
    end
    
    asyncio--&gt;&gt;ArticleAggregationService: Return all data
    ArticleAggregationService-&gt;&gt;ArticleAPI: Return ArticleOut (aggregated)
    ArticleAPI--&gt;&gt;Client: 200 OK (JSON Response)
```

**数据流说明:**
1.  **客户端请求**: 客户端向API网关发起获取文章的请求。
2.  **服务调用**: API层调用 `ArticleAggregationService` 来处理业务逻辑。
3.  **并行获取**: `ArticleAggregationService` 使用 `asyncio.gather` 并行启动三个独立的任务，分别从 `ArticleCacheService`、`UserCacheService` 和 `ContentStatsService` 获取数据。
4.  **缓存优先**: 每个底层服务都优先尝试从Redis缓存中获取数据。如果缓存命中，则直接返回；如果未命中，则会查询PostgreSQL，并将结果回填到Redis中（此图简化了未命中流程）。
5.  **数据聚合**: `asyncio.gather` 等待所有并行任务完成后，将结果统一返回给 `ArticleAggregationService`。
6.  **组装与返回**: `ArticleAggregationService` 将来自不同服务的数据组装成一个完整的 `ArticleOut` Pydantic模型，并返回给API层。
7.  **最终响应**: API层将Pydantic模型序列化为JSON，并将其作为HTTP响应返回给客户端。
## 3. 核心设计模式解析

项目的健壮性和高性能得益于几个关键设计模式的成功应用。

### 聚合器模式 (Aggregator Pattern)

**聚合器服务** (`*_aggregation_service.py`) 是本架构的业务编排核心。它扮演着一个“聚合器”的角色，负责将来自多个下游微服务或数据源（如缓存服务、统计服务、数据库服务）的零散数据，聚合成一个统一的、有意义的数据结构，以满足特定的业务需求。

*   **职责**:
    *   **解耦**: 将客户端（API层）与复杂的后端数据源解耦。API层只需与聚合器交互，而无需关心数据究竟来自何处。
    *   **编排**: 负责调用多个下游服务，并使用 `asyncio.gather` 等机制实现并行处理，以优化性能。
    *   **转换**: 将来自不同服务的原始数据，转换并组装成面向客户端的、标准化的数据传输对象（DTOs），即 `schemas`。

### 旁路缓存模式 (Cache-Aside Pattern)

系统广泛采用“旁路缓存”模式来提升读取性能并降低数据库负载。`BaseCacheService` 是该模式的通用实现。

*   **读取流程**:
    1.  应用首先尝试从 **Redis缓存** 中读取数据。
    2.  如果缓存 **命中 (Cache Hit)**，则直接将数据返回给调用方。
    3.  如果缓存 **未命中 (Cache Miss)**，应用会从 **PostgreSQL数据库** 中读取数据。
    4.  读取成功后，应用会将数据 **回填 (Backfill)** 到Redis缓存中，并设置一个合理的过期时间（TTL），以供后续请求使用。

*   **写入与失效**:
    *   当数据发生变更时（如更新或删除），系统会直接操作数据库，并采取 **“写后失效” (Write-Invalidate)** 策略，即删除缓存中对应的条目。这样可以确保下次读取时能从数据库加载最新的数据，保证了数据的一致性。

*   **缓存穿透防护**:
    *   系统还引入了 **布隆过滤器 (Bloom Filter)**。在查询数据库之前，会先通过布隆过滤器检查数据是否存在。如果布隆过滤器判定数据不存在，则直接返回，避免了对数据库的无效查询，有效防止了缓存穿透攻击。

### 原子计数器模式 (Atomic Counter Pattern)

对于点赞、收藏、浏览量等高并发的计数场景，`ContentStatsService` 采用了基于Redis的“原子计数器”模式。

*   **实现**:
    *   该服务充分利用了Redis的 **原子操作**（如 `INCR`, `DECR`, `SADD`, `SREM`），这些操作在单个命令中完成，保证了即使在大量并发请求下，计数结果也是准确的，避免了竞态条件。
    *   例如，当用户点赞时，系统会同时执行 `SADD`（将用户ID添加到点赞者集合）和 `INCR`（增加点赞总数）两个命令，并通过Redis的 **Pipeline** 机制将它们打包在一次网络往返中，以最大化性能。