# 用户点赞收藏列表API文档

## 概述

基于现有的点赞和收藏系统，提供用户点赞和收藏内容的列表查询接口，支持分页和内容类型筛选。

## API端点

### 1. 获取用户点赞列表

**端点**: `GET /users/{user_id}/likes`

**描述**: 获取指定用户的点赞内容列表

**权限**: 
- 用户本人可查看所有点赞记录
- 其他用户只能查看公开内容的点赞记录

**参数**:
```typescript
interface UserLikesParams {
  user_id: number; // 路径参数，用户ID
  content_type?: 'article' | 'video' | null; // 查询参数，内容类型筛选
  cursor?: string; // 查询参数，分页游标
  size?: number; // 查询参数，每页大小，默认20，最大100
  order_by?: 'created_at' | 'content_title'; // 查询参数，排序字段
  order_direction?: 'asc' | 'desc'; // 查询参数，排序方向
}
```

**响应**:
```typescript
interface CursorPaginationResponse<LikeItem> {
  items: LikeItem[];
  total_count: number;
  has_next: boolean;
  has_previous: boolean;
  next_cursor?: string;
  previous_cursor?: string;
}

interface LikeItem {
  id: number;
  content_type: 'article' | 'video';
  content_id: number;
  created_at: string;
  
  // 根据内容类型返回不同的详细信息
  content_info?: {
    article?: {
      id: number;
      title: string;
      summary: string;
      cover_url?: string;
      author_id: number;
      author_name: string;
      category_id?: number;
      category_name?: string;
      view_count: number;
      like_count: number;
      comment_count: number;
      created_at: string;
      updated_at: string;
    };
    video?: {
      id: number;
      title: string;
      description?: string;
      cover_url?: string;
      duration?: number;
      author_id: number;
      author_name: string;
      folder_id?: number;
      folder_name?: string;
      view_count: number;
      like_count: number;
      comment_count: number;
      created_at: string;
      updated_at: string;
    };
  };
}
```

### 2. 获取用户收藏列表

**端点**: `GET /users/{user_id}/favorites`

**描述**: 获取指定用户的收藏内容列表

**权限**: 
- 用户本人可查看所有收藏记录（包括私有收藏）
- 其他用户只能查看公开内容的收藏记录

**参数**:
```typescript
interface UserFavoritesParams {
  user_id: number; // 路径参数，用户ID
  content_type?: 'article' | 'video' | null; // 查询参数，内容类型筛选
  cursor?: string; // 查询参数，分页游标
  size?: number; // 查询参数，每页大小，默认20，最大100
  order_by?: 'created_at' | 'content_title' | 'note'; // 查询参数，排序字段
  order_direction?: 'asc' | 'desc'; // 查询参数，排序方向
  include_note?: boolean; // 查询参数，是否包含收藏备注，默认false
}
```

**响应**:
```typescript
interface CursorPaginationResponse<FavoriteItem> {
  items: FavoriteItem[];
  total_count: number;
  has_next: boolean;
  has_previous: boolean;
  next_cursor?: string;
  previous_cursor?: string;
}

interface FavoriteItem {
  id: number;
  content_type: 'article' | 'video';
  content_id: number;
  note?: string;
  created_at: string;
  updated_at: string;
  
  // 根据内容类型返回不同的详细信息
  content_info?: {
    article?: {
      id: number;
      title: string;
      summary: string;
      cover_url?: string;
      author_id: number;
      author_name: string;
      category_id?: number;
      category_name?: string;
      view_count: number;
      like_count: number;
      favorite_count: number;
      comment_count: number;
      created_at: string;
      updated_at: string;
    };
    video?: {
      id: number;
      title: string;
      description?: string;
      cover_url?: string;
      duration?: number;
      author_id: number;
      author_name: string;
      folder_id?: number;
      folder_name?: string;
      view_count: number;
      like_count: number;
      favorite_count: number;
      comment_count: number;
      created_at: string;
      updated_at: string;
    };
  };
}
```

### 3. 批量获取用户对内容的点赞状态

**端点**: `POST /users/{user_id}/likes/batch-status`

**描述**: 批量查询用户对指定内容列表的点赞状态

**权限**: 需要用户登录，只能查询自己的点赞状态

**请求体**:
```typescript
interface BatchLikeStatusRequest {
  content_items: Array<{
    content_type: 'article' | 'video';
    content_id: number;
  }>;
}
```

**响应**:
```typescript
interface ContentLikeInfo {
  content_type: 'article' | 'video';
  content_id: number;
  like_count: number;
  is_liked_by_user: boolean;
}
```

### 4. 批量获取用户对内容的收藏状态

**端点**: `POST /users/{user_id}/favorites/batch-status`

**描述**: 批量查询用户对指定内容列表的收藏状态

**权限**: 需要用户登录，只能查询自己的收藏状态

**请求体**:
```typescript
interface BatchFavoriteStatusRequest {
  content_items: Array<{
    content_type: 'article' | 'video';
    content_id: number;
  }>;
}
```

**响应**:
```typescript
interface ContentFavoriteInfo {
  content_type: 'article' | 'video';
  content_id: number;
  favorite_count: number;
  is_favorited_by_user: boolean;
}
```

## 实现说明

### 技术栈
- **框架**: FastAPI + SQLAlchemy
- **认证**: JWT Token
- **权限系统**: 自定义权限系统
- **分页**: 游标分页 (Cursor Pagination)
- **缓存**: Redis缓存支持

### 数据库模型
- **点赞**: `Like` 表 (user_id, content_type, content_id, created_at)
- **收藏**: `Favorite` 表 (user_id, content_type, content_id, note, created_at, updated_at)

### 权限控制
- 使用 `require_permission` 装饰器进行权限检查
- 支持用户本人和管理员的不同权限级别
- 自动过滤用户无权访问的内容

### 性能优化
- 使用游标分页提高大数据量查询性能
- 支持批量查询减少网络请求
- 集成缓存服务减少数据库压力
- 聚合服务处理复杂的数据组装逻辑

### 错误处理
- 统一的HTTP状态码返回
- 详细的错误信息描述
- 输入参数验证
- 资源不存在检查

## 示例请求

### 获取用户点赞列表
```bash
curl -X GET "http://localhost:8000/users/1/likes?content_type=article&size=10&order_by=created_at&order_direction=desc" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 获取用户收藏列表
```bash
curl -X GET "http://localhost:8000/users/1/favorites?include_note=true&size=20" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 批量查询点赞状态
```bash
curl -X POST "http://localhost:8000/users/1/likes/batch-status" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "content_items": [
      {"content_type": "article", "content_id": 1},
      {"content_type": "video", "content_id": 2}
    ]
  }'
```