# 消息通知API文档

本文档提供了系统消息通知相关的完整API接口说明，帮助前端开发者快速集成通知功能。所有接口均基于RESTful API设计，并支持WebSocket实时推送。

## 基础信息

- **API版本**: v1.0
- **基础路径**: `/api/v1/notifications`
- **认证要求**: 所有接口需要JWT Bearer Token认证（除广播接口需超级管理员权限）
- **内容类型**: `application/json`
- **错误处理**: 统一返回`success: false`和`error_code`字段

## 核心概念

### 通知类型 (NotificationType)
- `SYSTEM_UPDATE`: 系统更新通知
- `USER_ACTION`: 用户行为通知（如点赞、评论）
- `SOCIAL_INTERACTION`: 社交互动通知（如关注、回复）
- `PROMOTION`: 促销活动通知

### 通知优先级 (NotificationPriority)
- `LOW`: 低优先级
- `NORMAL`: 正常优先级
- `HIGH`: 高优先级
- `URGENT`: 紧急优先级

### 通知状态 (NotificationStatus)
- `unread`: 未读
- `read`: 已读
- `deleted`: 已删除

## 用户端接口

### 1. 获取通知列表

**端点**: `GET /api/v1/notifications/`

**描述**: 获取当前用户的通知列表，支持状态筛选和分页

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| status | string | 否 | 通知状态筛选 | "unread" |
| limit | int | 否 | 每页数量 (1-100) | 20 |
| offset | int | 否 | 分页偏移量 | 0 |

**请求示例**:
```bash
GET /api/v1/notifications/?status=unread&limit=10&offset=0
Authorization: Bearer your_jwt_token
```

**响应示例**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "notifications": [
      {
        "id": 1,
        "type": "SYSTEM_UPDATE",
        "priority": "HIGH",
        "title": "系统维护通知",
        "message": "系统将于今晚22:00-24:00进行维护",
        "data": {"maintenance_window": "22:00-24:00"},
        "action_url": "/maintenance",
        "status": "unread",
        "created_at": "2024-01-15T10:30:00Z",
        "read_at": null,
        "expires_at": "2024-01-22T10:30:00Z"
      }
    ],
    "total": 25,
    "unread_count": 5
  }
}
```

**错误响应**:
```json
{
  "success": false,
  "message": "获取通知列表失败",
  "error_code": "NOTIFICATION_LIST_001"
}
```

### 2. 获取未读通知数量

**端点**: `GET /api/v1/notifications/unread-count`

**描述**: 获取当前用户的未读通知数量

**请求示例**:
```bash
GET /api/v1/notifications/unread-count
Authorization: Bearer your_jwt_token
```

**响应示例**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "unread_count": 5
  }
}
```

### 3. 标记通知为已读

**端点**: `PATCH /api/v1/notifications/{notification_id}/read`

**描述**: 标记指定通知为已读状态

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| notification_id | int | 是 | 通知ID | 1 |

**请求示例**:
```bash
PATCH /api/v1/notifications/1/read
Authorization: Bearer your_jwt_token
```

**响应示例**:
```json
{
  "success": true,
  "message": "标记成功",
  "data": {
    "id": 1,
    "type": "SYSTEM_UPDATE",
    "priority": "HIGH",
    "title": "系统维护通知",
    "message": "系统将于今晚22:00-24:00进行维护",
    "status": "read",
    "read_at": "2024-01-15T10:35:00Z"
  }
}
```

### 4. 批量标记所有通知为已读

**端点**: `PATCH /api/v1/notifications/read-all`

**描述**: 标记当前用户的所有未读通知为已读

**请求示例**:
```bash
PATCH /api/v1/notifications/read-all
Authorization: Bearer your_jwt_token
```

**响应示例**:
```json
{
  "success": true,
  "message": "成功标记 5 条通知为已读",
  "data": {
    "count": 5
  }
}
```

### 5. 删除通知

**端点**: `DELETE /api/v1/notifications/{notification_id}`

**描述**: 删除指定通知（仅删除用户自己的通知）

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| notification_id | int | 是 | 通知ID | 1 |

**请求示例**:
```bash
DELETE /api/v1/notifications/1
Authorization: Bearer your_jwt_token
```

**响应示例**:
```json
{
  "success": true,
  "message": "通知删除成功",
  "data": null
}
```

## 管理员端接口

### 6. 创建通知（管理员）

**端点**: `POST /api/v1/notifications/`

**描述**: 创建新通知（仅管理员或系统使用）

**请求体**:
```json
{
  "user_id": 123,
  "type": "SYSTEM_UPDATE",
  "priority": "HIGH",
  "title": "系统更新通知",
  "message": "系统已更新到最新版本",
  "data": {"version": "2.1.0"},
  "action_url": "/changelog",
  "expires_in_hours": 24
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "创建成功",
  "data": {
    "id": 2,
    "type": "SYSTEM_UPDATE",
    "priority": "HIGH",
    "title": "系统更新通知",
    "message": "系统已更新到最新版本",
    "status": "unread",
    "created_at": "2024-01-15T11:00:00Z"
  }
}
```

### 7. 广播通知给所有用户（超级管理员）

**端点**: `POST /api/v1/notifications/broadcast`

**描述**: 向所有用户发送广播通知，会创建数据库记录并通过WebSocket推送

**请求体**:
```json
{
  "title": "系统维护通知",
  "message": "系统将于今晚22:00-24:00进行维护，期间服务可能中断",
  "type": "system_update",
  "priority": "high",
  "action_url": "/maintenance",
  "expires_in_hours": 168,
  "target_user_type": "all"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "广播发送成功",
  "data": {
    "message": "广播完成",
    "total_users": 1000,
    "notifications_created": 1000,
    "online_users_notified": 150
  }
}
```

### 8. 仅向在线用户广播（超级管理员）

**端点**: `POST /api/v1/notifications/broadcast/online-only`

**描述**: 仅向当前在线用户发送实时消息，不创建数据库记录

**请求体**:
```json
{
  "title": "紧急通知",
  "message": "系统正在进行紧急维护，请保存您的工作",
  "data": {"urgent": true}
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "成功向 150 个在线用户发送消息",
  "data": {
    "online_users_notified": 150
  }
}
```

### 9. 获取通知统计信息（管理员）

**端点**: `GET /api/v1/notifications/stats`

**描述**: 获取通知系统统计信息，包括WebSocket连接状态

**请求示例**:
```bash
GET /api/v1/notifications/stats
Authorization: Bearer admin_jwt_token
```

**响应示例**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "websocket_stats": {
      "total_connections": 200,
      "active_connections": 150,
      "disconnected": 50
    },
    "online_user_count": 150,
    "current_user_connections": 2
  }
}
```

## WebSocket实时通知集成

### 连接WebSocket

**端点**: `ws://your-domain.com/api/v1/notifications/ws`

**认证方式（按优先级）**
- Cookie：`access_token=...`（推荐，后端设置 HttpOnly Cookie 后浏览器会自动携带）
- Sec-WebSocket-Protocol 子协议：`protocols: ['Bearer.{token}']`
- Query 参数：`?token=your_jwt_token`

示例：
```
// 推荐：Cookie 模式（无需在URL附加token）
new WebSocket('ws://your-domain.com/api/v1/notifications/ws')

// 备选 1：浏览器子协议
new WebSocket('ws://your-domain.com/api/v1/notifications/ws', ['Bearer.' + your_jwt_token])

// 备选 2：Query 参数
ws://your-domain.com/api/v1/notifications/ws?token=your_jwt_token
```

### 接收的消息类型

1. **新通知消息** (`type: "new_notification"`)
```json
{
  "type": "new_notification",
  "data": {
    "id": 3,
    "title": "您收到了一条新评论",
    "message": "用户A评论了您的视频",
    "status": "unread"
  },
  "timestamp": "2024-01-15T12:00:00Z"
}
```

2. **未读数量更新** (`type: "unread_count"`)
```json
{
  "type": "unread_count",
  "data": {
    "unread_count": 6
  },
  "timestamp": "2024-01-15T12:01:00Z"
}
```

3. **系统通知** (`type: "system_notification"`)
```json
{
  "type": "system_notification",
  "data": {
    "title": "系统公告",
    "message": "有新的系统功能上线",
    "timestamp": "2024-01-15T12:00:00Z"
  },
  "timestamp": "2024-01-15T12:00:00Z"
}
```

## 前端集成指南

### 1. 初始化通知系统

```javascript
// notificationService.js
import axios from 'axios';

class NotificationService {
  constructor() {
    this.api = axios.create({
      baseURL: '/api/v1',
      timeout: 10000,
    });
    this.ws = null;
    this.token = localStorage.getItem('jwt_token');
  }

  // 连接WebSocket
  connectWebSocket() {
    const wsUrl = `ws://your-domain.com/api/v1/notifications/ws?token=${this.token}`;
    this.ws = new WebSocket(wsUrl);
    
    this.ws.onopen = () => {
      console.log('WebSocket连接成功');
    };
    
    this.ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      this.handleWebSocketMessage(message);
    };
    
    this.ws.onclose = () => {
      console.log('WebSocket连接断开，3秒后重连...');
      setTimeout(() => this.connectWebSocket(), 3000);
    };
  }

  // 处理WebSocket消息
  handleWebSocketMessage(message) {
    switch (message.type) {
      case 'new_notification':
        this.showNotification(message.data);
        this.updateUnreadCount(message.data.unread_count);
        break;
      case 'unread_count':
        this.updateUnreadCount(message.data.unread_count);
        break;
      case 'system_notification':
        this.showSystemNotification(message.data);
        break;
    }
  }

  // 获取通知列表
  async getNotifications(status = null, limit = 20, offset = 0) {
    try {
      const params = { limit, offset };
      if (status) params.status = status;
      
      const response = await this.api.get('/notifications/', { 
        params,
        headers: { Authorization: `Bearer ${this.token}` }
      });
      
      return response.data.data;
    } catch (error) {
      console.error('获取通知失败:', error);
      throw error;
    }
  }

  // 标记为已读
  async markAsRead(notificationId) {
    try {
      const response = await this.api.patch(
        `/notifications/${notificationId}/read`,
        {},
        { headers: { Authorization: `Bearer ${this.token}` } }
      );
      return response.data.data;
    } catch (error) {
      console.error('标记已读失败:', error);
      throw error;
    }
  }

  // 标记所有为已读
  async markAllAsRead() {
    try {
      const response = await this.api.patch(
        '/notifications/read-all',
        {},
        { headers: { Authorization: `Bearer ${this.token}` } }
      );
      return response.data.data.count;
    } catch (error) {
      console.error('批量标记已读失败:', error);
      throw error;
    }
  }

  // 显示通知（使用浏览器通知API）
  showNotification(notification) {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/icon.png',
        tag: `notification-${notification.id}`
      });
    }
  }

  // 更新未读数量显示
  updateUnreadCount(count) {
    const badge = document.getElementById('notification-badge');
    if (badge) {
      badge.textContent = count > 0 ? count : '';
      badge.style.display = count > 0 ? 'inline' : 'none';
    }
  }

  // 显示系统通知（弹窗或Toast）
  showSystemNotification(data) {
    // 使用你的UI组件库显示系统通知
    alert(`系统通知: ${data.message}`);
  }
}

export default NotificationService;
```

### 2. 在组件中使用

```javascript
// NotificationList.jsx
import React, { useState, useEffect } from 'react';
import NotificationService from './notificationService';

const NotificationList = () => {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(true);
  
  const notificationService = new NotificationService();

  useEffect(() => {
    // 初始化WebSocket
    notificationService.connectWebSocket();
    
    // 获取通知列表
    loadNotifications();
    
    // 获取未读数量
    loadUnreadCount();
    
    return () => {
      if (notificationService.ws) {
        notificationService.ws.close();
      }
    };
  }, []);

  const loadNotifications = async () => {
    try {
      setLoading(true);
      const data = await notificationService.getNotifications('unread', 10, 0);
      setNotifications(data.notifications);
    } catch (error) {
      console.error('加载通知失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadUnreadCount = async () => {
    try {
      const response = await notificationService.api.get('/notifications/unread-count', {
        headers: { Authorization: `Bearer ${notificationService.token}` }
      });
      setUnreadCount(response.data.data.unread_count);
    } catch (error) {
      console.error('获取未读数量失败:', error);
    }
  };

  const handleMarkAsRead = async (notificationId) => {
    try {
      await notificationService.markAsRead(notificationId);
      // 重新加载通知列表
      loadNotifications();
      loadUnreadCount();
    } catch (error) {
      console.error('标记已读失败:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await notificationService.markAllAsRead();
      loadNotifications();
      loadUnreadCount();
    } catch (error) {
      console.error('批量标记已读失败:', error);
    }
  };

  if (loading) return <div>加载中...</div>;

  return (
    <div className="notification-list">
      <div className="notification-header">
        <h3>通知中心</h3>
        <span className="unread-badge" id="notification-badge">
          {unreadCount}
        </span>
        {unreadCount > 0 && (
          <button onClick={handleMarkAllAsRead}>
            标记所有为已读
          </button>
        )}
      </div>
      
      <ul>
        {notifications.map(notification => (
          <li key={notification.id} className={`notification-item ${notification.status}`}>
            <div className="notification-content">
              <h4>{notification.title}</h4>
              <p>{notification.message}</p>
              {notification.action_url && (
                <a href={notification.action_url}>查看详情</a>
              )}
              <span className="notification-time">
                {new Date(notification.created_at).toLocaleString()}
              </span>
            </div>
            {notification.status === 'UNREAD' && (
              <button 
                onClick={() => handleMarkAsRead(notification.id)}
                className="mark-read-btn"
              >
                标记为已读
              </button>
            )}
          </li>
        ))}
      </ul>
      
      {notifications.length === 0 && (
        <p>暂无通知</p>
      )}
    </div>
  );
};

export default NotificationList;
```

### 3. 通知推送流程

```mermaid
sequenceDiagram
    participant A as 后端系统
    participant B as 数据库
    participant C as WebSocket服务器
    participant D as 前端客户端
    participant E as 浏览器通知

    Note over A,D: 1. 创建通知
    A->>B: 保存通知记录
    A->>C: 触发WebSocket事件
    C->>D: 推送新通知消息
    D->>E: 显示浏览器通知
    D->>D: 更新UI状态

    Note over A,D: 2. 用户交互
    D->>A: 标记为已读请求
    A->>B: 更新通知状态
    A->>C: 更新未读计数
    C->>D: 发送未读数量更新

    Note over A,D: 3. 广播通知
    A->>B: 为所有用户创建通知
    A->>C: 广播系统消息
    C->>D: 所有在线客户端接收
    D->>E: 显示紧急通知
```

## 错误码说明

| 错误码 | 描述 | HTTP状态码 | 解决方案 |
|--------|------|------------|----------|
| NOTIFICATION_LIST_001 | 获取通知列表失败 | 500 | 检查服务器日志，联系管理员 |
| NOTIFICATION_READ_001 | 通知不存在或无权限 | 404 | 检查notification_id参数 |
| NOTIFICATION_BROADCAST_001 | 广播通知失败 | 500 | 检查管理员权限和系统状态 |
| AUTH_001 | 认证失败 | 401 | 检查JWT Token有效性 |
| RATE_LIMIT_001 | 请求频率过高 | 429 | 降低请求频率 |

## 最佳实践

1. **WebSocket连接管理**: 实现自动重连机制，处理网络中断
2. **离线通知**: 结合推送服务（如Firebase）处理WebSocket断开情况
3. **性能优化**: 使用分页加载通知，避免一次性加载过多数据
4. **用户体验**: 提供通知静音、批量操作等功能
5. **数据安全**: 敏感通知内容需加密传输和存储

## 安全考虑

- 所有接口需JWT认证，广播接口需超级管理员权限
- WebSocket连接需验证Token有效性
- 通知内容长度限制，防止SQL注入和XSS攻击
- 实现通知过期机制，定期清理过期数据

此文档将根据API更新进行维护。如有疑问，请联系后端开发团队。
