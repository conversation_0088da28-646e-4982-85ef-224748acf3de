version: '3.8'

services:
  redis-master:
    image: redis:7-alpine
    container_name: redis-master
    command: redis-server
    ports:
      - "6379:6379"
    volumes:
      - redis_master_data:/data

  redis-slave-1:
    image: redis:7-alpine
    container_name: redis-slave-1
    command: redis-server --slaveof redis-master 6379
    depends_on:
      - redis-master
    ports:
      - "6380:6379"

  redis-slave-2:
    image: redis:7-alpine
    container_name: redis-slave-2
    command: redis-server --slaveof redis-master 6379
    depends_on:
      - redis-master
    ports:
      - "6381:6379"

volumes:
  redis_master_data:
