"""Pytest配置文件，用于设置测试环境"""

import asyncio
import os
import sys
from collections.abc import AsyncGenerator

# 将项目根目录添加到 sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))


import pytest

from app.db.redis import close_redis, init_redis


@pytest.fixture(scope="session")
def event_loop():
    """为整个测试会话创建一个事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


import pytest_asyncio


@pytest_asyncio.fixture(scope="function", autouse=True)
async def redis_lifespan() -> AsyncGenerator[None, None]:
    """在每个测试函数运行前后初始化和关闭Redis连接"""
    await init_redis()
    yield
    await close_redis()
