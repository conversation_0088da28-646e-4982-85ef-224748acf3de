"""DeviceTrustService 单元测试"""

from datetime import UTC, datetime
from unittest.mock import AsyncMock, MagicMock

import pytest

from app.services.device_service import DeviceTrustService
from tests.mocks.mock_token_service import MockTokenService


@pytest.fixture
def mock_user_device_crud():
    return AsyncMock()


@pytest.fixture
def device_trust_service(mock_user_device_crud):
    service = DeviceTrustService(
        token_service=MockTokenService(), user_device_crud=mock_user_device_crud
    )
    return service


@pytest.mark.asyncio
async def test_evaluate_device_trust_new_device(device_trust_service, mock_user_device_crud):
    """测试评估新设备的信任度"""
    mock_user_device_crud.get_by_fingerprint.return_value = None
    mock_user_device_crud.create.return_value = MagicMock(
        id=1,
        trust_score=20,
        is_new_device=True,
        is_trusted=False,
        is_blocked=False,
        last_login_at=None,
        login_count=1,
    )
    user = MagicMock(id=1)
    request = MagicMock()
    request.headers.get.return_value = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36"

    evaluation = await device_trust_service.evaluate_device_trust(user, request, db=AsyncMock())

    assert evaluation.requires_verification is True
    assert evaluation.trust_score == 20


@pytest.mark.asyncio
async def test_evaluate_device_trust_trusted_device(device_trust_service, mock_user_device_crud):
    """测试评估受信任设备的信任度"""
    mock_user_device_crud.get_by_fingerprint.return_value = MagicMock(
        id=1,
        trust_score=80,
        is_new_device=False,
        is_trusted=True,
        is_blocked=False,
        last_login_at=datetime.now(UTC),
        login_count=10,
    )
    user = MagicMock(id=1)
    request = MagicMock()
    request.headers.get.return_value = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36"

    evaluation = await device_trust_service.evaluate_device_trust(user, request, db=AsyncMock())

    assert evaluation.is_trusted is True
    assert evaluation.requires_verification is False
