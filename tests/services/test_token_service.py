"""TokenService 单元测试 - 重构版"""

import json
from unittest.mock import AsyncMock, patch

import pytest
from jose import ExpiredSignatureError

from app.services.token_service import TokenService


@pytest.fixture
def token_service():
    """提供一个 TokenService 实例"""
    return TokenService()


@pytest.mark.asyncio
@patch("app.services.token_service.set_key", new_callable=AsyncMock)
@patch("app.services.token_service.set_add", new_callable=AsyncMock)
@patch("app.services.token_service.set_key_expire", new_callable=AsyncMock)
@patch("app.services.token_service.get_key", new_callable=AsyncMock, return_value=None)
@patch("app.services.token_service.jwt.encode", return_value="test_token")
async def test_create_token_success(
    mock_jwt_encode,
    mock_get_key,
    mock_set_key_expire,
    mock_set_add,
    mock_set_key,
    token_service,
):
    """测试成功创建token"""
    username = "testuser"
    device_id = 123

    token = await token_service.create_token(username, device_id)

    assert token == "test_token"
    mock_jwt_encode.assert_called_once()
    mock_set_key.assert_called()
    mock_set_add.assert_called()
    mock_set_key_expire.assert_called()
    mock_get_key.assert_called_with(f"device_token:{username}:{device_id}")


@pytest.mark.asyncio
@patch("app.services.token_service.get_key")
@patch("app.services.token_service.jwt.decode")
async def test_verify_token_success(mock_jwt_decode, mock_get_key, token_service):
    """测试成功验证token"""
    mock_token = "valid_token"
    mock_payload = {"sub": "testuser", "device_id": 123}
    mock_redis_info = {"username": "testuser", "device_id": 123}

    mock_get_key.return_value = json.dumps(mock_redis_info)
    mock_jwt_decode.return_value = mock_payload

    payload = await token_service.verify_token(mock_token)

    assert payload is not None
    assert payload["sub"] == "testuser"
    mock_get_key.assert_called_with(f"token:{mock_token}")
    mock_jwt_decode.assert_called()


@pytest.mark.asyncio
@patch("app.services.token_service.get_key")
@patch("app.services.token_service.jwt.decode", side_effect=ExpiredSignatureError)
@patch("app.services.token_service.delete_key", new_callable=AsyncMock)
async def test_verify_token_expired(mock_delete_key, mock_jwt_decode, mock_get_key, token_service):
    """测试验证过期的token"""
    mock_token = "expired_token"
    mock_redis_info = {"username": "testuser", "device_id": 123}
    mock_get_key.return_value = json.dumps(mock_redis_info)

    payload = await token_service.verify_token(mock_token)

    assert payload is None
    # 验证是否调用了删除key的逻辑
    mock_delete_key.assert_called()


@pytest.mark.asyncio
@patch("app.services.token_service.get_key")
@patch("app.services.token_service.delete_key", new_callable=AsyncMock)
async def test_revoke_token_success(mock_delete_key, mock_get_key, token_service):
    """测试成功撤销token"""
    mock_token = "valid_token_to_revoke"
    mock_redis_info = {"username": "testuser", "device_id": 123}
    mock_get_key.side_effect = [
        json.dumps(mock_redis_info),  # 第一次调用，用于撤销
        mock_token,  # 第二次调用，用于检查设备token
    ]

    with patch("app.services.token_service.set_remove", new_callable=AsyncMock) as mock_set_remove:
        result = await token_service.revoke_token(mock_token)

        assert result is True
        assert mock_delete_key.call_count == 2
        mock_set_remove.assert_called_once()


@pytest.mark.asyncio
@patch("app.services.token_service.set_get_all_members", new_callable=AsyncMock)
@patch("app.services.token_service.TokenService.revoke_token", new_callable=AsyncMock)
async def test_revoke_user_tokens(mock_revoke_token, mock_set_get_all_members, token_service):
    """测试撤销用户的所有token"""
    username = "user_to_revoke"
    tokens = {"token1", "token2", "token3"}
    mock_set_get_all_members.return_value = tokens
    mock_revoke_token.return_value = True

    with patch("app.services.token_service.delete_key", new_callable=AsyncMock) as mock_delete_key:
        count = await token_service.revoke_user_tokens(username)

        assert count == len(tokens)
        assert mock_revoke_token.call_count == len(tokens)
        mock_delete_key.assert_called_with(f"user_tokens:{username}")
