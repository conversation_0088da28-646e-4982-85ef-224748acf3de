"""AuthOrchestratorService 单元测试"""

from unittest.mock import AsyncMock, MagicMock

import pytest

from app.core.enums import AuthType
from app.services.auth_orchestrator_service import AuthOrchestratorService
from tests.mocks.mock_token_service import MockTokenService


@pytest.fixture
def mock_sms_auth_service():
    return AsyncMock()


@pytest.fixture
def mock_wechat_auth_service():
    return AsyncMock()


@pytest.fixture
def mock_device_trust_service():
    return AsyncMock()


@pytest.fixture
def mock_user_management_service():
    return AsyncMock()


@pytest.fixture
def auth_orchestrator_service(
    mock_sms_auth_service,
    mock_wechat_auth_service,
    mock_device_trust_service,
    mock_user_management_service,
):
    service = AuthOrchestratorService(
        token_service=MockTokenService(),
        device_trust_service=mock_device_trust_service,
        user_management_service=mock_user_management_service,
        sms_auth_service=mock_sms_auth_service,
        wechat_auth_service=mock_wechat_auth_service,
    )
    return service


@pytest.mark.asyncio
async def test_initiate_sms_authentication(auth_orchestrator_service, mock_sms_auth_service):
    """测试发起SMS认证"""
    mock_sms_auth_service.send_verification_code.return_value = MagicMock(
        success=True, message="OK"
    )
    request_data = {"phone": "13800138000"}

    response = await auth_orchestrator_service.initiate_authentication(
        auth_type=AuthType.SMS, request_data=request_data
    )

    assert response.success is True
    mock_sms_auth_service.send_verification_code.assert_called_once()


@pytest.mark.asyncio
async def test_initiate_wechat_authentication(auth_orchestrator_service, mock_wechat_auth_service):
    """测试发起微信认证"""
    mock_wechat_auth_service.create_qr_code.return_value = MagicMock(
        scene_str="scene", qr_url="url", expire_seconds=60
    )
    request_data = {}

    response = await auth_orchestrator_service.initiate_authentication(
        auth_type=AuthType.WECHAT, request_data=request_data
    )

    assert response.success is True
    assert response.data["scene_str"] == "scene"
    mock_wechat_auth_service.create_qr_code.assert_called_once()
