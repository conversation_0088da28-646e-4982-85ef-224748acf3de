"""
短信认证服务测试

测试短信验证码发送功能，包括：
- 正常发送流程
- 手机号验证
- 错误处理
- 响应格式验证
"""

import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from app.services.sms_auth_service import SMSAuthService
from app.services.interfaces.token_service_interface import ITokenService
from app.services.interfaces.device_service_interface import IDeviceTrustService
from app.services.interfaces.user_management_service_interface import IUserManagementService
from app.services.user_aggregation_service import UserAggregationService
from app.services.sms_service import SmsService
from app.core.sms_template import SmsTemplate
from app.schemas.auth import SMSVerificationResponse
from app.exceptions.auth import SMSVerificationError, RateLimitError


class TestSMSAuthService:
    """短信认证服务测试类"""
    
    @pytest.fixture
    def mock_token_service(self):
        """模拟令牌服务"""
        mock = AsyncMock(spec=ITokenService)
        mock.create_access_token = AsyncMock(return_value="test_token")
        return mock
    
    @pytest.fixture
    def mock_device_service(self):
        """模拟设备信任服务"""
        mock = AsyncMock(spec=IDeviceTrustService)
        return mock
    
    @pytest.fixture
    def mock_user_management_service(self):
        """模拟用户管理服务"""
        mock = AsyncMock(spec=IUserManagementService)
        return mock
    
    @pytest.fixture
    def mock_user_aggregation_service(self):
        """模拟用户聚合服务"""
        mock = AsyncMock(spec=UserAggregationService)
        mock.get_user_profile = AsyncMock(return_value=None)
        return mock
    
    @pytest.fixture
    def mock_sms_service(self):
        """模拟短信服务"""
        mock = AsyncMock(spec=SmsService)
        mock.send_verification_code = AsyncMock()
        mock.verify_code = AsyncMock(return_value=True)
        return mock
    
    @pytest.fixture
    def sms_auth_service(self, mock_token_service, mock_device_service, 
                         mock_user_management_service, mock_user_aggregation_service):
        """创建短信认证服务实例"""
        with patch('app.services.sms_auth_service.SmsService') as mock_sms_class:
            mock_sms_class.return_value = AsyncMock(spec=SmsService)
            
            service = SMSAuthService(
                token_service=mock_token_service,
                device_trust_service=mock_device_service,
                user_management_service=mock_user_management_service,
                user_aggregation_service=mock_user_aggregation_service
            )
            return service
    
    @pytest.mark.asyncio
    async def test_send_verification_code_success(self, sms_auth_service):
        """测试成功发送验证码"""
        # 准备测试数据
        phone = "13800138000"
        template_type = SmsTemplate.LOGIN
        
        # 模拟短信服务发送成功
        sms_auth_service.sms_service.send_verification_code = AsyncMock()
        
        # 执行测试
        result = await sms_auth_service.send_verification_code(phone, template_type)
        
        # 验证结果
        assert isinstance(result, SMSVerificationResponse)
        assert result.success is True
        assert result.status == "success"
        assert result.message == "验证码发送成功，请查收短信"
        assert result.data is None
        assert result.timestamp is not None
        
        # 验证短信服务被调用
        sms_auth_service.sms_service.send_verification_code.assert_called_once_with(
            phone, template_type
        )
    
    @pytest.mark.asyncio
    async def test_send_verification_code_invalid_phone(self, sms_auth_service):
        """测试无效手机号"""
        # 准备测试数据
        invalid_phones = ["", "123", "1380013800", "138001380001", "abc12345678"]
        
        for phone in invalid_phones:
            with pytest.raises(SMSVerificationError) as exc_info:
                await sms_auth_service.send_verification_code(phone, SmsTemplate.LOGIN)
            
            assert "手机号" in str(exc_info.value.message)
    
    @pytest.mark.asyncio
    async def test_send_verification_code_rate_limit(self, sms_auth_service):
        """测试频率限制错误 - 现在应该直接抛出异常"""
        from app.exceptions.auth import RateLimitError
        
        # 模拟频率限制错误
        sms_auth_service.sms_service.send_verification_code.side_effect = ValueError(
            "发送过于频繁"
        )
        
        # 执行测试 - 现在应该直接抛出 ValueError
        with pytest.raises(ValueError) as exc_info:
            await sms_auth_service.send_verification_code("13800138000", SmsTemplate.LOGIN)
        
        assert "发送过于频繁" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_send_verification_code_service_error(self, sms_auth_service):
        """测试短信服务错误"""
        # 模拟短信服务错误 - 现在由底层服务直接抛出异常
        sms_auth_service.sms_service.send_verification_code.side_effect = ValueError(
            "短信服务不可用"
        )
        
        # 执行测试 - 现在应该直接抛出 ValueError
        with pytest.raises(ValueError) as exc_info:
            await sms_auth_service.send_verification_code("13800138000", SmsTemplate.LOGIN)
        
        assert "短信服务不可用" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_send_verification_code_general_exception(self, sms_auth_service):
        """测试通用异常处理"""
        # 模拟通用异常
        sms_auth_service.sms_service.send_verification_code.side_effect = Exception(
            "未知错误"
        )
        
        # 执行测试 - 现在应该直接抛出 Exception
        with pytest.raises(Exception) as exc_info:
            await sms_auth_service.send_verification_code("13800138000", SmsTemplate.LOGIN)
        
        assert "未知错误" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_validate_phone_number_valid(self, sms_auth_service):
        """测试有效手机号验证"""
        valid_phones = ["13800138000", "13912345678", "15098765432", "18888888888"]
        
        for phone in valid_phones:
            result = sms_auth_service._validate_phone_number(phone)
            assert result == phone
    
    @pytest.mark.asyncio
    async def test_validate_phone_number_empty(self, sms_auth_service):
        """测试空手机号"""
        with pytest.raises(SMSVerificationError) as exc_info:
            sms_auth_service._validate_phone_number("")
        
        assert "手机号不能为空" in str(exc_info.value.message)
        assert exc_info.value.details["phone"] == ""
    
    @pytest.mark.asyncio
    async def test_validate_phone_number_invalid_format(self, sms_auth_service):
        """测试无效手机号格式"""
        invalid_phones = ["12345678901", "1380013800", "138001380001", "abc12345678"]
        
        for phone in invalid_phones:
            with pytest.raises(SMSVerificationError) as exc_info:
                sms_auth_service._validate_phone_number(phone)
            
            assert "手机号格式不正确" in str(exc_info.value.message)
            assert exc_info.value.details["phone"] == phone


@pytest.mark.asyncio
async def test_sms_verification_response_schema():
    """测试 SMSVerificationResponse schema"""
    from app.schemas.auth import SMSVerificationResponse
    
    # 测试成功响应
    response = SMSVerificationResponse(
        success=True,
        status="success",
        message="验证码发送成功",
        data=None,
        timestamp=datetime.now().isoformat()
    )
    
    assert response.success is True
    assert response.status == "success"
    assert response.message == "验证码发送成功"
    assert response.data is None
    assert response.timestamp is not None


if __name__ == "__main__":
    # 运行简单测试
    print("🧪 运行短信认证服务测试...")
    
    async def run_basic_tests():
        try:
            # 测试 schema
            await test_sms_verification_response_schema()
            print("✅ SMSVerificationResponse schema 测试通过")
            
            # 测试服务实例化
            from app.services.service_factory import get_sms_auth_service
            service = get_sms_auth_service()
            print("✅ SMSAuthService 实例化成功")
            
            # 测试手机号验证
            test_cases = [
                ("13800138000", True),
                ("", False),
                ("123", False),
                ("138001380001", False),
            ]
            
            for phone, should_pass in test_cases:
                try:
                    result = service._validate_phone_number(phone)
                    if should_pass:
                        print(f"✅ 手机号 {phone} 验证通过")
                    else:
                        print(f"❌ 手机号 {phone} 应该验证失败")
                except SMSVerificationError:
                    if not should_pass:
                        print(f"✅ 手机号 {phone} 正确拒绝")
                    else:
                        print(f"❌ 手机号 {phone} 应该验证通过")
            
            print("🎉 基础测试完成！")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    asyncio.run(run_basic_tests())