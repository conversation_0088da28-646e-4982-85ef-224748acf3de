"""
背包功能测试
"""
import base64
import pytest

from app.models.backpack import BackpackItem, BackpackItemType
from app.schemas.backpack import BackpackItemCreate
from app.services.backpack_storage import backpack_storage


class TestBackpackModels:
    """测试背包模型"""
    
    def test_backpack_item_type_enum(self):
        """测试背包项目类型枚举"""
        assert BackpackItemType.COSTUME == "costume"
        assert BackpackItemType.SOUND == "sound"
        assert BackpackItemType.SPRITE == "sprite"
        assert BackpackItemType.SCRIPT == "script"
    
    def test_backpack_item_properties(self):
        """测试背包项目属性"""
        item = BackpackItem(
            username="testuser",
            type=BackpackItemType.COSTUME,
            mime="image/png",
            name="Test Costume",
            body_path="backpack/objects/test.png",
            thumbnail_path="backpack/thumbnails/test.jpg",
            size_bytes=1024
        )
        
        assert item.thumbnail == "backpack/thumbnails/test.jpg"
        assert item.body == "backpack/objects/test.png"
        assert item.size == 1024


class TestBackpackSchemas:
    """测试背包schemas"""
    
    def test_backpack_item_create_validation(self):
        """测试创建背包项目的验证"""
        # 有效的Base64数据
        test_data = base64.b64encode(b"test data").decode()
        
        item_data = BackpackItemCreate(
            type=BackpackItemType.COSTUME,
            mime="image/png",
            name="Test Item",
            body=test_data,
            thumbnail=test_data
        )
        
        assert item_data.type == BackpackItemType.COSTUME
        assert item_data.mime == "image/png"
        assert item_data.name == "Test Item"
    
    def test_backpack_item_create_invalid_base64(self):
        """测试无效Base64数据的验证"""
        with pytest.raises(ValueError, match="无效的Base64编码"):
            BackpackItemCreate(
                type=BackpackItemType.COSTUME,
                mime="image/png",
                name="Test Item",
                body="invalid_base64",
                thumbnail="invalid_base64"
            )
    
    def test_backpack_item_create_type_mime_validation(self):
        """测试类型与MIME类型兼容性验证"""
        test_data = base64.b64encode(b"test data").decode()
        
        # 不兼容的类型和MIME
        with pytest.raises(ValueError, match="类型 costume 不支持MIME类型 audio/mp3"):
            BackpackItemCreate(
                type=BackpackItemType.COSTUME,
                mime="audio/mp3",  # 音频MIME类型不适用于costume
                name="Test Item",
                body=test_data,
                thumbnail=test_data
            )


class TestBackpackStorage:
    """测试背包存储服务"""

    def test_validate_mime_type(self):
        """测试MIME类型验证"""
        # 有效的组合
        assert backpack_storage.validate_mime_type("image/png", BackpackItemType.COSTUME)
        assert backpack_storage.validate_mime_type("audio/mp3", BackpackItemType.SOUND)
        assert backpack_storage.validate_mime_type("application/zip", BackpackItemType.SPRITE)
        assert backpack_storage.validate_mime_type("application/json", BackpackItemType.SCRIPT)

        # 无效的组合
        assert not backpack_storage.validate_mime_type("audio/mp3", BackpackItemType.COSTUME)
        assert not backpack_storage.validate_mime_type("image/png", BackpackItemType.SOUND)

    def test_get_file_extension(self):
        """测试文件扩展名获取"""
        storage = backpack_storage

        # 测试不同MIME类型的扩展名
        assert storage._get_file_extension("image/png", BackpackItemType.COSTUME) == ".png"
        assert storage._get_file_extension("audio/mp3", BackpackItemType.SOUND) == ".mp3"
        assert storage._get_file_extension("application/json", BackpackItemType.SCRIPT) == ".json"

        # Sprite类型强制使用.dat
        assert storage._get_file_extension("application/zip", BackpackItemType.SPRITE) == ".dat"
        assert storage._get_file_extension("application/octet-stream", BackpackItemType.SPRITE) == ".dat"

    def test_validate_file_size(self):
        """测试文件大小验证"""
        storage = backpack_storage

        # 正常大小的文件
        small_data = b"a" * 1024  # 1KB
        storage._validate_file_size(small_data)  # 应该不抛出异常

        # 超大文件 - 注意：这里使用配置中的文件大小限制
        large_data = b"a" * (11 * 1024 * 1024)  # 11MB
        with pytest.raises(ValueError, match="文件大小超过限制"):
            storage._validate_file_size(large_data)

    def test_get_file_url(self):
        """测试文件URL获取 - OSS CDN模式"""
        storage = backpack_storage

        # 测试OSS对象路径
        oss_path = "backpack/objects/test.png"
        url = storage.get_file_url(oss_path)
        # 在实际环境中会使用CDN域名，测试环境返回相对路径
        assert url == "/backpack/objects/test.png"

        # 测试空路径
        assert storage.get_file_url("") == ""

        # 测试已经是完整URL的情况
        full_url = "https://example.com/test.png"
        assert storage.get_file_url(full_url) == full_url

    def test_calculate_file_hash(self):
        """测试文件哈希计算"""
        storage = backpack_storage

        # 测试相同内容产生相同哈希
        data1 = b"test content"
        data2 = b"test content"
        hash1 = storage._calculate_file_hash(data1)
        hash2 = storage._calculate_file_hash(data2)
        assert hash1 == hash2

        # 测试不同内容产生不同哈希
        data3 = b"different content"
        hash3 = storage._calculate_file_hash(data3)
        assert hash1 != hash3

        # 测试哈希长度（SHA256应该是64个字符）
        assert len(hash1) == 64


class TestBackpackIntegration:
    """背包功能集成测试"""
    
    def test_create_backpack_item_flow(self):
        """测试创建背包项目的完整流程"""
        # 准备测试数据
        test_body = b"test image data"
        test_thumbnail = b"test thumbnail data"
        
        body_base64 = base64.b64encode(test_body).decode()
        thumbnail_base64 = base64.b64encode(test_thumbnail).decode()
        
        # 创建请求数据
        item_data = BackpackItemCreate(
            type=BackpackItemType.COSTUME,
            mime="image/png",
            name="Test Costume",
            body=body_base64,
            thumbnail=thumbnail_base64
        )
        
        # 验证数据有效性
        assert item_data.type == BackpackItemType.COSTUME
        assert item_data.mime == "image/png"
        assert item_data.name == "Test Costume"
        
        # 验证MIME类型兼容性
        assert backpack_storage.validate_mime_type(item_data.mime, item_data.type)


if __name__ == "__main__":
    pytest.main([__file__])
