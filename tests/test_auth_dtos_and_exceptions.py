"""测试认证DTOs和异常处理"""

from datetime import datetime, timedelta
from unittest.mock import Mock

from app.exceptions.auth import (
    AuthenticationError,
    AuthErrorCodes,
    AuthErrorMessages,
    DeviceNotTrustedError,
    DeviceVerificationRequiredError,
    DeviceVerificationError,
    SMSRateLimitError,
    SMSInvalidCodeError,
    SMSCodeExpiredError,
    SMSVerificationError,
    WeChatAPIError,
    WeChatAuthError,
    WeChatInvalidOpenIDError,
    RateLimitError,
)
from app.core.response_wrapper import ResponseCode
from app.utils.auth_helpers import AuthConstants, AuthValidator, AuthResponseBuilder
from app.schemas.auth import (
    AuthCompletionResponse,
    AuthenticationResult,
    DeviceTrustEvaluation,
    DeviceVerificationPayload,
    WeChatScanStatus,
)


class TestAuthDTOs:
    """测试认证数据传输对象"""
    
    def test_authentication_result_creation(self):
        """测试AuthenticationResult创建"""
        result = AuthenticationResult(
            success=True,
            message="认证成功",
            auth_method="sms"
        )
        
        assert result.success is True
        assert result.message == "认证成功"
        assert result.auth_method == "sms"
        assert result.user is None
        assert result.access_token is None
        assert result.requires_device_verification is False
        assert result.verification_token is None
    
    def test_device_trust_evaluation_creation(self):
        """测试DeviceTrustEvaluation创建"""
        mock_device = Mock()
        mock_device.id = 1
        mock_device.device_fingerprint = "test_fingerprint"
        
        evaluation = DeviceTrustEvaluation(
            device=mock_device,
            is_trusted=False,
            requires_verification=True,
            trust_score=30,
            risk_factors=["new_device", "different_location"]
        )
        
        assert evaluation.device == mock_device
        assert evaluation.is_trusted is False
        assert evaluation.requires_verification is True
        assert evaluation.trust_score == 30
        assert "new_device" in evaluation.risk_factors
        assert "different_location" in evaluation.risk_factors
    
    def test_device_verification_payload_creation(self):
        """测试DeviceVerificationPayload创建"""
        now = datetime.now()
        expires_at = now + timedelta(minutes=30)
        
        payload = DeviceVerificationPayload(
            user_id=1,
            device_id=2,
            device_fingerprint="test_fingerprint",
            issued_at=now,
            expires_at=expires_at
        )
        
        assert payload.user_id == 1
        assert payload.device_id == 2
        assert payload.device_fingerprint == "test_fingerprint"
        assert payload.issued_at == now
        assert payload.expires_at == expires_at
    
    def test_auth_completion_response_pydantic_model(self):
        """测试AuthCompletionResponse Pydantic模型"""
        response = AuthCompletionResponse(
            success=True,
            message="认证成功",
            auth_method="wechat"
        )
        
        assert response.success is True
        assert response.message == "认证成功"
        assert response.auth_method == "wechat"
        assert response.token_type == "bearer"
        assert response.requires_device_verification is False
    
    def test_wechat_scan_status_creation(self):
        """测试WeChatScanStatus创建"""
        status = WeChatScanStatus(
            status="scanned",
            openid="test_openid",
            message="已扫码，等待确认"
        )
        
        assert status.status == "scanned"
        assert status.openid == "test_openid"
        assert status.message == "已扫码，等待确认"
        assert status.user_info is None


class TestAuthExceptions:
    """测试认证异常类"""
    
    def test_authentication_error_creation(self):
        """测试AuthenticationError创建"""
        error = AuthenticationError(
            message="认证失败",
            response_code=ResponseCode.AUTH_USER_NOT_FOUND,
            details={"reason": "invalid_credentials"}
        )
        
        assert error.message == "认证失败"
        assert error.response_code == ResponseCode.AUTH_USER_NOT_FOUND
        assert error.details["reason"] == "invalid_credentials"
    
    def test_device_verification_error_creation(self):
        """测试DeviceVerificationError创建"""
        error = DeviceVerificationError(
            message="设备验证失败",
            details={"device_id": 123}
        )
        
        assert error.message == "设备验证失败"
        assert error.response_code == ResponseCode.AUTH_DEVICE_VERIFICATION_FAILED
        assert error.details["device_id"] == 123
    
    def test_sms_verification_error_creation(self):
        """测试SMSVerificationError创建"""
        error = SMSVerificationError(
            message="验证码错误",
            details={"phone": "13800138000", "retry_after": 60}
        )
        
        assert error.message == "验证码错误"
        assert error.response_code == ResponseCode.AUTH_SMS_INVALID_CODE
        assert error.details["phone"] == "13800138000"
        assert error.details["retry_after"] == 60
    
    def test_wechat_auth_error_creation(self):
        """测试WeChatAuthError创建"""
        error = WeChatAuthError(
            message="微信认证失败",
            details={"openid": "test_openid", "scene_str": "test_scene"}
        )
        
        assert error.message == "微信认证失败"
        assert error.response_code == ResponseCode.AUTH_WECHAT_API_ERROR
        assert error.details["openid"] == "test_openid"
        assert error.details["scene_str"] == "test_scene"
    
    def test_rate_limit_error_creation(self):
        """测试RateLimitError创建"""
        error = RateLimitError(
            message="请求频率过高",
            details={"retry_after": 300}
        )
        
        assert error.message == "请求频率过高"
        assert error.response_code == ResponseCode.AUTH_SMS_RATE_LIMIT
        assert error.details["retry_after"] == 300


class TestAuthErrorCodes:
    """测试认证错误码"""
    
    def test_error_codes_exist(self):
        """测试错误码常量存在"""
        assert hasattr(AuthErrorCodes, "USER_NOT_FOUND")
        assert hasattr(AuthErrorCodes, "DEVICE_VERIFICATION_REQUIRED")
        assert hasattr(AuthErrorCodes, "INVALID_VERIFICATION_CODE")
        assert hasattr(AuthErrorCodes, "WECHAT_API_ERROR")
        assert hasattr(AuthErrorCodes, "INVALID_TOKEN")
    
    def test_error_messages_exist(self):
        """测试错误消息存在"""
        message = AuthErrorMessages.get_message(AuthErrorCodes.USER_NOT_FOUND)
        assert message == "用户不存在"
        
        message = AuthErrorMessages.get_message(AuthErrorCodes.INVALID_VERIFICATION_CODE)
        assert message == "验证码错误"
        
        # 测试不存在的错误码
        message = AuthErrorMessages.get_message("NONEXISTENT_CODE", "默认消息")
        assert message == "默认消息"


class TestAuthUtils:
    """测试认证工具函数"""
    
    def test_auth_response_builder_success(self):
        """测试成功响应构建"""
        # 创建一个简单的用户对象，不使用Mock以避免Pydantic验证问题
        from datetime import datetime

        from app.schemas.user import UserAggregated, UserStats
        
        user_stats = UserStats(
            article_count=0,
            video_count=0,
            follower_count=0,
            following_count=0
        )
        
        mock_user = UserAggregated(
            id=1,
            username="13800138000",
            role_id=1,
            nickname="测试用户",
            description="测试描述",
            email="<EMAIL>",
            avatar=None,
            cover=None,
            is_active=True,
            is_superuser=False,
            last_login=None,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            likes_privacy_settings=3,
            favorites_privacy_settings=3,
            wechat_openid=None,
            wechat_unionid=None,
            login_type="sms",
            stats=user_stats
        )
        
        response = AuthResponseBuilder.build_success_response(
            user=mock_user,
            access_token="test_token",
            auth_method="sms",
            message="登录成功"
        )
        
        assert response["success"] is True
        assert response["user"]["id"] == 1
        assert response["access_token"] == "test_token"
        assert response["auth_method"] == "sms"
        assert response["message"] == "登录成功"
        assert response["token_type"] == "bearer"
    
    def test_auth_response_builder_device_verification(self):
        """测试设备验证响应构建"""
        response = AuthResponseBuilder.build_device_verification_response(
            verification_token="test_token",
            message="需要设备验证"
        )
        
        assert response["success"] is False
        assert response["requires_device_verification"] is True
        assert response["verification_token"] == "test_token"
        assert response["message"] == "需要设备验证"
    
    def test_auth_response_builder_failure(self):
        """测试失败响应构建"""
        response = AuthResponseBuilder.build_failure_response(
            error_code=AuthErrorCodes.USER_NOT_FOUND,
            auth_method="sms"
        )
        
        assert response["success"] is False
        assert response["message"] == "用户不存在"
        assert response["auth_method"] == "sms"
    
    def test_auth_validator_phone_number(self):
        """测试手机号验证"""
        assert AuthValidator.validate_phone_number("13800138000") is True
        assert AuthValidator.validate_phone_number("18912345678") is True
        assert AuthValidator.validate_phone_number("12345678901") is False  # 不是有效的手机号开头
        assert AuthValidator.validate_phone_number("1380013800") is False   # 长度不对
        assert AuthValidator.validate_phone_number("1380013800a") is False  # 包含非数字
    
    def test_auth_validator_verification_code(self):
        """测试验证码验证"""
        assert AuthValidator.validate_verification_code("123456") is True
        assert AuthValidator.validate_verification_code("000000") is True
        assert AuthValidator.validate_verification_code("12345") is False   # 长度不对
        assert AuthValidator.validate_verification_code("12345a") is False  # 包含非数字
    
    def test_auth_validator_openid(self):
        """测试OpenID验证"""
        assert AuthValidator.validate_openid("oB2W_0abcdefghijklmnopqrstuvwxyz") is True
        assert AuthValidator.validate_openid("short") is False  # 太短
        assert AuthValidator.validate_openid("") is False       # 空字符串
    
    def test_auth_validator_sanitize_phone(self):
        """测试手机号脱敏"""
        sanitized = AuthValidator.sanitize_phone_for_logging("13800138000")
        assert sanitized == "138****8000"
        
        # 非标准手机号不脱敏
        sanitized = AuthValidator.sanitize_phone_for_logging("test_user")
        assert sanitized == "test_user"
    
    def test_auth_validator_sanitize_openid(self):
        """测试OpenID脱敏"""
        sanitized = AuthValidator.sanitize_openid_for_logging("oB2W_0abcdefghijklmnopqrstuvwxyz")
        assert sanitized == "oB2W****wxyz"
        
        # 太短的不脱敏
        sanitized = AuthValidator.sanitize_openid_for_logging("short")
        assert sanitized == "short"


class TestAuthConstants:
    """测试认证常量"""
    
    def test_auth_method_constants(self):
        """测试认证方法常量"""
        assert AuthConstants.AUTH_METHOD_SMS == "sms"
        assert AuthConstants.AUTH_METHOD_WECHAT == "wechat"
        assert AuthConstants.AUTH_METHOD_PASSWORD == "password"
        assert AuthConstants.AUTH_METHOD_DEVICE_VERIFICATION == "device_verification"
    
    def test_device_trust_level_constants(self):
        """测试设备信任级别常量"""
        assert AuthConstants.DEVICE_TRUST_LEVEL_UNKNOWN == 0
        assert AuthConstants.DEVICE_TRUST_LEVEL_LOW == 1
        assert AuthConstants.DEVICE_TRUST_LEVEL_MEDIUM == 2
        assert AuthConstants.DEVICE_TRUST_LEVEL_HIGH == 3
        assert AuthConstants.DEVICE_TRUST_LEVEL_TRUSTED == 4
    
    def test_cache_expire_time_constants(self):
        """测试缓存过期时间常量"""
        assert AuthConstants.SMS_CODE_EXPIRE_TIME == 300
        assert AuthConstants.DEVICE_VERIFICATION_EXPIRE_TIME == 1800
        assert AuthConstants.WECHAT_QR_CODE_EXPIRE_TIME == 600