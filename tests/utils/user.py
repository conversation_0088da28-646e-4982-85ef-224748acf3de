from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models
from app.schemas import UserCreate
from app.utils.common import random_lower_string


async def create_random_user(db: AsyncSession) -> models.User:
    email = f"{random_lower_string()}@example.com"
    password = random_lower_string()
    user_in = UserCreate(email=email, password=password)
    return await crud.user.create(db=db, obj_in=user_in)
