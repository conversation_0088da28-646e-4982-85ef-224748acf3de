from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models
from app.schemas import VideoCreate
from app.utils.common import random_lower_string
from tests.utils.user import create_random_user


async def create_random_video(db: AsyncSession, author_id: int | None = None) -> models.Video:
    if author_id is None:
        user = await create_random_user(db)
        author_id = user.id

    title = random_lower_string()
    description = random_lower_string()
    video_in = VideoCreate(title=title, description=description, author_id=author_id)
    return await crud.video.create(db=db, obj_in=video_in)
