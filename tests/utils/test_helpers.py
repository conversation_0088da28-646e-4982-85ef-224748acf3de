"""
测试工具和Mock服务模块

提供用于API测试的各种工具和Mock对象。
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
from unittest.mock import AsyncMock, MagicMock, Mock
from uuid import uuid4

import pytest
from fastapi import FastAPI, Request
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app.core.response_wrapper import ResponseCode
from app.models.user import User, UserRole
from app.schemas.user import UserAggregated, UserStats
from app.schemas.auth import AuthInitiationResponse, AuthCompletionResponse
from app.schemas.article import ArticleOut
from app.schemas.video import VideoOut


class MockRedis:
    """Mock Redis客户端"""
    
    def __init__(self):
        self.data = {}
        self.expires = {}
    
    async def get(self, key: str) -> Optional[str]:
        if key in self.expires and self.expires[key] < datetime.now():
            del self.data[key]
            del self.expires[key]
            return None
        return self.data.get(key)
    
    async def set(self, key: str, value: str, ex: Optional[int] = None) -> bool:
        self.data[key] = value
        if ex:
            self.expires[key] = datetime.now() + timedelta(seconds=ex)
        return True
    
    async def delete(self, key: str) -> bool:
        if key in self.data:
            del self.data[key]
            if key in self.expires:
                del self.expires[key]
            return True
        return False
    
    async def exists(self, key: str) -> bool:
        return key in self.data
    
    async def ttl(self, key: str) -> int:
        if key not in self.data:
            return -2
        if key in self.expires:
            remaining = (self.expires[key] - datetime.now()).total_seconds()
            return max(0, int(remaining))
        return -1


class MockDatabase:
    """Mock数据库会话"""
    
    def __init__(self):
        self.users = {}
        self.articles = {}
        self.videos = {}
        self.user_devices = {}
        self.user_stats = {}
        self.user_roles = {}
        self.next_id = 1
    
    async def get_user(self, user_id: int) -> Optional[User]:
        return self.users.get(user_id)
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        for user in self.users.values():
            if user.username == username:
                return user
        return None
    
    async def create_user(self, user_data: Dict[str, Any]) -> User:
        user_id = self.next_id
        self.next_id += 1
        
        user = User(
            id=user_id,
            username=user_data.get('username'),
            email=user_data.get('email'),
            nickname=user_data.get('nickname', ''),
            role_id=user_data.get('role_id', 1),
            is_active=True,
            is_superuser=False,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        self.users[user_id] = user
        
        # 创建用户统计
        self.user_stats[user_id] = UserStats(
            article_count=0,
            video_count=0,
            follower_count=0,
            following_count=0
        )
        
        return user
    
    async def get_article(self, article_id: int) -> Optional[ArticleOut]:
        return self.articles.get(article_id)
    
    async def create_article(self, article_data: Dict[str, Any]) -> ArticleOut:
        article_id = self.next_id
        self.next_id += 1
        
        article = ArticleOut(
            id=article_id,
            title=article_data.get('title', ''),
            content=article_data.get('content', ''),
            author_id=article_data.get('author_id'),
            created_at=datetime.now(),
            updated_at=datetime.now(),
            view_count=0,
            like_count=0,
            comment_count=0
        )
        
        self.articles[article_id] = article
        return article
    
    async def get_video(self, video_id: int) -> Optional[VideoOut]:
        return self.videos.get(video_id)
    
    async def create_video(self, video_data: Dict[str, Any]) -> VideoOut:
        video_id = self.next_id
        self.next_id += 1
        
        video = VideoOut(
            id=video_id,
            title=video_data.get('title', ''),
            description=video_data.get('description', ''),
            author_id=video_data.get('author_id'),
            created_at=datetime.now(),
            updated_at=datetime.now(),
            view_count=0,
            like_count=0,
            comment_count=0
        )
        
        self.videos[video_id] = video
        return video


class MockAuthOrchestratorService:
    """Mock认证编排服务"""
    
    def __init__(self):
        self.db = MockDatabase()
        self.redis = MockRedis()
        self.sms_service = AsyncMock()
        self.wechat_service = AsyncMock()
        self.device_service = AsyncMock()
    
    async def initiate_authentication(self, auth_type: str, request_data: Dict[str, Any]) -> AuthInitiationResponse:
        """模拟认证发起"""
        phone = request_data.get('phone')
        
        # 检查频率限制
        rate_limit_key = f"sms_rate_limit:{phone}"
        if await self.redis.exists(rate_limit_key):
            return AuthInitiationResponse(
                success=False,
                message="发送频率过高，请稍后再试",
                auth_type=auth_type,
                retry_after=60
            )
        
        # 模拟发送验证码
        verification_code = "123456"  # 固定测试验证码
        await self.redis.set(f"sms_code:{phone}", verification_code, ex=300)
        await self.redis.set(rate_limit_key, "1", ex=60)
        
        return AuthInitiationResponse(
            success=True,
            message="验证码发送成功",
            auth_type=auth_type,
            expires_in=300
        )
    
    async def complete_authentication(self, auth_type: str, verification_data: Dict[str, Any], request: Request, db: AsyncSession) -> AuthCompletionResponse:
        """模拟认证完成"""
        phone = verification_data.get('phone')
        code = verification_data.get('code')
        
        # 验证验证码
        stored_code = await self.redis.get(f"sms_code:{phone}")
        if not stored_code or stored_code != code:
            return AuthCompletionResponse(
                success=False,
                message="验证码错误",
                auth_type=auth_type
            )
        
        # 获取或创建用户
        user = await self.db.get_user_by_username(phone)
        if not user:
            user = await self.db.create_user({
                'username': phone,
                'email': f"{phone}@test.com",
                'nickname': f"用户{phone[-4:]}"
            })
        
        # 生成访问令牌
        access_token = f"mock_token_{user.id}_{uuid4().hex[:8]}"
        
        return AuthCompletionResponse(
            success=True,
            message="登录成功",
            auth_type=auth_type,
            user=UserAggregated(
                id=user.id,
                username=user.username,
                role_id=user.role_id,
                nickname=user.nickname,
                email=user.email,
                is_active=user.is_active,
                is_superuser=user.is_superuser,
                created_at=user.created_at,
                updated_at=user.updated_at,
                stats=self.db.user_stats.get(user_id=user.id, default=UserStats())
            ),
            access_token=access_token,
            token_type="bearer"
        )


class MockRequest:
    """Mock FastAPI Request对象"""
    
    def __init__(self, headers: Optional[Dict[str, str]] = None, client: Optional[str] = None):
        self.headers = headers or {}
        self.client = Mock()
        self.client.host = client or "127.0.0.1"
    
    async def json(self) -> Dict[str, Any]:
        return {}


class TestUserFactory:
    """测试用户工厂"""
    
    @staticmethod
    def create_user(
        user_id: int = 1,
        username: str = "testuser",
        email: str = "<EMAIL>",
        nickname: str = "测试用户",
        role_id: int = 1,
        is_active: bool = True,
        is_superuser: bool = False
    ) -> User:
        """创建测试用户"""
        return User(
            id=user_id,
            username=username,
            email=email,
            nickname=nickname,
            role_id=role_id,
            is_active=is_active,
            is_superuser=is_superuser,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    @staticmethod
    def create_admin_user(user_id: int = 999) -> User:
        """创建管理员用户"""
        return TestUserFactory.create_user(
            user_id=user_id,
            username="admin",
            email="<EMAIL>",
            nickname="管理员",
            is_superuser=True
        )


class TestDataFactory:
    """测试数据工厂"""
    
    @staticmethod
    def create_article_data(
        title: str = "测试文章",
        content: str = "这是一篇测试文章的内容",
        author_id: int = 1,
        **kwargs
    ) -> Dict[str, Any]:
        """创建文章测试数据"""
        return {
            "title": title,
            "content": content,
            "author_id": author_id,
            **kwargs
        }
    
    @staticmethod
    def create_video_data(
        title: str = "测试视频",
        description: str = "这是一个测试视频",
        author_id: int = 1,
        **kwargs
    ) -> Dict[str, Any]:
        """创建视频测试数据"""
        return {
            "title": title,
            "description": description,
            "author_id": author_id,
            **kwargs
        }
    
    @staticmethod
    def create_user_update_data(
        nickname: Optional[str] = None,
        email: Optional[str] = None,
        password: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """创建用户更新数据"""
        data = {}
        if nickname is not None:
            data["nickname"] = nickname
        if email is not None:
            data["email"] = email
        if password is not None:
            data["password"] = password
        data.update(kwargs)
        return data


class APITestClient:
    """API测试客户端"""
    
    def __init__(self, app: FastAPI):
        self.client = TestClient(app)
        self.auth_tokens = {}
    
    def login(self, username: str, password: str) -> str:
        """模拟登录并返回token"""
        response = self.client.post(
            "/api/v1/auth/sms/login",
            json={"phone": username, "code": password}
        )
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                token = data.get("access_token")
                self.auth_tokens[username] = token
                return token
        return None
    
    def get_auth_headers(self, username: str) -> Dict[str, str]:
        """获取认证头"""
        token = self.auth_tokens.get(username)
        if token:
            return {"Authorization": f"Bearer {token}"}
        return {}
    
    def get(self, url: str, username: Optional[str] = None, **kwargs) -> Any:
        """GET请求"""
        headers = kwargs.pop('headers', {})
        if username:
            headers.update(self.get_auth_headers(username))
        return self.client.get(url, headers=headers, **kwargs)
    
    def post(self, url: str, username: Optional[str] = None, **kwargs) -> Any:
        """POST请求"""
        headers = kwargs.pop('headers', {})
        if username:
            headers.update(self.get_auth_headers(username))
        return self.client.post(url, headers=headers, **kwargs)
    
    def put(self, url: str, username: Optional[str] = None, **kwargs) -> Any:
        """PUT请求"""
        headers = kwargs.pop('headers', {})
        if username:
            headers.update(self.get_auth_headers(username))
        return self.client.put(url, headers=headers, **kwargs)
    
    def delete(self, url: str, username: Optional[str] = None, **kwargs) -> Any:
        """DELETE请求"""
        headers = kwargs.pop('headers', {})
        if username:
            headers.update(self.get_auth_headers(username))
        return self.client.delete(url, headers=headers, **kwargs)


class ResponseValidator:
    """响应验证工具"""
    
    @staticmethod
    def validate_success_response(response_data: Dict[str, Any]) -> bool:
        """验证成功响应格式"""
        return (
            isinstance(response_data, dict) and
            response_data.get('success') is True and
            'code' in response_data and
            'message' in response_data and
            'timestamp' in response_data
        )
    
    @staticmethod
    def validate_error_response(response_data: Dict[str, Any]) -> bool:
        """验证错误响应格式"""
        return (
            isinstance(response_data, dict) and
            response_data.get('success') is False and
            'code' in response_data and
            'message' in response_data and
            'timestamp' in response_data
        )
    
    @staticmethod
    def validate_pagination_response(response_data: Dict[str, Any]) -> bool:
        """验证分页响应格式"""
        return (
            ResponseValidator.validate_success_response(response_data) and
            'data' in response_data and
            isinstance(response_data['data'], dict) and
            'items' in response_data['data'] and
            isinstance(response_data['data']['items'], list)
        )
    
    @staticmethod
    def assert_response_code(response_data: Dict[str, Any], expected_code: Union[ResponseCode, int]):
        """断言响应码"""
        if isinstance(expected_code, ResponseCode):
            expected_code = expected_code.code
        
        actual_code = response_data.get('code')
        assert actual_code == expected_code, f"Expected code {expected_code}, got {actual_code}"
    
    @staticmethod
    def assert_response_message(response_data: Dict[str, Any], expected_message: str):
        """断言响应消息"""
        actual_message = response_data.get('message')
        assert actual_message == expected_message, f"Expected message '{expected_message}', got '{actual_message}'"


class TestUtils:
    """测试工具类"""
    
    @staticmethod
    def generate_phone_number() -> str:
        """生成随机手机号"""
        import random
        return f"1{random.randint(3, 9)}{random.randint(100000000, 999999999)}"
    
    @staticmethod
    def generate_email() -> str:
        """生成随机邮箱"""
        import random
        import string
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        return f"{username}@test.com"
    
    @staticmethod
    def generate_chinese_name() -> str:
        """生成随机中文名"""
        import random
        surnames = ['张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴']
        given_names = ['伟', '芳', '娜', '秀英', '敏', '静', '丽', '强', '磊', '军']
        return f"{random.choice(surnames)}{random.choice(given_names)}"
    
    @staticmethod
    async def wait_for_condition(condition_func, timeout: float = 5.0, interval: float = 0.1) -> bool:
        """等待条件满足"""
        start_time = datetime.now()
        while (datetime.now() - start_time).total_seconds() < timeout:
            if await condition_func():
                return True
            await asyncio.sleep(interval)
        return False