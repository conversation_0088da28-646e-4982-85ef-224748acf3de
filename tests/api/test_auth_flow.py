"""端到端认证流程集成测试"""

from unittest.mock import AsyncMock, MagicMock

import pytest
import pytest_asyncio
from httpx import ASGITransport, AsyncClient

from app.main import app
from app.schemas.auth import AuthCompletionResponse, AuthInitiationResponse
from app.services.auth_orchestrator_service import AuthOrchestratorService
from app.services.service_factory import get_auth_orchestrator_service


@pytest_asyncio.fixture
async def client() -> AsyncClient:
    """提供一个异步的HTTP测试客户端"""
    async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as ac:
        yield ac


@pytest.mark.asyncio
async def test_full_auth_flow_with_device_verification(client: AsyncClient):
    """
    测试一个完整的认证流程，包括：
    1. 发起认证 (发送验证码)
    2. 完成认证 (提交验证码)，但触发设备验证
    3. 确认设备 (提交设备验证码)，最终成功登录
    """
    # --- Mock AuthOrchestratorService ---
    mock_orchestrator = AsyncMock(spec=AuthOrchestratorService)

    # 覆盖依赖
    app.dependency_overrides[get_auth_orchestrator_service] = lambda: mock_orchestrator

    # --- 步骤 1: 发起认证 ---
    mock_orchestrator.initiate_authentication.return_value = AuthInitiationResponse(
        success=True, message="验证码已发送", auth_type="SMS"
    )

    response_init = await client.post("/api/v1/auth/initiate", json={"phone": "13800138000"})

    assert response_init.status_code == 200
    data_init = response_init.json()
    assert data_init["success"] is True
    assert data_init["message"] == "验证码已发送"

    # --- 步骤 2: 完成认证 (触发设备验证) ---
    mock_orchestrator.complete_authentication.return_value = AuthCompletionResponse(
        success=False,
        user=None,
        access_token=None,
        token_type="bearer",
        requires_device_verification=True,
        verification_token="fake_device_verification_token",
        message="需要设备验证",
        auth_method="SMS",
    )

    response_complete = await client.post(
        "/api/v1/auth/complete", json={"phone": "13800138000", "code": "123456"}
    )

    assert response_complete.status_code == 200
    data_complete = response_complete.json()
    assert data_complete["requires_device_verification"] is True
    assert data_complete["verification_token"] is not None

    # --- 步骤 3: 确认设备 ---
    mock_user = MagicMock(id=1, username="testuser", email="<EMAIL>")
    mock_orchestrator.handle_device_verification.return_value = AuthCompletionResponse(
        success=True,
        user=mock_user,
        access_token="fake_access_token",
        token_type="bearer",
        message="登录成功",
        auth_method="device_verification",
    )

    response_confirm = await client.post(
        "/api/v1/auth/device/confirm",
        json={
            "verification_token": data_complete["verification_token"],
            "code": "654321",
        },
    )

    assert response_confirm.status_code == 200
    data_confirm = response_confirm.json()
    assert data_confirm["success"] is True
    assert data_confirm["access_token"] == "fake_access_token"
    assert data_confirm["user"] is not None

    # 清理依赖覆盖
    app.dependency_overrides = {}
