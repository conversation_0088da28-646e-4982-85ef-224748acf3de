"""
用户管理API测试用例

测试所有用户相关的API端点，包括：
- 用户信息获取
- 用户信息更新
- 用户删除
- 用户关注/取消关注
- 用户内容列表（点赞、收藏、历史）
- 权限控制
- 分页功能
"""

import pytest
from fastapi import FastAPI, Request
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from app.api.endpoints.users import router as users_router
from app.core.response_wrapper import ResponseCode
from app.models.user import User, UserRole
from app.schemas.user import UserAggregated, UserStats, UserUpdate
from app.core.pagination import CursorPaginationResponse
from tests.utils.test_helpers import (
    MockDatabase, MockRequest, ResponseValidator, TestUtils,
    TestDataFactory, TestUserFactory
)


@pytest.fixture
def mock_db():
    """Mock数据库"""
    return MockDatabase()


@pytest.fixture
def test_app():
    """测试应用"""
    app = FastAPI()
    app.include_router(users_router, prefix="/api/v1/users", tags=["users"])
    return app


@pytest.fixture
def client(test_app):
    """测试客户端"""
    return TestClient(test_app)


@pytest.fixture
def test_user():
    """测试用户"""
    return TestUserFactory.create_user(
        user_id=1,
        username="testuser",
        email="<EMAIL>",
        nickname="测试用户"
    )


@pytest.fixture
def test_admin():
    """测试管理员"""
    return TestUserFactory.create_user(
        user_id=999,
        username="admin",
        email="<EMAIL>",
        nickname="管理员",
        is_superuser=True
    )


@pytest.fixture
def auth_headers():
    """认证头"""
    return {"Authorization": "Bearer test_token"}


class TestUserGetCurrentUser:
    """获取当前用户信息测试"""
    
    def test_get_current_user_success(self, client, test_user, auth_headers):
        """测试成功获取当前用户信息"""
        with patch('app.api.deps.get_current_user', return_value=test_user):
            with patch('app.services.service_factory.get_user_aggregation_service') as mock_get_service:
                mock_service = AsyncMock()
                mock_service.get_aggregated_user.return_value = UserAggregated(
                    id=test_user.id,
                    username=test_user.username,
                    role_id=test_user.role_id,
                    nickname=test_user.nickname,
                    email=test_user.email,
                    is_active=test_user.is_active,
                    is_superuser=test_user.is_superuser,
                    created_at=test_user.created_at,
                    updated_at=test_user.updated_at,
                    stats=UserStats()
                )
                mock_get_service.return_value = mock_service
                
                response = client.get("/api/v1/users/me", headers=auth_headers)
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证响应格式
                ResponseValidator.validate_success_response(data)
                user_data = data.get("data", {})
                assert user_data.get("id") == test_user.id
                assert user_data.get("username") == test_user.username
                assert user_data.get("email") == test_user.email
    
    def test_get_current_user_not_found(self, client, test_user, auth_headers):
        """测试用户不存在"""
        with patch('app.api.deps.get_current_user', return_value=test_user):
            with patch('app.services.service_factory.get_user_aggregation_service') as mock_get_service:
                mock_service = AsyncMock()
                mock_service.get_aggregated_user.return_value = None
                mock_get_service.return_value = mock_service
                
                response = client.get("/api/v1/users/me", headers=auth_headers)
                
                assert response.status_code == 404
                data = response.json()
                ResponseValidator.validate_error_response(data)
    
    def test_get_current_user_unauthorized(self, client):
        """测试未授权访问"""
        response = client.get("/api/v1/users/me")
        assert response.status_code == 401


class TestGetUserById:
    """根据ID获取用户信息测试"""
    
    def test_get_user_success(self, client, test_user):
        """测试成功获取用户信息"""
        with patch('app.services.service_factory.get_user_aggregation_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.get_aggregated_user.return_value = UserAggregated(
                id=test_user.id,
                username=test_user.username,
                role_id=test_user.role_id,
                nickname=test_user.nickname,
                email=test_user.email,
                is_active=test_user.is_active,
                is_superuser=test_user.is_superuser,
                created_at=test_user.created_at,
                updated_at=test_user.updated_at,
                stats=UserStats()
            )
            mock_get_service.return_value = mock_service
            
            response = client.get(f"/api/v1/users/{test_user.id}")
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应格式
            ResponseValidator.validate_success_response(data)
            user_data = data.get("data", {})
            assert user_data.get("id") == test_user.id
            assert user_data.get("username") == test_user.username
    
    def test_get_user_not_found(self, client):
        """测试用户不存在"""
        with patch('app.services.service_factory.get_user_aggregation_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.get_aggregated_user.return_value = None
            mock_get_service.return_value = mock_service
            
            response = client.get("/api/v1/users/999")
            
            assert response.status_code == 404
            data = response.json()
            ResponseValidator.validate_error_response(data)
    
    def test_get_user_invalid_id(self, client):
        """测试无效用户ID"""
        invalid_ids = ["abc", "0", "-1", "1.5"]
        
        for user_id in invalid_ids:
            response = client.get(f"/api/v1/users/{user_id}")
            assert response.status_code == 422


class TestUpdateUser:
    """更新用户信息测试"""
    
    def test_update_user_success(self, client, test_user, auth_headers):
        """测试成功更新用户信息"""
        update_data = {
            "nickname": "更新后的昵称",
            "email": "<EMAIL>"
        }
        
        with patch('app.api.deps.get_current_user', return_value=test_user):
            with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = test_user
                mock_get_cache.return_value = mock_cache_service
                
                with patch('app.services.service_factory.get_password_service') as mock_get_password:
                    mock_password_service = MagicMock()
                    mock_get_password.return_value = mock_password_service
                    
                    # 模拟数据库更新
                    with patch('app.db.session.get_db') as mock_get_db:
                        mock_db = MagicMock()
                        mock_db.execute.return_value.scalar_one_or_none.return_value = None
                        mock_db.commit.return_value = None
                        mock_db.refresh.return_value = None
                        mock_get_db.return_value = mock_db
                        
                        response = client.put(
                            f"/api/v1/users/{test_user.id}",
                            json=update_data,
                            headers=auth_headers
                        )
                        
                        assert response.status_code == 200
                        data = response.json()
                        
                        # 验证响应格式
                        ResponseValidator.validate_success_response(data)
                        user_data = data.get("data", {})
                        assert user_data.get("nickname") == "更新后的昵称"
    
    def test_update_user_with_password(self, client, test_user, auth_headers):
        """测试更新用户密码"""
        update_data = {
            "password": "newpassword123"
        }
        
        with patch('app.api.deps.get_current_user', return_value=test_user):
            with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = test_user
                mock_get_cache.return_value = mock_cache_service
                
                with patch('app.services.service_factory.get_password_service') as mock_get_password:
                    mock_password_service = MagicMock()
                    mock_password_service.get_password_hash.return_value = "hashed_password"
                    mock_get_password.return_value = mock_password_service
                    
                    with patch('app.db.session.get_db') as mock_get_db:
                        mock_db = MagicMock()
                        mock_db.commit.return_value = None
                        mock_db.refresh.return_value = None
                        mock_get_db.return_value = mock_db
                        
                        response = client.put(
                            f"/api/v1/users/{test_user.id}",
                            json=update_data,
                            headers=auth_headers
                        )
                        
                        assert response.status_code == 200
                        # 验证密码哈希被调用
                        mock_password_service.get_password_hash.assert_called_once_with("newpassword123")
    
    def test_update_user_not_found(self, client, test_user, auth_headers):
        """测试更新不存在的用户"""
        with patch('app.api.deps.get_current_user', return_value=test_user):
            with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = None
                mock_get_cache.return_value = mock_cache_service
                
                response = client.put("/api/v1/users/999", json={"nickname": "test"}, headers=auth_headers)
                
                assert response.status_code == 404
                data = response.json()
                ResponseValidator.validate_error_response(data)
    
    def test_update_user_permission_denied(self, client, test_user, auth_headers):
        """测试权限不足"""
        other_user_id = 2
        
        with patch('app.api.deps.get_current_user', return_value=test_user):
            with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = TestUserFactory.create_user(user_id=other_user_id)
                mock_get_cache.return_value = mock_cache_service
                
                # 模拟权限检查失败
                with patch('app.core.permission_system.PermissionChecker.require_permission') as mock_permission:
                    mock_permission.side_effect = Exception("权限不足")
                    
                    response = client.put(
                        f"/api/v1/users/{other_user_id}",
                        json={"nickname": "test"},
                        headers=auth_headers
                    )
                    
                    assert response.status_code == 403
    
    def test_update_user_email_exists(self, client, test_user, auth_headers):
        """测试邮箱已存在"""
        update_data = {"email": "<EMAIL>"}
        
        with patch('app.api.deps.get_current_user', return_value=test_user):
            with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = test_user
                mock_get_cache.return_value = mock_cache_service
                
                with patch('app.services.service_factory.get_password_service') as mock_get_password:
                    mock_password_service = MagicMock()
                    mock_get_password.return_value = mock_password_service
                    
                    with patch('app.db.session.get_db') as mock_get_db:
                        mock_db = MagicMock()
                        # 模拟邮箱已存在
                        existing_user = TestUserFactory.create_user(user_id=2, email="<EMAIL>")
                        mock_db.execute.return_value.scalar_one_or_none.return_value = existing_user
                        mock_get_db.return_value = mock_db
                        
                        response = client.put(
                            f"/api/v1/users/{test_user.id}",
                            json=update_data,
                            headers=auth_headers
                        )
                        
                        assert response.status_code == 400
                        data = response.json()
                        assert "邮箱已存在" in data.get("message", "")


class TestDeleteUser:
    """删除用户测试"""
    
    def test_delete_user_success(self, client, test_admin, auth_headers):
        """测试成功删除用户"""
        target_user = TestUserFactory.create_user(user_id=2)
        
        with patch('app.api.deps.get_current_user', return_value=test_admin):
            with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = target_user
                mock_cache_service.invalidate_user.return_value = None
                mock_get_cache.return_value = mock_cache_service
                
                with patch('app.crud.user.remove') as mock_remove:
                    mock_remove.return_value = None
                    
                    with patch('app.tasks.cache_tasks.task_invalidate_user_cache') as mock_task:
                        mock_task.apply_async.return_value = None
                        
                        response = client.delete(f"/api/v1/users/{target_user.id}", headers=auth_headers)
                        
                        assert response.status_code == 204
    
    def test_delete_user_not_found(self, client, test_admin, auth_headers):
        """测试删除不存在的用户"""
        with patch('app.api.deps.get_current_user', return_value=test_admin):
            with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = None
                mock_get_cache.return_value = mock_cache_service
                
                response = client.delete("/api/v1/users/999", headers=auth_headers)
                
                assert response.status_code == 404
                data = response.json()
                ResponseValidator.validate_error_response(data)
    
    def test_delete_user_permission_denied(self, client, test_user, auth_headers):
        """测试权限不足"""
        with patch('app.api.deps.get_current_user', return_value=test_user):
            response = client.delete("/api/v1/users/2", headers=auth_headers)
            assert response.status_code == 403
    
    def test_delete_self_not_allowed(self, client, test_user, auth_headers):
        """测试不能删除自己"""
        with patch('app.api.deps.get_current_user', return_value=test_user):
            with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = test_user
                mock_get_cache.return_value = mock_cache_service
                
                response = client.delete(f"/api/v1/users/{test_user.id}", headers=auth_headers)
                
                assert response.status_code == 400
                data = response.json()
                assert "不能删除自己的账户" in data.get("message", "")


class TestUserFollow:
    """用户关注测试"""
    
    def test_follow_user_success(self, client, test_user, auth_headers):
        """测试成功关注用户"""
        target_user = TestUserFactory.create_user(user_id=2)
        follow_data = {"user_id": target_user.id}
        
        with patch('app.api.deps.get_current_user', return_value=test_user):
            with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = target_user
                mock_get_cache.return_value = mock_cache_service
                
                with patch('app.crud.user.follow') as mock_follow:
                    mock_follow.return_value = None
                    
                    response = client.post("/api/v1/users/follow", json=follow_data, headers=auth_headers)
                    
                    assert response.status_code == 204
    
    def test_follow_self_not_allowed(self, client, test_user, auth_headers):
        """测试不能关注自己"""
        follow_data = {"user_id": test_user.id}
        
        with patch('app.api.deps.get_current_user', return_value=test_user):
            response = client.post("/api/v1/users/follow", json=follow_data, headers=auth_headers)
            
            assert response.status_code == 400
            data = response.json()
            assert "不能关注自己" in data.get("message", "")
    
    def test_follow_user_not_found(self, client, test_user, auth_headers):
        """测试关注不存在的用户"""
        follow_data = {"user_id": 999}
        
        with patch('app.api.deps.get_current_user', return_value=test_user):
            with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = None
                mock_get_cache.return_value = mock_cache_service
                
                response = client.post("/api/v1/users/follow", json=follow_data, headers=auth_headers)
                
                assert response.status_code == 404
                data = response.json()
                assert "被关注的用户不存在" in data.get("message", "")
    
    def test_follow_already_followed(self, client, test_user, auth_headers):
        """测试已经关注过的用户"""
        target_user = TestUserFactory.create_user(user_id=2)
        follow_data = {"user_id": target_user.id}
        
        with patch('app.api.deps.get_current_user', return_value=test_user):
            with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = target_user
                # 模拟已经关注
                test_user.following = [target_user]
                mock_get_cache.return_value = mock_cache_service
                
                response = client.post("/api/v1/users/follow", json=follow_data, headers=auth_headers)
                
                assert response.status_code == 400
                data = response.json()
                assert "已经关注了该用户" in data.get("message", "")


class TestUserUnfollow:
    """用户取消关注测试"""
    
    def test_unfollow_user_success(self, client, test_user, auth_headers):
        """测试成功取消关注"""
        target_user = TestUserFactory.create_user(user_id=2)
        follow_data = {"user_id": target_user.id}
        
        with patch('app.api.deps.get_current_user', return_value=test_user):
            with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = target_user
                # 模拟已经关注
                test_user.following = [target_user]
                mock_get_cache.return_value = mock_cache_service
                
                with patch('app.crud.user.unfollow') as mock_unfollow:
                    mock_unfollow.return_value = None
                    
                    response = client.post("/api/v1/users/unfollow", json=follow_data, headers=auth_headers)
                    
                    assert response.status_code == 204
    
    def test_unfollow_user_not_found(self, client, test_user, auth_headers):
        """测试取消关注不存在的用户"""
        follow_data = {"user_id": 999}
        
        with patch('app.api.deps.get_current_user', return_value=test_user):
            with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = None
                mock_get_cache.return_value = mock_cache_service
                
                response = client.post("/api/v1/users/unfollow", json=follow_data, headers=auth_headers)
                
                assert response.status_code == 404
                data = response.json()
                assert "被取消关注的用户不存在" in data.get("message", "")
    
    def test_unfollow_not_followed(self, client, test_user, auth_headers):
        """测试取消关注未关注的用户"""
        target_user = TestUserFactory.create_user(user_id=2)
        follow_data = {"user_id": target_user.id}
        
        with patch('app.api.deps.get_current_user', return_value=test_user):
            with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = target_user
                # 模拟未关注
                test_user.following = []
                mock_get_cache.return_value = mock_cache_service
                
                response = client.post("/api/v1/users/unfollow", json=follow_data, headers=auth_headers)
                
                assert response.status_code == 400
                data = response.json()
                assert "未关注该用户" in data.get("message", "")


class TestUserContentLists:
    """用户内容列表测试"""
    
    def test_get_user_liked_articles_success(self, client, test_user):
        """测试获取用户点赞的文章列表"""
        with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
            mock_cache_service = AsyncMock()
            mock_cache_service.get_entity.return_value = test_user
            mock_get_cache.return_value = mock_cache_service
            
            with patch('app.crud.article.article.get_paginated_liked_articles') as mock_get_articles:
                mock_get_articles.return_value = CursorPaginationResponse(
                    items=[],
                    total_count=0,
                    has_next=False,
                    has_previous=False,
                    next_cursor=None,
                    previous_cursor=None
                )
                
                with patch('app.services.service_factory.get_article_aggregation_service') as mock_get_agg:
                    mock_agg_service = AsyncMock()
                    mock_agg_service.get_aggregated_articles_by_ids.return_value = []
                    mock_get_agg.return_value = mock_agg_service
                    
                    response = client.get(f"/api/v1/users/{test_user.id}/likes/articles")
                    
                    assert response.status_code == 200
                    data = response.json()
                    
                    # 验证分页响应格式
                    ResponseValidator.validate_pagination_response(data)
                    assert data.get("data", {}).get("items") == []
    
    def test_get_user_liked_articles_user_not_found(self, client):
        """测试获取不存在用户的点赞文章"""
        with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
            mock_cache_service = AsyncMock()
            mock_cache_service.get_entity.return_value = None
            mock_get_cache.return_value = mock_cache_service
            
            response = client.get("/api/v1/users/999/likes/articles")
            
            assert response.status_code == 404
            data = response.json()
            ResponseValidator.validate_error_response(data)
    
    def test_get_user_liked_articles_permission_denied(self, client, test_user):
        """测试权限不足"""
        target_user = TestUserFactory.create_user(user_id=2)
        # 模拟用户隐私设置不允许查看点赞
        target_user.check_privacy_setting = lambda resource_type, content_type: False
        
        with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
            mock_cache_service = AsyncMock()
            mock_cache_service.get_entity.return_value = target_user
            mock_get_cache.return_value = mock_cache_service
            
            response = client.get(f"/api/v1/users/{target_user.id}/likes/articles")
            
            assert response.status_code == 403
            data = response.json()
            assert "没有权限查看" in data.get("message", "")
    
    def test_get_user_favorite_articles_success(self, client, test_user):
        """测试获取用户收藏的文章列表"""
        with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
            mock_cache_service = AsyncMock()
            mock_cache_service.get_entity.return_value = test_user
            mock_get_cache.return_value = mock_cache_service
            
            with patch('app.crud.article.get_paginated_favorite_articles') as mock_get_articles:
                mock_get_articles.return_value = type('MockResult', (), {
                    'items': [],
                    'total_count': 0,
                    'has_next': False,
                    'has_previous': False,
                    'next_cursor': None,
                    'previous_cursor': None
                })()
                
                with patch('app.services.service_factory.get_article_aggregation_service') as mock_get_agg:
                    mock_agg_service = AsyncMock()
                    mock_agg_service.get_aggregated_articles_by_ids.return_value = []
                    mock_get_agg.return_value = mock_agg_service
                    
                    response = client.get(f"/api/v1/users/{test_user.id}/favorites/articles")
                    
                    assert response.status_code == 200
                    data = response.json()
                    
                    # 验证分页响应格式
                    ResponseValidator.validate_pagination_response(data)
    
    def test_get_user_history_articles_success(self, client, test_user, auth_headers):
        """测试获取用户浏览历史的文章列表"""
        with patch('app.api.deps.get_current_user_optional', return_value=test_user):
            with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = test_user
                mock_get_cache.return_value = mock_cache_service
                
                with patch('app.crud.article.get_paginated_history_articles') as mock_get_articles:
                    mock_get_articles.return_value = type('MockResult', (), {
                        'items': [],
                        'total_count': 0,
                        'has_next': False,
                        'has_previous': False,
                        'next_cursor': None,
                        'previous_cursor': None
                    })()
                    
                    with patch('app.services.service_factory.get_article_aggregation_service') as mock_get_agg:
                        mock_agg_service = AsyncMock()
                        mock_agg_service.get_aggregated_articles_by_ids.return_value = []
                        mock_get_agg.return_value = mock_agg_service
                        
                        response = client.get(f"/api/v1/users/{test_user.id}/history/articles", headers=auth_headers)
                        
                        assert response.status_code == 200
                        data = response.json()
                        
                        # 验证分页响应格式
                        ResponseValidator.validate_pagination_response(data)
    
    def test_get_user_history_articles_permission_denied(self, client, test_user):
        """测试权限不足（只能查看自己的历史）"""
        target_user = TestUserFactory.create_user(user_id=2)
        
        with patch('app.api.deps.get_current_user_optional', return_value=test_user):
            with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = target_user
                mock_get_cache.return_value = mock_cache_service
                
                response = client.get(f"/api/v1/users/{target_user.id}/history/articles")
                
                assert response.status_code == 403
                data = response.json()
                assert "没有权限查看" in data.get("message", "")
    
    def test_get_user_liked_videos_success(self, client, test_user):
        """测试获取用户点赞的视频列表"""
        with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
            mock_cache_service = AsyncMock()
            mock_cache_service.get_entity.return_value = test_user
            mock_get_cache.return_value = mock_cache_service
            
            with patch('app.crud.video.get_paginated_liked_videos') as mock_get_videos:
                mock_get_videos.return_value = type('MockResult', (), {
                    'items': [],
                    'total_count': 0,
                    'has_next': False,
                    'has_previous': False,
                    'next_cursor': None,
                    'previous_cursor': None
                })()
                
                with patch('app.services.service_factory.get_video_aggregation_service') as mock_get_agg:
                    mock_agg_service = AsyncMock()
                    mock_agg_service.get_aggregated_videos_by_ids.return_value = []
                    mock_get_agg.return_value = mock_agg_service
                    
                    response = client.get(f"/api/v1/users/{test_user.id}/likes/videos")
                    
                    assert response.status_code == 200
                    data = response.json()
                    
                    # 验证分页响应格式
                    ResponseValidator.validate_pagination_response(data)
    
    def test_get_user_favorite_videos_success(self, client, test_user):
        """测试获取用户收藏的视频列表"""
        with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
            mock_cache_service = AsyncMock()
            mock_cache_service.get_entity.return_value = test_user
            mock_get_cache.return_value = mock_cache_service
            
            with patch('app.crud.video.get_paginated_favorite_videos') as mock_get_videos:
                mock_get_videos.return_value = type('MockResult', (), {
                    'items': [],
                    'total_count': 0,
                    'has_next': False,
                    'has_previous': False,
                    'next_cursor': None,
                    'previous_cursor': None
                })()
                
                with patch('app.services.service_factory.get_video_aggregation_service') as mock_get_agg:
                    mock_agg_service = AsyncMock()
                    mock_agg_service.get_aggregated_videos_by_ids.return_value = []
                    mock_get_agg.return_value = mock_agg_service
                    
                    response = client.get(f"/api/v1/users/{test_user.id}/favorites/videos")
                    
                    assert response.status_code == 200
                    data = response.json()
                    
                    # 验证分页响应格式
                    ResponseValidator.validate_pagination_response(data)
    
    def test_get_user_history_videos_success(self, client, test_user, auth_headers):
        """测试获取用户浏览历史的视频列表"""
        with patch('app.api.deps.get_current_user_optional', return_value=test_user):
            with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = test_user
                mock_get_cache.return_value = mock_cache_service
                
                with patch('app.crud.video.get_paginated_history_videos') as mock_get_videos:
                    mock_get_videos.return_value = type('MockResult', (), {
                        'items': [],
                        'total_count': 0,
                        'has_next': False,
                        'has_previous': False,
                        'next_cursor': None,
                        'previous_cursor': None
                    })()
                    
                    with patch('app.services.service_factory.get_video_aggregation_service') as mock_get_agg:
                        mock_agg_service = AsyncMock()
                        mock_agg_service.get_aggregated_videos_by_ids.return_value = []
                        mock_get_agg.return_value = mock_agg_service
                        
                        response = client.get(f"/api/v1/users/{test_user.id}/history/videos", headers=auth_headers)
                        
                        assert response.status_code == 200
                        data = response.json()
                        
                        # 验证分页响应格式
                        ResponseValidator.validate_pagination_response(data)


class TestUserPagination:
    """用户分页测试"""
    
    def test_get_users_pagination_success(self, client, test_admin, auth_headers):
        """测试成功获取用户列表（分页）"""
        with patch('app.api.deps.get_current_user', return_value=test_admin):
            with patch('app.services.service_factory.get_user_aggregation_service') as mock_get_service:
                mock_service = AsyncMock()
                mock_service.get_paginated_users.return_value = CursorPaginationResponse(
                    items=[],
                    total_count=0,
                    has_next=False,
                    has_previous=False,
                    next_cursor=None,
                    previous_cursor=None
                )
                mock_get_service.return_value = mock_service
                
                response = client.get("/api/v1/users/", headers=auth_headers)
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证分页响应格式
                ResponseValidator.validate_pagination_response(data)
    
    def test_get_users_pagination_params(self, client, test_admin, auth_headers):
        """测试分页参数"""
        test_cases = [
            {"size": 10, "order_by": "id", "order_direction": "asc"},
            {"size": 50, "order_by": "created_at", "order_direction": "desc"},
            {"cursor": "abc123", "size": 20}
        ]
        
        for params in test_cases:
            with patch('app.api.deps.get_current_user', return_value=test_admin):
                with patch('app.services.service_factory.get_user_aggregation_service') as mock_get_service:
                    mock_service = AsyncMock()
                    mock_service.get_paginated_users.return_value = CursorPaginationResponse(
                        items=[],
                        total_count=0,
                        has_next=False,
                        has_previous=False,
                        next_cursor=None,
                        previous_cursor=None
                    )
                    mock_get_service.return_value = mock_service
                    
                    response = client.get("/api/v1/users/", params=params, headers=auth_headers)
                    
                    assert response.status_code == 200
    
    def test_get_users_invalid_pagination_params(self, client, test_admin, auth_headers):
        """测试无效分页参数"""
        invalid_params = [
            {"size": 0},  # size太小
            {"size": 101},  # size太大
            {"order_direction": "invalid"},  # 无效排序方向
        ]
        
        for params in invalid_params:
            with patch('app.api.deps.get_current_user', return_value=test_admin):
                response = client.get("/api/v1/users/", params=params, headers=auth_headers)
                assert response.status_code == 422
    
    def test_get_users_permission_denied(self, client, test_user, auth_headers):
        """测试权限不足"""
        with patch('app.api.deps.get_current_user', return_value=test_user):
            response = client.get("/api/v1/users/", headers=auth_headers)
            assert response.status_code == 403


class TestUserSecurity:
    """用户安全测试"""
    
    def test_sql_injection_prevention(self, client, test_user, auth_headers):
        """测试SQL注入防护"""
        malicious_inputs = [
            {"nickname": "test'; DROP TABLE users; --"},
            {"email": "<EMAIL>' OR '1'='1"},
            {"nickname": "<script>alert('xss')</script>"}
        ]
        
        for malicious_input in malicious_inputs:
            with patch('app.api.deps.get_current_user', return_value=test_user):
                response = client.put(
                    f"/api/v1/users/{test_user.id}",
                    json=malicious_input,
                    headers=auth_headers
                )
                # 应该被验证拦截或安全处理
                assert response.status_code in [422, 200]
    
    def test_xss_prevention(self, client, test_user, auth_headers):
        """测试XSS防护"""
        xss_payload = {"nickname": "<script>alert('xss')</script>"}
        
        with patch('app.api.deps.get_current_user', return_value=test_user):
            with patch('app.services.service_factory.get_user_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = test_user
                mock_get_cache.return_value = mock_cache_service
                
                with patch('app.services.service_factory.get_password_service') as mock_get_password:
                    mock_password_service = MagicMock()
                    mock_get_password.return_value = mock_password_service
                    
                    with patch('app.db.session.get_db') as mock_get_db:
                        mock_db = MagicMock()
                        mock_db.commit.return_value = None
                        mock_db.refresh.return_value = None
                        mock_get_db.return_value = mock_db
                        
                        response = client.put(
                            f"/api/v1/users/{test_user.id}",
                            json=xss_payload,
                            headers=auth_headers
                        )
                        
                        if response.status_code == 200:
                            data = response.json()
                            # 确保XSS脚本被转义或过滤
                            user_data = data.get("data", {})
                            nickname = user_data.get("nickname", "")
                            assert "<script>" not in nickname