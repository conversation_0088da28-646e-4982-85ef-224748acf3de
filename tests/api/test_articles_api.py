"""
文章管理API测试用例

测试所有文章相关的API端点，包括：
- 文章推荐
- 相似文章
- 文章CRUD操作
- 文章点赞、收藏、浏览历史
- 文章评论
- 文章分类和标签
- 权限控制
- 分页功能
"""

import pytest
from fastapi import FastAPI, Request
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta

from app.api.endpoints.articles import router as articles_router
from app.core.response_wrapper import ResponseCode
from app.models.user import User
from app.schemas.article import ArticleOut, ArticleCreate, ArticleUpdate
from app.core.pagination import CursorPaginationResponse
from tests.utils.test_helpers import (
    MockDatabase, MockRequest, ResponseValidator, TestUtils,
    TestDataFactory, TestUserFactory
)


@pytest.fixture
def mock_db():
    """Mock数据库"""
    return MockDatabase()


@pytest.fixture
def test_app():
    """测试应用"""
    app = FastAPI()
    app.include_router(articles_router, prefix="/api/v1/articles", tags=["articles"])
    return app


@pytest.fixture
def client(test_app):
    """测试客户端"""
    return TestClient(test_app)


@pytest.fixture
def test_user():
    """测试用户"""
    return TestUserFactory.create_user(
        user_id=1,
        username="testuser",
        email="<EMAIL>",
        nickname="测试用户"
    )


@pytest.fixture
def test_admin():
    """测试管理员"""
    return TestUserFactory.create_user(
        user_id=999,
        username="admin",
        email="<EMAIL>",
        nickname="管理员",
        is_superuser=True
    )


@pytest.fixture
def test_article():
    """测试文章"""
    return ArticleOut(
        id=1,
        title="测试文章",
        content="这是一篇测试文章的内容",
        author_id=1,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        view_count=100,
        like_count=50,
        comment_count=10
    )


@pytest.fixture
def auth_headers():
    """认证头"""
    return {"Authorization": "Bearer test_token"}


class TestArticleRecommendations:
    """文章推荐API测试"""
    
    def test_get_article_recommendations_success(self, client, test_user, auth_headers):
        """测试成功获取文章推荐"""
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.services.service_factory.get_recommendation_service') as mock_get_rec:
                mock_rec_service = AsyncMock()
                mock_rec_service.get_paginated_content_recommendations.return_value = []
                mock_get_rec.return_value = mock_rec_service
                
                with patch('app.services.service_factory.get_article_aggregation_service') as mock_get_agg:
                    mock_agg_service = AsyncMock()
                    mock_agg_service.get_aggregated_articles_by_ids.return_value = []
                    mock_get_agg.return_value = mock_agg_service
                    
                    response = client.get("/api/v1/articles/recommendations", headers=auth_headers)
                    
                    assert response.status_code == 200
                    data = response.json()
                    
                    # 验证分页响应格式
                    ResponseValidator.validate_pagination_response(data)
                    assert data.get("data", {}).get("items") == []
    
    def test_get_article_recommendations_unauthorized(self, client):
        """测试未授权访问"""
        response = client.get("/api/v1/articles/recommendations")
        assert response.status_code == 401
    
    def test_get_article_recommendations_with_pagination(self, client, test_user, auth_headers):
        """测试带分页参数的文章推荐"""
        pagination_params = {
            "cursor": "abc123",
            "size": 10,
            "order_by": "created_at",
            "order_direction": "desc"
        }
        
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.services.service_factory.get_recommendation_service') as mock_get_rec:
                mock_rec_service = AsyncMock()
                mock_rec_service.get_paginated_content_recommendations.return_value = []
                mock_get_rec.return_value = mock_rec_service
                
                with patch('app.services.service_factory.get_article_aggregation_service') as mock_get_agg:
                    mock_agg_service = AsyncMock()
                    mock_agg_service.get_aggregated_articles_by_ids.return_value = []
                    mock_get_agg.return_value = mock_agg_service
                    
                    response = client.get(
                        "/api/v1/articles/recommendations",
                        params=pagination_params,
                        headers=auth_headers
                    )
                    
                    assert response.status_code == 200
    
    def test_get_article_recommendations_empty_result(self, client, test_user, auth_headers):
        """测试空推荐结果"""
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.services.service_factory.get_recommendation_service') as mock_get_rec:
                mock_rec_service = AsyncMock()
                mock_rec_service.get_paginated_content_recommendations.return_value = []
                mock_get_rec.return_value = mock_rec_service
                
                with patch('app.services.service_factory.get_article_aggregation_service') as mock_get_agg:
                    mock_agg_service = AsyncMock()
                    mock_agg_service.get_aggregated_articles_by_ids.return_value = []
                    mock_get_agg.return_value = mock_agg_service
                    
                    response = client.get("/api/v1/articles/recommendations", headers=auth_headers)
                    
                    assert response.status_code == 200
                    data = response.json()
                    
                    # 验证空结果处理
                    assert data.get("success") is True
                    assert data.get("data", {}).get("items") == []
                    assert data.get("data", {}).get("total_count") == 0
                    assert data.get("data", {}).get("has_next") is False


class TestSimilarArticles:
    """相似文章API测试"""
    
    def test_get_similar_articles_success(self, client, test_article):
        """测试成功获取相似文章"""
        with patch('app.services.service_factory.get_article_cache_service') as mock_get_cache:
            mock_cache_service = AsyncMock()
            mock_cache_service.get_entity.return_value = test_article
            mock_get_cache.return_value = mock_cache_service
            
            with patch('app.services.service_factory.get_recommendation_service') as mock_get_rec:
                mock_rec_service = AsyncMock()
                mock_rec_service.get_similar_content.return_value = []
                mock_get_rec.return_value = mock_rec_service
                
                with patch('app.services.service_factory.get_article_aggregation_service') as mock_get_agg:
                    mock_agg_service = AsyncMock()
                    mock_agg_service.get_aggregated_articles_by_ids.return_value = []
                    mock_get_agg.return_value = mock_agg_service
                    
                    response = client.get(f"/api/v1/articles/{test_article.id}/similar")
                    
                    assert response.status_code == 200
                    data = response.json()
                    
                    # 验证响应格式
                    ResponseValidator.validate_success_response(data)
                    assert data.get("data") == []
    
    def test_get_similar_articles_not_found(self, client):
        """测试文章不存在"""
        with patch('app.services.service_factory.get_article_cache_service') as mock_get_cache:
            mock_cache_service = AsyncMock()
            mock_cache_service.get_entity.return_value = None
            mock_get_cache.return_value = mock_cache_service
            
            response = client.get("/api/v1/articles/999/similar")
            
            assert response.status_code == 404
            data = response.json()
            ResponseValidator.validate_error_response(data)
    
    def test_get_similar_articles_with_limit(self, client, test_article):
        """测试带数量限制的相似文章"""
        with patch('app.services.service_factory.get_article_cache_service') as mock_get_cache:
            mock_cache_service = AsyncMock()
            mock_cache_service.get_entity.return_value = test_article
            mock_get_cache.return_value = mock_cache_service
            
            with patch('app.services.service_factory.get_recommendation_service') as mock_get_rec:
                mock_rec_service = AsyncMock()
                mock_rec_service.get_similar_content.return_value = []
                mock_get_rec.return_value = mock_rec_service
                
                with patch('app.services.service_factory.get_article_aggregation_service') as mock_get_agg:
                    mock_agg_service = AsyncMock()
                    mock_agg_service.get_aggregated_articles_by_ids.return_value = []
                    mock_get_agg.return_value = mock_agg_service
                    
                    response = client.get(f"/api/v1/articles/{test_article.id}/similar?limit=5")
                    
                    assert response.status_code == 200
    
    def test_get_similar_articles_invalid_limit(self, client, test_article):
        """测试无效的数量限制"""
        invalid_limits = [0, 21, -1, "abc"]
        
        for limit in invalid_limits:
            response = client.get(f"/api/v1/articles/{test_article.id}/similar?limit={limit}")
            assert response.status_code == 422


class TestArticleCRUD:
    """文章CRUD操作测试"""
    
    def test_create_article_success(self, client, test_user, auth_headers):
        """测试成功创建文章"""
        article_data = {
            "title": "新文章标题",
            "content": "文章内容",
            "category_id": 1,
            "tags": ["标签1", "标签2"]
        }
        
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.crud.article.create') as mock_create:
                mock_create.return_value = ArticleOut(
                    id=1,
                    title=article_data["title"],
                    content=article_data["content"],
                    author_id=test_user.id,
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                    view_count=0,
                    like_count=0,
                    comment_count=0
                )
                
                response = client.post("/api/v1/articles/", json=article_data, headers=auth_headers)
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证响应格式
                ResponseValidator.validate_success_response(data)
                article_data_response = data.get("data", {})
                assert article_data_response.get("title") == article_data["title"]
    
    def test_create_article_missing_fields(self, client, test_user, auth_headers):
        """测试创建文章缺少必填字段"""
        incomplete_data = {"title": "只有标题"}
        
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            response = client.post("/api/v1/articles/", json=incomplete_data, headers=auth_headers)
            assert response.status_code == 422
    
    def test_create_article_unauthorized(self, client):
        """测试未授权创建文章"""
        article_data = {"title": "测试", "content": "内容"}
        response = client.post("/api/v1/articles/", json=article_data)
        assert response.status_code == 401
    
    def test_get_article_success(self, client, test_article):
        """测试成功获取文章"""
        with patch('app.services.service_factory.get_article_cache_service') as mock_get_cache:
            mock_cache_service = AsyncMock()
            mock_cache_service.get_entity.return_value = test_article
            mock_get_cache.return_value = mock_cache_service
            
            with patch('app.services.service_factory.get_article_aggregation_service') as mock_get_agg:
                mock_agg_service = AsyncMock()
                mock_agg_service.get_aggregated_articles_by_ids.return_value = [test_article]
                mock_get_agg.return_value = mock_agg_service
                
                response = client.get(f"/api/v1/articles/{test_article.id}")
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证响应格式
                ResponseValidator.validate_success_response(data)
                article_data = data.get("data", {})
                assert article_data.get("id") == test_article.id
    
    def test_get_article_not_found(self, client):
        """测试获取不存在的文章"""
        with patch('app.services.service_factory.get_article_cache_service') as mock_get_cache:
            mock_cache_service = AsyncMock()
            mock_cache_service.get_entity.return_value = None
            mock_get_cache.return_value = mock_cache_service
            
            response = client.get("/api/v1/articles/999")
            
            assert response.status_code == 404
            data = response.json()
            ResponseValidator.validate_error_response(data)
    
    def test_update_article_success(self, client, test_user, test_article, auth_headers):
        """测试成功更新文章"""
        update_data = {
            "title": "更新后的标题",
            "content": "更新后的内容"
        }
        
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.services.service_factory.get_article_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = test_article
                mock_get_cache.return_value = mock_cache_service
                
                with patch('app.crud.article.update') as mock_update:
                    mock_update.return_value = ArticleOut(
                        id=test_article.id,
                        title=update_data["title"],
                        content=update_data["content"],
                        author_id=test_user.id,
                        created_at=test_article.created_at,
                        updated_at=datetime.now(),
                        view_count=test_article.view_count,
                        like_count=test_article.like_count,
                        comment_count=test_article.comment_count
                    )
                    
                    response = client.put(
                        f"/api/v1/articles/{test_article.id}",
                        json=update_data,
                        headers=auth_headers
                    )
                    
                    assert response.status_code == 200
                    data = response.json()
                    
                    # 验证响应格式
                    ResponseValidator.validate_success_response(data)
                    article_data = data.get("data", {})
                    assert article_data.get("title") == update_data["title"]
    
    def test_update_article_not_owner(self, client, test_user, test_article, auth_headers):
        """测试非作者更新文章"""
        # 创建另一个用户
        other_user = TestUserFactory.create_user(user_id=2, username="otheruser")
        
        with patch('app.api.deps.get_current_active_user', return_value=other_user):
            with patch('app.services.service_factory.get_article_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = test_article
                mock_get_cache.return_value = mock_cache_service
                
                response = client.put(
                    f"/api/v1/articles/{test_article.id}",
                    json={"title": "更新"},
                    headers=auth_headers
                )
                
                assert response.status_code == 403
    
    def test_delete_article_success(self, client, test_user, test_article, auth_headers):
        """测试成功删除文章"""
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.services.service_factory.get_article_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = test_article
                mock_get_cache.return_value = mock_cache_service
                
                with patch('app.crud.article.remove') as mock_remove:
                    mock_remove.return_value = None
                    
                    response = client.delete(f"/api/v1/articles/{test_article.id}", headers=auth_headers)
                    
                    assert response.status_code == 204
    
    def test_delete_article_not_owner(self, client, test_user, test_article, auth_headers):
        """测试非作者删除文章"""
        other_user = TestUserFactory.create_user(user_id=2, username="otheruser")
        
        with patch('app.api.deps.get_current_active_user', return_value=other_user):
            with patch('app.services.service_factory.get_article_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = test_article
                mock_get_cache.return_value = mock_cache_service
                
                response = client.delete(f"/api/v1/articles/{test_article.id}", headers=auth_headers)
                
                assert response.status_code == 403


class TestArticleInteractions:
    """文章交互测试"""
    
    def test_like_article_success(self, client, test_user, test_article, auth_headers):
        """测试成功点赞文章"""
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.services.service_factory.get_article_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = test_article
                mock_get_cache.return_value = mock_cache_service
                
                with patch('app.crud.article.like') as mock_like:
                    mock_like.return_value = None
                    
                    response = client.post(f"/api/v1/articles/{test_article.id}/like", headers=auth_headers)
                    
                    assert response.status_code == 200
                    data = response.json()
                    
                    # 验证响应格式
                    ResponseValidator.validate_success_response(data)
    
    def test_unlike_article_success(self, client, test_user, test_article, auth_headers):
        """测试取消点赞文章"""
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.services.service_factory.get_article_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = test_article
                mock_get_cache.return_value = mock_cache_service
                
                with patch('app.crud.article.unlike') as mock_unlike:
                    mock_unlike.return_value = None
                    
                    response = client.delete(f"/api/v1/articles/{test_article.id}/like", headers=auth_headers)
                    
                    assert response.status_code == 200
                    data = response.json()
                    
                    # 验证响应格式
                    ResponseValidator.validate_success_response(data)
    
    def test_favorite_article_success(self, client, test_user, test_article, auth_headers):
        """测试成功收藏文章"""
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.services.service_factory.get_article_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = test_article
                mock_get_cache.return_value = mock_cache_service
                
                with patch('app.crud.article.favorite') as mock_favorite:
                    mock_favorite.return_value = None
                    
                    response = client.post(f"/api/v1/articles/{test_article.id}/favorite", headers=auth_headers)
                    
                    assert response.status_code == 200
                    data = response.json()
                    
                    # 验证响应格式
                    ResponseValidator.validate_success_response(data)
    
    def test_unfavorite_article_success(self, client, test_user, test_article, auth_headers):
        """测试取消收藏文章"""
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.services.service_factory.get_article_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = test_article
                mock_get_cache.return_value = mock_cache_service
                
                with patch('app.crud.article.unfavorite') as mock_unfavorite:
                    mock_unfavorite.return_value = None
                    
                    response = client.delete(f"/api/v1/articles/{test_article.id}/favorite", headers=auth_headers)
                    
                    assert response.status_code == 200
                    data = response.json()
                    
                    # 验证响应格式
                    ResponseValidator.validate_success_response(data)


class TestArticleSearch:
    """文章搜索测试"""
    
    def test_search_articles_success(self, client):
        """测试成功搜索文章"""
        search_params = {
            "q": "测试关键词",
            "category_id": 1,
            "tag": "标签",
            "author_id": 1,
            "page": 1,
            "size": 10
        }
        
        with patch('app.crud.article.search') as mock_search:
            mock_search.return_value = {
                "items": [],
                "total": 0,
                "pages": 0,
                "current_page": 1
            }
            
            response = client.get("/api/v1/articles/search", params=search_params)
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应格式
            ResponseValidator.validate_success_response(data)
    
    def test_search_articles_empty_query(self, client):
        """测试空搜索查询"""
        response = client.get("/api/v1/articles/search")
        assert response.status_code == 422
    
    def test_search_articles_with_filters(self, client):
        """测试带过滤条件的搜索"""
        filter_params = {
            "q": "测试",
            "category_id": 1,
            "tag": "技术",
            "author_id": 1,
            "date_from": "2024-01-01",
            "date_to": "2024-12-31",
            "sort_by": "created_at",
            "sort_order": "desc"
        }
        
        with patch('app.crud.article.search') as mock_search:
            mock_search.return_value = {
                "items": [],
                "total": 0,
                "pages": 0,
                "current_page": 1
            }
            
            response = client.get("/api/v1/articles/search", params=filter_params)
            
            assert response.status_code == 200


class TestArticleCategories:
    """文章分类测试"""
    
    def test_get_article_categories_success(self, client):
        """测试成功获取文章分类"""
        with patch('app.crud.category.get_all') as mock_get_all:
            mock_get_all.return_value = []
            
            response = client.get("/api/v1/articles/categories")
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应格式
            ResponseValidator.validate_success_response(data)
            assert data.get("data") == []
    
    def test_get_articles_by_category_success(self, client):
        """测试获取分类下的文章"""
        with patch('app.crud.article.get_by_category') as mock_get_by_category:
            mock_get_by_category.return_value = []
            
            response = client.get("/api/v1/articles/categories/1")
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应格式
            ResponseValidator.validate_success_response(data)


class TestArticleTags:
    """文章标签测试"""
    
    def test_get_article_tags_success(self, client):
        """测试成功获取文章标签"""
        with patch('app.crud.tag.get_all') as mock_get_all:
            mock_get_all.return_value = []
            
            response = client.get("/api/v1/articles/tags")
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应格式
            ResponseValidator.validate_success_response(data)
            assert data.get("data") == []
    
    def test_get_articles_by_tag_success(self, client):
        """测试获取标签下的文章"""
        with patch('app.crud.article.get_by_tag') as mock_get_by_tag:
            mock_get_by_tag.return_value = []
            
            response = client.get("/api/v1/articles/tags/technology")
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应格式
            ResponseValidator.validate_success_response(data)


class TestArticleComments:
    """文章评论测试"""
    
    def test_get_article_comments_success(self, client, test_article):
        """测试成功获取文章评论"""
        with patch('app.crud.comment.get_by_article') as mock_get_comments:
            mock_get_comments.return_value = []
            
            response = client.get(f"/api/v1/articles/{test_article.id}/comments")
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应格式
            ResponseValidator.validate_success_response(data)
            assert data.get("data") == []
    
    def test_create_article_comment_success(self, client, test_user, test_article, auth_headers):
        """测试成功创建文章评论"""
        comment_data = {
            "content": "这是一条评论",
            "parent_id": None
        }
        
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.crud.comment.create') as mock_create:
                mock_create.return_value = {
                    "id": 1,
                    "content": comment_data["content"],
                    "author_id": test_user.id,
                    "article_id": test_article.id,
                    "created_at": datetime.now()
                }
                
                response = client.post(
                    f"/api/v1/articles/{test_article.id}/comments",
                    json=comment_data,
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证响应格式
                ResponseValidator.validate_success_response(data)
                comment_data_response = data.get("data", {})
                assert comment_data_response.get("content") == comment_data["content"]


class TestArticleStats:
    """文章统计测试"""
    
    def test_get_article_stats_success(self, client, test_article):
        """测试成功获取文章统计"""
        with patch('app.crud.article.get_stats') as mock_get_stats:
            mock_get_stats.return_value = {
                "view_count": 100,
                "like_count": 50,
                "comment_count": 10,
                "favorite_count": 5,
                "share_count": 2
            }
            
            response = client.get(f"/api/v1/articles/{test_article.id}/stats")
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应格式
            ResponseValidator.validate_success_response(data)
            stats_data = data.get("data", {})
            assert stats_data.get("view_count") == 100
            assert stats_data.get("like_count") == 50


class TestArticleSecurity:
    """文章安全测试"""
    
    def test_sql_injection_prevention(self, client, test_user, auth_headers):
        """测试SQL注入防护"""
        malicious_inputs = [
            {"title": "test'; DROP TABLE articles; --"},
            {"content": "test' OR '1'='1"},
            {"title": "<script>alert('xss')</script>"}
        ]
        
        for malicious_input in malicious_inputs:
            with patch('app.api.deps.get_current_active_user', return_value=test_user):
                response = client.post(
                    "/api/v1/articles/",
                    json=malicious_input,
                    headers=auth_headers
                )
                # 应该被验证拦截或安全处理
                assert response.status_code in [422, 200]
    
    def test_xss_prevention(self, client, test_user, auth_headers):
        """测试XSS防护"""
        xss_payload = {"title": "<script>alert('xss')</script>", "content": "正常内容"}
        
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.crud.article.create') as mock_create:
                mock_create.return_value = ArticleOut(
                    id=1,
                    title="safe_title",  # 应该被清理
                    content=xss_payload["content"],
                    author_id=test_user.id,
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                    view_count=0,
                    like_count=0,
                    comment_count=0
                )
                
                response = client.post("/api/v1/articles/", json=xss_payload, headers=auth_headers)
                
                if response.status_code == 200:
                    data = response.json()
                    # 确保XSS脚本被转义或过滤
                    article_data = data.get("data", {})
                    title = article_data.get("title", "")
                    assert "<script>" not in title


class TestArticlePerformance:
    """文章性能测试"""
    
    def test_concurrent_article_requests(self, client, test_article):
        """测试并发文章请求"""
        import threading
        import time
        
        results = []
        
        def get_article():
            with patch('app.services.service_factory.get_article_cache_service') as mock_get_cache:
                mock_cache_service = AsyncMock()
                mock_cache_service.get_entity.return_value = test_article
                mock_get_cache.return_value = mock_cache_service
                
                with patch('app.services.service_factory.get_article_aggregation_service') as mock_get_agg:
                    mock_agg_service = AsyncMock()
                    mock_agg_service.get_aggregated_articles_by_ids.return_value = [test_article]
                    mock_get_agg.return_value = mock_agg_service
                    
                    response = client.get(f"/api/v1/articles/{test_article.id}")
                    results.append(response.status_code)
        
        # 创建多个并发请求
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=get_article)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证所有请求都得到响应
        assert len(results) == 10
        assert all(status == 200 for status in results)
    
    def test_article_response_time(self, client, test_article):
        """测试文章响应时间"""
        import time
        
        with patch('app.services.service_factory.get_article_cache_service') as mock_get_cache:
            mock_cache_service = AsyncMock()
            mock_cache_service.get_entity.return_value = test_article
            mock_get_cache.return_value = mock_cache_service
            
            with patch('app.services.service_factory.get_article_aggregation_service') as mock_get_agg:
                mock_agg_service = AsyncMock()
                mock_agg_service.get_aggregated_articles_by_ids.return_value = [test_article]
                mock_get_agg.return_value = mock_agg_service
                
                start_time = time.time()
                response = client.get(f"/api/v1/articles/{test_article.id}")
                end_time = time.time()
                
                response_time = end_time - start_time
                
                assert response.status_code == 200
                # 响应时间应该小于1秒
                assert response_time < 1.0