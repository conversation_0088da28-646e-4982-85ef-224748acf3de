"""
视频管理API测试用例

测试所有视频相关的API端点，包括：
- 视频推荐
- 相似视频
- 视频CRUD操作
- 视频点赞、收藏、浏览历史
- 视频评论
- 视频文件夹管理
- 权限控制
- 分页功能
"""

import pytest
from fastapi import FastAPI, Request
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta

from app.api.endpoints.videos import router as videos_router
from app.core.response_wrapper import ResponseCode
from app.models.user import User
from app.schemas.video import VideoOut, VideoCreate, VideoUpdate
from app.core.pagination import CursorPaginationResponse
from tests.utils.test_helpers import (
    MockDatabase, MockRequest, ResponseValidator, TestUtils,
    TestDataFactory, TestUserFactory
)


@pytest.fixture
def mock_db():
    """Mock数据库"""
    return MockDatabase()


@pytest.fixture
def test_app():
    """测试应用"""
    app = FastAPI()
    app.include_router(videos_router, prefix="/api/v1/videos", tags=["videos"])
    return app


@pytest.fixture
def client(test_app):
    """测试客户端"""
    return TestClient(test_app)


@pytest.fixture
def test_user():
    """测试用户"""
    return TestUserFactory.create_user(
        user_id=1,
        username="testuser",
        email="<EMAIL>",
        nickname="测试用户"
    )


@pytest.fixture
def test_admin():
    """测试管理员"""
    return TestUserFactory.create_user(
        user_id=999,
        username="admin",
        email="<EMAIL>",
        nickname="管理员",
        is_superuser=True
    )


@pytest.fixture
def test_video():
    """测试视频"""
    return VideoOut(
        id=1,
        title="测试视频",
        description="这是一个测试视频",
        author_id=1,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        view_count=1000,
        like_count=100,
        comment_count=20,
        duration=300,
        file_size=1048576,
        file_url="https://example.com/video.mp4",
        thumbnail_url="https://example.com/thumbnail.jpg"
    )


@pytest.fixture
def auth_headers():
    """认证头"""
    return {"Authorization": "Bearer test_token"}


class TestVideoRecommendations:
    """视频推荐API测试"""
    
    def test_get_video_recommendations_success(self, client, test_user, auth_headers):
        """测试成功获取视频推荐"""
        with patch('app.api.deps.get_current_user_optional', return_value=test_user):
            with patch('app.services.service_factory.get_recommendation_service') as mock_get_rec:
                mock_rec_service = AsyncMock()
                mock_rec_service.get_paginated_content_recommendations.return_value = []
                mock_get_rec.return_value = mock_rec_service
                
                with patch('app.services.service_factory.get_video_aggregation_service') as mock_get_agg:
                    mock_agg_service = AsyncMock()
                    mock_agg_service.get_aggregated_videos_by_ids.return_value = []
                    mock_get_agg.return_value = mock_agg_service
                    
                    response = client.get("/api/v1/videos/recommendations", headers=auth_headers)
                    
                    assert response.status_code == 200
                    data = response.json()
                    
                    # 验证分页响应格式
                    ResponseValidator.validate_pagination_response(data)
                    assert data.get("data", {}).get("items") == []
    
    def test_get_video_recommendations_unauthorized(self, client):
        """测试未授权访问"""
        with patch('app.api.deps.get_current_user_optional', return_value=None):
            response = client.get("/api/v1/videos/recommendations")
            assert response.status_code == 401
    
    def test_get_video_recommendations_with_pagination(self, client, test_user, auth_headers):
        """测试带分页参数的视频推荐"""
        pagination_params = {
            "cursor": "abc123",
            "size": 10,
            "order_by": "created_at",
            "order_direction": "desc"
        }
        
        with patch('app.api.deps.get_current_user_optional', return_value=test_user):
            with patch('app.services.service_factory.get_recommendation_service') as mock_get_rec:
                mock_rec_service = AsyncMock()
                mock_rec_service.get_paginated_content_recommendations.return_value = []
                mock_get_rec.return_value = mock_rec_service
                
                with patch('app.services.service_factory.get_video_aggregation_service') as mock_get_agg:
                    mock_agg_service = AsyncMock()
                    mock_agg_service.get_aggregated_videos_by_ids.return_value = []
                    mock_get_agg.return_value = mock_agg_service
                    
                    response = client.get(
                        "/api/v1/videos/recommendations",
                        params=pagination_params,
                        headers=auth_headers
                    )
                    
                    assert response.status_code == 200
    
    def test_get_video_recommendations_empty_result(self, client, test_user, auth_headers):
        """测试空推荐结果"""
        with patch('app.api.deps.get_current_user_optional', return_value=test_user):
            with patch('app.services.service_factory.get_recommendation_service') as mock_get_rec:
                mock_rec_service = AsyncMock()
                mock_rec_service.get_paginated_content_recommendations.return_value = []
                mock_get_rec.return_value = mock_rec_service
                
                with patch('app.services.service_factory.get_video_aggregation_service') as mock_get_agg:
                    mock_agg_service = AsyncMock()
                    mock_agg_service.get_aggregated_videos_by_ids.return_value = []
                    mock_get_agg.return_value = mock_agg_service
                    
                    response = client.get("/api/v1/videos/recommendations", headers=auth_headers)
                    
                    assert response.status_code == 200
                    data = response.json()
                    
                    # 验证空结果处理
                    assert data.get("success") is True
                    assert data.get("data", {}).get("items") == []
                    assert data.get("data", {}).get("total_count") == 0
                    assert data.get("data", {}).get("has_next") is False


class TestSimilarVideos:
    """相似视频API测试"""
    
    def test_get_similar_videos_success(self, client, test_video):
        """测试成功获取相似视频"""
        with patch('app.services.service_factory.get_video_aggregation_service') as mock_get_agg:
            mock_agg_service = AsyncMock()
            mock_agg_service.get_aggregated_videos_by_ids.return_value = []
            mock_get_agg.return_value = mock_agg_service
            
            with patch('app.services.service_factory.get_recommendation_service') as mock_get_rec:
                mock_rec_service = AsyncMock()
                mock_rec_service.get_similar_content.return_value = []
                mock_get_rec.return_value = mock_rec_service
                
                response = client.get(f"/api/v1/videos/{test_video.id}/similar")
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证分页响应格式
                ResponseValidator.validate_pagination_response(data)
                assert data.get("data", {}).get("items") == []
    
    def test_get_similar_videos_with_pagination(self, client, test_video):
        """测试带分页参数的相似视频"""
        pagination_params = {
            "cursor": "abc123",
            "size": 10,
            "order_by": "similarity",
            "order_direction": "desc"
        }
        
        with patch('app.services.service_factory.get_video_aggregation_service') as mock_get_agg:
            mock_agg_service = AsyncMock()
            mock_agg_service.get_aggregated_videos_by_ids.return_value = []
            mock_get_agg.return_value = mock_agg_service
            
            with patch('app.services.service_factory.get_recommendation_service') as mock_get_rec:
                mock_rec_service = AsyncMock()
                mock_rec_service.get_similar_content.return_value = []
                mock_get_rec.return_value = mock_rec_service
                
                response = client.get(
                    f"/api/v1/videos/{test_video.id}/similar",
                    params=pagination_params
                )
                
                assert response.status_code == 200


class TestVideoCRUD:
    """视频CRUD操作测试"""
    
    def test_create_video_success(self, client, test_user, auth_headers):
        """测试成功创建视频"""
        video_data = {
            "title": "新视频标题",
            "description": "视频描述",
            "category_id": 1,
            "tags": ["标签1", "标签2"],
            "duration": 300,
            "file_url": "https://example.com/video.mp4",
            "thumbnail_url": "https://example.com/thumbnail.jpg"
        }
        
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.crud.video.create') as mock_create:
                mock_create.return_value = VideoOut(
                    id=1,
                    title=video_data["title"],
                    description=video_data["description"],
                    author_id=test_user.id,
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                    view_count=0,
                    like_count=0,
                    comment_count=0,
                    duration=video_data["duration"],
                    file_url=video_data["file_url"],
                    thumbnail_url=video_data["thumbnail_url"]
                )
                
                response = client.post("/api/v1/videos/", json=video_data, headers=auth_headers)
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证响应格式
                ResponseValidator.validate_success_response(data)
                video_data_response = data.get("data", {})
                assert video_data_response.get("title") == video_data["title"]
    
    def test_create_video_missing_fields(self, client, test_user, auth_headers):
        """测试创建视频缺少必填字段"""
        incomplete_data = {"title": "只有标题"}
        
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            response = client.post("/api/v1/videos/", json=incomplete_data, headers=auth_headers)
            assert response.status_code == 422
    
    def test_create_video_unauthorized(self, client):
        """测试未授权创建视频"""
        video_data = {"title": "测试", "description": "内容", "file_url": "https://example.com/video.mp4"}
        response = client.post("/api/v1/videos/", json=video_data)
        assert response.status_code == 401
    
    def test_get_video_success(self, client, test_video):
        """测试成功获取视频"""
        with patch('app.services.service_factory.get_video_aggregation_service') as mock_get_agg:
            mock_agg_service = AsyncMock()
            mock_agg_service.get_aggregated_videos_by_ids.return_value = [test_video]
            mock_get_agg.return_value = mock_agg_service
            
            response = client.get(f"/api/v1/videos/{test_video.id}")
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应格式
            ResponseValidator.validate_success_response(data)
            video_data = data.get("data", {})
            assert video_data.get("id") == test_video.id
    
    def test_get_video_not_found(self, client):
        """测试获取不存在的视频"""
        with patch('app.services.service_factory.get_video_aggregation_service') as mock_get_agg:
            mock_agg_service = AsyncMock()
            mock_agg_service.get_aggregated_videos_by_ids.return_value = []
            mock_get_agg.return_value = mock_agg_service
            
            response = client.get("/api/v1/videos/999")
            
            assert response.status_code == 404
            data = response.json()
            ResponseValidator.validate_error_response(data)
    
    def test_update_video_success(self, client, test_user, test_video, auth_headers):
        """测试成功更新视频"""
        update_data = {
            "title": "更新后的标题",
            "description": "更新后的描述"
        }
        
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.crud.video.update') as mock_update:
                mock_update.return_value = VideoOut(
                    id=test_video.id,
                    title=update_data["title"],
                    description=update_data["description"],
                    author_id=test_user.id,
                    created_at=test_video.created_at,
                    updated_at=datetime.now(),
                    view_count=test_video.view_count,
                    like_count=test_video.like_count,
                    comment_count=test_video.comment_count,
                    duration=test_video.duration,
                    file_url=test_video.file_url,
                    thumbnail_url=test_video.thumbnail_url
                )
                
                response = client.put(
                    f"/api/v1/videos/{test_video.id}",
                    json=update_data,
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证响应格式
                ResponseValidator.validate_success_response(data)
                video_data = data.get("data", {})
                assert video_data.get("title") == update_data["title"]
    
    def test_update_video_not_owner(self, client, test_user, test_video, auth_headers):
        """测试非作者更新视频"""
        # 创建另一个用户
        other_user = TestUserFactory.create_user(user_id=2, username="otheruser")
        
        with patch('app.api.deps.get_current_active_user', return_value=other_user):
            response = client.put(
                f"/api/v1/videos/{test_video.id}",
                json={"title": "更新"},
                headers=auth_headers
            )
            
            assert response.status_code == 403
    
    def test_delete_video_success(self, client, test_user, test_video, auth_headers):
        """测试成功删除视频"""
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.crud.video.remove') as mock_remove:
                mock_remove.return_value = None
                
                response = client.delete(f"/api/v1/videos/{test_video.id}", headers=auth_headers)
                
                assert response.status_code == 204
    
    def test_delete_video_not_owner(self, client, test_user, test_video, auth_headers):
        """测试非作者删除视频"""
        other_user = TestUserFactory.create_user(user_id=2, username="otheruser")
        
        with patch('app.api.deps.get_current_active_user', return_value=other_user):
            response = client.delete(f"/api/v1/videos/{test_video.id}", headers=auth_headers)
            
            assert response.status_code == 403


class TestVideoInteractions:
    """视频交互测试"""
    
    def test_like_video_success(self, client, test_user, test_video, auth_headers):
        """测试成功点赞视频"""
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.crud.video.like') as mock_like:
                mock_like.return_value = None
                
                response = client.post(f"/api/v1/videos/{test_video.id}/like", headers=auth_headers)
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证响应格式
                ResponseValidator.validate_success_response(data)
    
    def test_unlike_video_success(self, client, test_user, test_video, auth_headers):
        """测试取消点赞视频"""
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.crud.video.unlike') as mock_unlike:
                mock_unlike.return_value = None
                
                response = client.delete(f"/api/v1/videos/{test_video.id}/like", headers=auth_headers)
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证响应格式
                ResponseValidator.validate_success_response(data)
    
    def test_favorite_video_success(self, client, test_user, test_video, auth_headers):
        """测试成功收藏视频"""
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.crud.video.favorite') as mock_favorite:
                mock_favorite.return_value = None
                
                response = client.post(f"/api/v1/videos/{test_video.id}/favorite", headers=auth_headers)
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证响应格式
                ResponseValidator.validate_success_response(data)
    
    def test_unfavorite_video_success(self, client, test_user, test_video, auth_headers):
        """测试取消收藏视频"""
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.crud.video.unfavorite') as mock_unfavorite:
                mock_unfavorite.return_value = None
                
                response = client.delete(f"/api/v1/videos/{test_video.id}/favorite", headers=auth_headers)
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证响应格式
                ResponseValidator.validate_success_response(data)
    
    def test_record_video_view_success(self, client, test_video):
        """测试成功记录视频观看"""
        view_data = {
            "video_id": test_video.id,
            "watch_time": 120,
            "completion_rate": 0.4
        }
        
        with patch('app.services.behavior_tracking_service.BehaviorTrackingService.record_video_view') as mock_record:
            mock_record.return_value = None
            
            response = client.post("/api/v1/videos/view", json=view_data)
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应格式
            ResponseValidator.validate_success_response(data)


class TestVideoSearch:
    """视频搜索测试"""
    
    def test_search_videos_success(self, client):
        """测试成功搜索视频"""
        search_params = {
            "q": "测试关键词",
            "category_id": 1,
            "tag": "标签",
            "author_id": 1,
            "duration_min": 60,
            "duration_max": 600,
            "page": 1,
            "size": 10
        }
        
        with patch('app.crud.video.search') as mock_search:
            mock_search.return_value = {
                "items": [],
                "total": 0,
                "pages": 0,
                "current_page": 1
            }
            
            response = client.get("/api/v1/videos/search", params=search_params)
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应格式
            ResponseValidator.validate_success_response(data)
    
    def test_search_videos_empty_query(self, client):
        """测试空搜索查询"""
        response = client.get("/api/v1/videos/search")
        assert response.status_code == 422
    
    def test_search_videos_with_filters(self, client):
        """测试带过滤条件的搜索"""
        filter_params = {
            "q": "测试",
            "category_id": 1,
            "tag": "娱乐",
            "author_id": 1,
            "duration_min": 60,
            "duration_max": 600,
            "date_from": "2024-01-01",
            "date_to": "2024-12-31",
            "sort_by": "view_count",
            "sort_order": "desc"
        }
        
        with patch('app.crud.video.search') as mock_search:
            mock_search.return_value = {
                "items": [],
                "total": 0,
                "pages": 0,
                "current_page": 1
            }
            
            response = client.get("/api/v1/videos/search", params=filter_params)
            
            assert response.status_code == 200


class TestVideoCategories:
    """视频分类测试"""
    
    def test_get_video_categories_success(self, client):
        """测试成功获取视频分类"""
        with patch('app.crud.category.get_all') as mock_get_all:
            mock_get_all.return_value = []
            
            response = client.get("/api/v1/videos/categories")
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应格式
            ResponseValidator.validate_success_response(data)
            assert data.get("data") == []
    
    def test_get_videos_by_category_success(self, client):
        """测试获取分类下的视频"""
        with patch('app.crud.video.get_by_category') as mock_get_by_category:
            mock_get_by_category.return_value = []
            
            response = client.get("/api/v1/videos/categories/1")
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应格式
            ResponseValidator.validate_success_response(data)


class TestVideoComments:
    """视频评论测试"""
    
    def test_get_video_comments_success(self, client, test_video):
        """测试成功获取视频评论"""
        with patch('app.crud.comment.get_by_video') as mock_get_comments:
            mock_get_comments.return_value = []
            
            response = client.get(f"/api/v1/videos/{test_video.id}/comments")
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应格式
            ResponseValidator.validate_success_response(data)
            assert data.get("data") == []
    
    def test_create_video_comment_success(self, client, test_user, test_video, auth_headers):
        """测试成功创建视频评论"""
        comment_data = {
            "content": "这是一条视频评论",
            "parent_id": None,
            "timestamp": 120  # 评论时间点（秒）
        }
        
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.crud.comment.create') as mock_create:
                mock_create.return_value = {
                    "id": 1,
                    "content": comment_data["content"],
                    "author_id": test_user.id,
                    "video_id": test_video.id,
                    "timestamp": comment_data["timestamp"],
                    "created_at": datetime.now()
                }
                
                response = client.post(
                    f"/api/v1/videos/{test_video.id}/comments",
                    json=comment_data,
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证响应格式
                ResponseValidator.validate_success_response(data)
                comment_data_response = data.get("data", {})
                assert comment_data_response.get("content") == comment_data["content"]


class TestVideoStats:
    """视频统计测试"""
    
    def test_get_video_stats_success(self, client, test_video):
        """测试成功获取视频统计"""
        with patch('app.crud.video.get_stats') as mock_get_stats:
            mock_get_stats.return_value = {
                "view_count": 1000,
                "like_count": 100,
                "comment_count": 20,
                "favorite_count": 10,
                "share_count": 5,
                "average_watch_time": 180,
                "completion_rate": 0.6
            }
            
            response = client.get(f"/api/v1/videos/{test_video.id}/stats")
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应格式
            ResponseValidator.validate_success_response(data)
            stats_data = data.get("data", {})
            assert stats_data.get("view_count") == 1000
            assert stats_data.get("like_count") == 100


class TestVideoFolders:
    """视频文件夹测试"""
    
    def test_get_video_folders_success(self, client, test_user, auth_headers):
        """测试成功获取视频文件夹"""
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.services.service_factory.get_video_folder_service') as mock_get_service:
                mock_service = AsyncMock()
                mock_service.get_user_folders.return_value = []
                mock_get_service.return_value = mock_service
                
                response = client.get("/api/v1/videos/folders", headers=auth_headers)
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证响应格式
                ResponseValidator.validate_success_response(data)
                assert data.get("data") == []
    
    def test_create_video_folder_success(self, client, test_user, auth_headers):
        """测试成功创建视频文件夹"""
        folder_data = {
            "name": "我的收藏",
            "description": "收藏的视频",
            "is_public": False
        }
        
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.services.service_factory.get_video_folder_service') as mock_get_service:
                mock_service = AsyncMock()
                mock_service.create_folder.return_value = {
                    "id": 1,
                    "name": folder_data["name"],
                    "description": folder_data["description"],
                    "user_id": test_user.id,
                    "created_at": datetime.now()
                }
                mock_get_service.return_value = mock_service
                
                response = client.post("/api/v1/videos/folders", json=folder_data, headers=auth_headers)
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证响应格式
                ResponseValidator.validate_success_response(data)
                folder_data_response = data.get("data", {})
                assert folder_data_response.get("name") == folder_data["name"]
    
    def test_add_video_to_folder_success(self, client, test_user, test_video, auth_headers):
        """测试成功添加视频到文件夹"""
        folder_data = {
            "folder_id": 1,
            "video_id": test_video.id
        }
        
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.services.service_factory.get_video_folder_service') as mock_get_service:
                mock_service = AsyncMock()
                mock_service.add_video_to_folder.return_value = None
                mock_get_service.return_value = mock_service
                
                response = client.post("/api/v1/videos/folders/add", json=folder_data, headers=auth_headers)
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证响应格式
                ResponseValidator.validate_success_response(data)
    
    def test_remove_video_from_folder_success(self, client, test_user, test_video, auth_headers):
        """测试成功从文件夹移除视频"""
        folder_data = {
            "folder_id": 1,
            "video_id": test_video.id
        }
        
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.services.service_factory.get_video_folder_service') as mock_get_service:
                mock_service = AsyncMock()
                mock_service.remove_video_from_folder.return_value = None
                mock_get_service.return_value = mock_service
                
                response = client.post("/api/v1/videos/folders/remove", json=folder_data, headers=auth_headers)
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证响应格式
                ResponseValidator.validate_success_response(data)


class TestVideoUpload:
    """视频上传测试"""
    
    def test_get_upload_url_success(self, client, test_user, auth_headers):
        """测试成功获取上传URL"""
        upload_data = {
            "filename": "test_video.mp4",
            "content_type": "video/mp4",
            "file_size": 1048576
        }
        
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.services.upload_service.get_upload_url') as mock_get_url:
                mock_get_url.return_value = {
                    "upload_url": "https://example.com/upload",
                    "file_id": "test_file_id",
                    "expires_at": datetime.now() + timedelta(hours=1)
                }
                
                response = client.post("/api/v1/videos/upload/url", json=upload_data, headers=auth_headers)
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证响应格式
                ResponseValidator.validate_success_response(data)
                upload_data_response = data.get("data", {})
                assert "upload_url" in upload_data_response
    
    def test_confirm_upload_success(self, client, test_user, auth_headers):
        """测试成功确认上传"""
        confirm_data = {
            "file_id": "test_file_id",
            "title": "上传的视频",
            "description": "视频描述",
            "category_id": 1
        }
        
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.services.upload_service.confirm_upload') as mock_confirm:
                mock_confirm.return_value = {
                    "video_id": 1,
                    "status": "completed"
                }
                
                response = client.post("/api/v1/videos/upload/confirm", json=confirm_data, headers=auth_headers)
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证响应格式
                ResponseValidator.validate_success_response(data)


class TestVideoSecurity:
    """视频安全测试"""
    
    def test_sql_injection_prevention(self, client, test_user, auth_headers):
        """测试SQL注入防护"""
        malicious_inputs = [
            {"title": "test'; DROP TABLE videos; --"},
            {"description": "test' OR '1'='1"},
            {"title": "<script>alert('xss')</script>"}
        ]
        
        for malicious_input in malicious_inputs:
            with patch('app.api.deps.get_current_active_user', return_value=test_user):
                response = client.post(
                    "/api/v1/videos/",
                    json=malicious_input,
                    headers=auth_headers
                )
                # 应该被验证拦截或安全处理
                assert response.status_code in [422, 200]
    
    def test_xss_prevention(self, client, test_user, auth_headers):
        """测试XSS防护"""
        xss_payload = {"title": "<script>alert('xss')</script>", "description": "正常描述"}
        
        with patch('app.api.deps.get_current_active_user', return_value=test_user):
            with patch('app.crud.video.create') as mock_create:
                mock_create.return_value = VideoOut(
                    id=1,
                    title="safe_title",  # 应该被清理
                    description=xss_payload["description"],
                    author_id=test_user.id,
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                    view_count=0,
                    like_count=0,
                    comment_count=0,
                    duration=0,
                    file_url="https://example.com/video.mp4",
                    thumbnail_url="https://example.com/thumbnail.jpg"
                )
                
                response = client.post("/api/v1/videos/", json=xss_payload, headers=auth_headers)
                
                if response.status_code == 200:
                    data = response.json()
                    # 确保XSS脚本被转义或过滤
                    video_data = data.get("data", {})
                    title = video_data.get("title", "")
                    assert "<script>" not in title


class TestVideoPerformance:
    """视频性能测试"""
    
    def test_concurrent_video_requests(self, client, test_video):
        """测试并发视频请求"""
        import threading
        import time
        
        results = []
        
        def get_video():
            with patch('app.services.service_factory.get_video_aggregation_service') as mock_get_agg:
                mock_agg_service = AsyncMock()
                mock_agg_service.get_aggregated_videos_by_ids.return_value = [test_video]
                mock_get_agg.return_value = mock_agg_service
                
                response = client.get(f"/api/v1/videos/{test_video.id}")
                results.append(response.status_code)
        
        # 创建多个并发请求
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=get_video)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证所有请求都得到响应
        assert len(results) == 10
        assert all(status == 200 for status in results)
    
    def test_video_response_time(self, client, test_video):
        """测试视频响应时间"""
        import time
        
        with patch('app.services.service_factory.get_video_aggregation_service') as mock_get_agg:
            mock_agg_service = AsyncMock()
            mock_agg_service.get_aggregated_videos_by_ids.return_value = [test_video]
            mock_get_agg.return_value = mock_agg_service
            
            start_time = time.time()
            response = client.get(f"/api/v1/videos/{test_video.id}")
            end_time = time.time()
            
            response_time = end_time - start_time
            
            assert response.status_code == 200
            # 响应时间应该小于1秒
            assert response_time < 1.0