"""
认证API测试用例

测试所有认证相关的API端点，包括：
- 短信验证码发送
- 短信登录/注册
- 设备验证
- 错误处理
- 频率限制
"""

import pytest
from fastapi import FastAPI, Request
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta

from app.api.endpoints.auth import router as auth_router
from app.core.response_wrapper import ResponseCode
from app.exceptions.auth import (
    AuthenticationError,
    SMSInvalidCodeError,
    SMSRateLimitError,
    DeviceVerificationError
)
from app.schemas.auth import AuthInitiationResponse, AuthCompletionResponse
from tests.utils.test_helpers import (
    MockAuthOrchestratorService, MockRequest, ResponseValidator,
    TestUtils, TestDataFactory
)


@pytest.fixture
def mock_auth_service():
    """Mock认证服务"""
    return MockAuthOrchestratorService()


@pytest.fixture
def test_app():
    """测试应用"""
    app = FastAPI()
    app.include_router(auth_router, prefix="/api/v1/auth", tags=["auth"])
    return app


@pytest.fixture
def client(test_app):
    """测试客户端"""
    return TestClient(test_app)


@pytest.fixture
def test_phone():
    """测试手机号"""
    return "13800138000"


@pytest.fixture
def test_verification_code():
    """测试验证码"""
    return "123456"


class TestSMSCodeAPI:
    """短信验证码API测试"""
    
    def test_send_sms_code_success(self, client, test_phone, mock_auth_service):
        """测试成功发送短信验证码"""
        with patch('app.services.auth_orchestrator_service.get_auth_orchestrator_service', return_value=mock_auth_service):
            response = client.post(
                "/api/v1/auth/sms/send-code",
                json={"phone": test_phone, "template_type": "LOGIN"}
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应格式
            ResponseValidator.validate_success_response(data)
            assert data.get("auth_type") == "sms"
            assert "expires_in" in data
    
    def test_send_sms_code_invalid_phone(self, client):
        """测试无效手机号"""
        invalid_phones = ["123", "1380013800", "138001380000", "not_a_phone"]
        
        for phone in invalid_phones:
            response = client.post(
                "/api/v1/auth/sms/send-code",
                json={"phone": phone, "template_type": "LOGIN"}
            )
            
            # 应该返回验证错误
            assert response.status_code == 422
    
    def test_send_sms_code_rate_limit(self, client, test_phone, mock_auth_service):
        """测试频率限制"""
        # 模拟频率限制
        mock_auth_service.redis.set(f"sms_rate_limit:{test_phone}", "1", ex=60)
        
        with patch('app.services.auth_orchestrator_service.get_auth_orchestrator_service', return_value=mock_auth_service):
            response = client.post(
                "/api/v1/auth/sms/send-code",
                json={"phone": test_phone, "template_type": "LOGIN"}
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证频率限制响应
            assert data.get("success") is False
            assert "retry_after" in data
            assert "频率过高" in data.get("message", "")
    
    def test_send_sms_code_missing_phone(self, client):
        """测试缺少手机号参数"""
        response = client.post(
            "/api/v1/auth/sms/send-code",
            json={"template_type": "LOGIN"}
        )
        
        assert response.status_code == 422
    
    def test_send_sms_code_service_error(self, client, test_phone):
        """测试服务错误"""
        # 模拟服务异常
        with patch('app.services.auth_orchestrator_service.get_auth_orchestrator_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.initiate_authentication.side_effect = AuthenticationError(
                message="服务暂时不可用",
                response_code=ResponseCode.SERVICE_UNAVAILABLE
            )
            mock_get_service.return_value = mock_service
            
            response = client.post(
                "/api/v1/auth/sms/send-code",
                json={"phone": test_phone, "template_type": "LOGIN"}
            )
            
            assert response.status_code == 503
            data = response.json()
            ResponseValidator.validate_error_response(data)


class TestSMSLoginAPI:
    """短信登录API测试"""
    
    def test_sms_login_success(self, client, test_phone, test_verification_code, mock_auth_service):
        """测试成功短信登录"""
        # 预设验证码
        mock_auth_service.redis.set(f"sms_code:{test_phone}", test_verification_code, ex=300)
        
        with patch('app.services.auth_orchestrator_service.get_auth_orchestrator_service', return_value=mock_auth_service):
            response = client.post(
                "/api/v1/auth/sms/login",
                json={"phone": test_phone, "code": test_verification_code}
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应格式
            ResponseValidator.validate_success_response(data)
            assert data.get("auth_type") == "sms"
            assert "access_token" in data
            assert "user" in data
            assert data.get("token_type") == "bearer"
    
    def test_sms_login_invalid_code(self, client, test_phone, mock_auth_service):
        """测试无效验证码"""
        # 预设错误的验证码
        mock_auth_service.redis.set(f"sms_code:{test_phone}", "654321", ex=300)
        
        with patch('app.services.auth_orchestrator_service.get_auth_orchestrator_service', return_value=mock_auth_service):
            response = client.post(
                "/api/v1/auth/sms/login",
                json={"phone": test_phone, "code": "123456"}  # 错误的验证码
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证失败响应
            assert data.get("success") is False
            assert "验证码错误" in data.get("message", "")
    
    def test_sms_login_expired_code(self, client, test_phone, mock_auth_service):
        """测试过期验证码"""
        # 预设过期的验证码
        mock_auth_service.redis.set(f"sms_code:{test_phone}", "123456", ex=-1)  # 立即过期
        
        with patch('app.services.auth_orchestrator_service.get_auth_orchestrator_service', return_value=mock_auth_service):
            response = client.post(
                "/api/v1/auth/sms/login",
                json={"phone": test_phone, "code": "123456"}
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证失败响应
            assert data.get("success") is False
            assert "验证码已过期" in data.get("message", "")
    
    def test_sms_login_missing_params(self, client):
        """测试缺少参数"""
        # 缺少手机号
        response = client.post(
            "/api/v1/auth/sms/login",
            json={"code": "123456"}
        )
        assert response.status_code == 422
        
        # 缺少验证码
        response = client.post(
            "/api/v1/auth/sms/login",
            json={"phone": "13800138000"}
        )
        assert response.status_code == 422
    
    def test_sms_login_invalid_code_format(self, client, test_phone):
        """测试验证码格式错误"""
        invalid_codes = ["123", "1234567", "abcdef", "12a456"]
        
        for code in invalid_codes:
            response = client.post(
                "/api/v1/auth/sms/login",
                json={"phone": test_phone, "code": code}
            )
            assert response.status_code == 422
    
    def test_sms_login_user_not_found_create_new(self, client, test_phone, test_verification_code, mock_auth_service):
        """测试用户不存在时自动创建"""
        # 预设验证码
        mock_auth_service.redis.set(f"sms_code:{test_phone}", test_verification_code, ex=300)
        
        with patch('app.services.auth_orchestrator_service.get_auth_orchestrator_service', return_value=mock_auth_service):
            response = client.post(
                "/api/v1/auth/sms/login",
                json={"phone": test_phone, "code": test_verification_code}
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证新用户创建
            assert data.get("success") is True
            user_data = data.get("user", {})
            assert user_data.get("username") == test_phone
            assert "email" in user_data
    
    def test_sms_login_device_verification_required(self, client, test_phone, test_verification_code):
        """测试需要设备验证的情况"""
        # 模拟需要设备验证的响应
        with patch('app.services.auth_orchestrator_service.get_auth_orchestrator_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.complete_authentication.return_value = AuthCompletionResponse(
                success=False,
                message="需要设备验证",
                auth_type="sms",
                requires_device_verification=True,
                verification_token="test_verification_token"
            )
            mock_get_service.return_value = mock_service
            
            response = client.post(
                "/api/v1/auth/sms/login",
                json={"phone": test_phone, "code": test_verification_code}
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证设备验证响应
            assert data.get("success") is False
            assert data.get("requires_device_verification") is True
            assert "verification_token" in data


class TestDeviceConfirmAPI:
    """设备确认API测试"""
    
    def test_device_confirm_success(self, client, mock_auth_service):
        """测试成功设备确认"""
        verification_token = "test_verification_token"
        verification_code = "123456"
        
        # 预设验证码
        mock_auth_service.redis.set(f"device_verification:{verification_token}", verification_code, ex=1800)
        
        with patch('app.services.auth_orchestrator_service.get_auth_orchestrator_service', return_value=mock_auth_service):
            response = client.post(
                "/api/v1/auth/device/confirm",
                json={
                    "verification_token": verification_token,
                    "code": verification_code
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应格式
            ResponseValidator.validate_success_response(data)
            assert "access_token" in data
            assert "user" in data
    
    def test_device_confirm_invalid_token(self, client):
        """测试无效验证令牌"""
        response = client.post(
            "/api/v1/auth/device/confirm",
            json={
                "verification_token": "invalid_token",
                "code": "123456"
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证失败响应
        assert data.get("success") is False
        assert "令牌无效" in data.get("message", "")
    
    def test_device_confirm_invalid_code(self, client, mock_auth_service):
        """测试无效验证码"""
        verification_token = "test_verification_token"
        
        # 预设错误的验证码
        mock_auth_service.redis.set(f"device_verification:{verification_token}", "654321", ex=1800)
        
        with patch('app.services.auth_orchestrator_service.get_auth_orchestrator_service', return_value=mock_auth_service):
            response = client.post(
                "/api/v1/auth/device/confirm",
                json={
                    "verification_token": verification_token,
                    "code": "123456"  # 错误的验证码
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证失败响应
            assert data.get("success") is False
            assert "验证码错误" in data.get("message", "")
    
    def test_device_confirm_expired_token(self, client, mock_auth_service):
        """测试过期令牌"""
        verification_token = "expired_token"
        verification_code = "123456"
        
        # 预设过期的令牌
        mock_auth_service.redis.set(f"device_verification:{verification_token}", verification_code, ex=-1)
        
        with patch('app.services.auth_orchestrator_service.get_auth_orchestrator_service', return_value=mock_auth_service):
            response = client.post(
                "/api/v1/auth/device/confirm",
                json={
                    "verification_token": verification_token,
                    "code": verification_code
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证失败响应
            assert data.get("success") is False
            assert "令牌已过期" in data.get("message", "")
    
    def test_device_confirm_missing_params(self, client):
        """测试缺少参数"""
        # 缺少验证令牌
        response = client.post(
            "/api/v1/auth/device/confirm",
            json={"code": "123456"}
        )
        assert response.status_code == 422
        
        # 缺少验证码
        response = client.post(
            "/api/v1/auth/device/confirm",
            json={"verification_token": "test_token"}
        )
        assert response.status_code == 422


class TestAuthErrorHandling:
    """认证错误处理测试"""
    
    def test_authentication_error_handling(self, client, test_phone):
        """测试认证异常处理"""
        with patch('app.services.auth_orchestrator_service.get_auth_orchestrator_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.initiate_authentication.side_effect = AuthenticationError(
                message="认证服务异常",
                response_code=ResponseCode.INTERNAL_SERVER_ERROR
            )
            mock_get_service.return_value = mock_service
            
            response = client.post(
                "/api/v1/auth/sms/send-code",
                json={"phone": test_phone, "template_type": "LOGIN"}
            )
            
            assert response.status_code == 500
            data = response.json()
            ResponseValidator.validate_error_response(data)
            ResponseValidator.assert_response_code(data, ResponseCode.INTERNAL_SERVER_ERROR)
    
    def test_network_error_handling(self, client, test_phone):
        """测试网络错误处理"""
        with patch('app.services.auth_orchestrator_service.get_auth_orchestrator_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.initiate_authentication.side_effect = Exception("网络连接失败")
            mock_get_service.return_value = mock_service
            
            response = client.post(
                "/api/v1/auth/sms/send-code",
                json={"phone": test_phone, "template_type": "LOGIN"}
            )
            
            assert response.status_code == 500
            data = response.json()
            ResponseValidator.validate_error_response(data)
    
    def test_database_error_handling(self, client, test_phone):
        """测试数据库错误处理"""
        with patch('app.services.auth_orchestrator_service.get_auth_orchestrator_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.initiate_authentication.side_effect = Exception("数据库连接失败")
            mock_get_service.return_value = mock_service
            
            response = client.post(
                "/api/v1/auth/sms/send-code",
                json={"phone": test_phone, "template_type": "LOGIN"}
            )
            
            assert response.status_code == 500
            data = response.json()
            ResponseValidator.validate_error_response(data)


class TestAuthSecurity:
    """认证安全测试"""
    
    def test_sql_injection_prevention(self, client):
        """测试SQL注入防护"""
        malicious_inputs = [
            "13800138000'; DROP TABLE users; --",
            "13800138000' OR '1'='1",
            "13800138000' UNION SELECT * FROM users--"
        ]
        
        for malicious_input in malicious_inputs:
            response = client.post(
                "/api/v1/auth/sms/send-code",
                json={"phone": malicious_input, "template_type": "LOGIN"}
            )
            # 应该被验证拦截，而不是数据库错误
            assert response.status_code == 422
    
    def test_xss_prevention(self, client):
        """测试XSS防护"""
        malicious_input = "<script>alert('xss')</script>"
        
        response = client.post(
            "/api/v1/auth/sms/send-code",
            json={"phone": "13800138000", "template_type": malicious_input}
        )
        
        # 应该被验证拦截
        assert response.status_code == 422
    
    def test_brute_force_protection(self, client, test_phone):
        """测试暴力破解防护"""
        # 模拟多次失败尝试
        with patch('app.services.auth_orchestrator_service.get_auth_orchestrator_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.complete_authentication.return_value = AuthCompletionResponse(
                success=False,
                message="验证码错误",
                auth_type="sms"
            )
            mock_get_service.return_value = mock_service
            
            # 模拟多次失败登录
            for i in range(5):
                response = client.post(
                    "/api/v1/auth/sms/login",
                    json={"phone": test_phone, "code": "wrong_code"}
                )
                
                if i < 4:  # 前4次应该返回验证错误
                    assert response.status_code == 200
                    data = response.json()
                    assert data.get("success") is False
                else:  # 第5次应该触发频率限制
                    assert response.status_code == 429


class TestAuthPerformance:
    """认证性能测试"""
    
    def test_concurrent_sms_requests(self, client, test_phone, mock_auth_service):
        """测试并发短信请求"""
        import threading
        import time
        
        results = []
        
        def send_request():
            response = client.post(
                "/api/v1/auth/sms/send-code",
                json={"phone": test_phone, "template_type": "LOGIN"}
            )
            results.append(response.status_code)
        
        # 创建多个并发请求
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=send_request)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证所有请求都得到响应
        assert len(results) == 10
        # 至少有一些请求应该成功（取决于频率限制设置）
        assert 200 in results
    
    def test_response_time(self, client, test_phone, mock_auth_service):
        """测试响应时间"""
        import time
        
        start_time = time.time()
        response = client.post(
            "/api/v1/auth/sms/send-code",
            json={"phone": test_phone, "template_type": "LOGIN"}
        )
        end_time = time.time()
        
        response_time = end_time - start_time
        
        assert response.status_code == 200
        # 响应时间应该小于1秒
        assert response_time < 1.0


class TestAuthIntegration:
    """认证集成测试"""
    
    def test_complete_auth_flow(self, client, test_phone, test_verification_code, mock_auth_service):
        """测试完整的认证流程"""
        # 1. 发送验证码
        with patch('app.services.auth_orchestrator_service.get_auth_orchestrator_service', return_value=mock_auth_service):
            response = client.post(
                "/api/v1/auth/sms/send-code",
                json={"phone": test_phone, "template_type": "LOGIN"}
            )
            assert response.status_code == 200
            
            # 2. 使用验证码登录
            mock_auth_service.redis.set(f"sms_code:{test_phone}", test_verification_code, ex=300)
            
            response = client.post(
                "/api/v1/auth/sms/login",
                json={"phone": test_phone, "code": test_verification_code}
            )
            assert response.status_code == 200
            
            data = response.json()
            assert data.get("success") is True
            assert "access_token" in data
            
            # 3. 使用token访问受保护资源
            token = data.get("access_token")
            headers = {"Authorization": f"Bearer {token}"}
            
            # 这里可以测试其他需要认证的端点
            # response = client.get("/api/v1/users/me", headers=headers)
            # assert response.status_code == 200
    
    def test_device_verification_flow(self, client, test_phone, test_verification_code):
        """测试设备验证流程"""
        # 1. 发送验证码
        response = client.post(
            "/api/v1/auth/sms/send-code",
            json={"phone": test_phone, "template_type": "LOGIN"}
        )
        assert response.status_code == 200
        
        # 2. 登录（触发设备验证）
        with patch('app.services.auth_orchestrator_service.get_auth_orchestrator_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.complete_authentication.return_value = AuthCompletionResponse(
                success=False,
                message="需要设备验证",
                auth_type="sms",
                requires_device_verification=True,
                verification_token="test_verification_token"
            )
            mock_get_service.return_value = mock_service
            
            response = client.post(
                "/api/v1/auth/sms/login",
                json={"phone": test_phone, "code": test_verification_code}
            )
            assert response.status_code == 200
            
            data = response.json()
            assert data.get("requires_device_verification") is True
            verification_token = data.get("verification_token")
            
            # 3. 完成设备验证
            with patch('app.services.auth_orchestrator_service.get_auth_orchestrator_service') as mock_get_service_2:
                mock_service_2 = AsyncMock()
                mock_service_2.handle_device_verification.return_value = AuthCompletionResponse(
                    success=True,
                    message="设备验证成功",
                    auth_type="sms",
                    access_token="final_access_token",
                    user={"id": 1, "username": test_phone}
                )
                mock_get_service_2.return_value = mock_service_2
                
                response = client.post(
                    "/api/v1/auth/device/confirm",
                    json={
                        "verification_token": verification_token,
                        "code": test_verification_code
                    }
                )
                assert response.status_code == 200
                
                data = response.json()
                assert data.get("success") is True
                assert "access_token" in data