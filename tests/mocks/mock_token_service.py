"""模拟Token服务实现，用于测试"""

from datetime import timedelta

from app.services.interfaces.token_service_interface import ITokenService


class MockTokenService(ITokenService):
    """模拟Token服务，用于测试"""

    def __init__(self):
        self.tokens = {}

    async def create_token(
        self,
        username: str,
        device_id: int | None = None,
        expires_delta: timedelta | None = None,
        token_type: str = "access",
    ) -> str:
        token = f"mock_token_{username}_{device_id or ''}"
        self.tokens[token] = {"username": username, "device_id": device_id}
        return token

    async def verify_token(self, token: str) -> dict | None:
        return self.tokens.get(token)

    async def revoke_token(self, token: str) -> bool:
        if token in self.tokens:
            del self.tokens[token]
            return True
        return False

    async def revoke_user_tokens(self, username: str) -> int:
        count = 0
        for token, info in list(self.tokens.items()):
            if info["username"] == username:
                del self.tokens[token]
                count += 1
        return count

    async def revoke_device_token(self, username: str, device_id: int) -> bool:
        for token, info in list(self.tokens.items()):
            if info["username"] == username and info["device_id"] == device_id:
                del self.tokens[token]
                return True
        return False

    async def get_user_active_tokens(self, username: str) -> list[dict]:
        return [info for info in self.tokens.values() if info["username"] == username]

    async def create_access_token(
        self, data: dict, expires_delta: timedelta | None = None, device_id: int | None = None
    ) -> str:
        return await self.create_token(data["sub"], device_id)

    async def refresh_token(self, refresh_token: str) -> str | None:
        info = await self.verify_token(refresh_token)
        if info:
            return await self.create_token(info["username"], info.get("device_id"))
        return None

    async def validate_token(self, token: str) -> dict | None:
        return await self.verify_token(token)
