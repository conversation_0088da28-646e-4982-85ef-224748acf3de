networks:
  steam_net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/24

services:
  postgres:
    image: postgres:15-alpine
    container_name: steam_postgres
    restart: always
    environment:
      POSTGRES_USER: steam_user
      POSTGRES_PASSWORD: steam_password
      POSTGRES_DB: steam_db
    ports:
      - "15432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U steam_user -d steam_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      steam_net:
        ipv4_address: **********

  redis-master:
    image: redis/redis-stack-server:latest
    container_name: redis-master
    hostname: redis-master
    restart: always
    environment:
      - REDIS_ARGS=--replica-announce-ip redis-master
    ports:
      - "6379:6379"
    volumes:
      - redis_master_data:/data
    networks:
      steam_net:
        ipv4_address: ***********

  redis-slave-1:
    image: redis/redis-stack-server:latest
    container_name: redis-slave-1
    hostname: redis-slave-1
    restart: always
    environment:
      - REDIS_ARGS=--slaveof redis-master 6379 --replica-announce-ip redis-slave-1
    depends_on:
      - redis-master
    ports:
      - "6380:6379"
    networks:
      steam_net:
        ipv4_address: ***********

  redis-slave-2:
    image: redis/redis-stack-server:latest
    container_name: redis-slave-2
    hostname: redis-slave-2
    restart: always
    environment:
      - REDIS_ARGS=--slaveof redis-master 6379 --replica-announce-ip redis-slave-2
    depends_on:
      - redis-master
    ports:
      - "6381:6379"
    networks:
      steam_net:
        ipv4_address: ***********

  redis-sentinel-1:
    image: redis:7-alpine
    container_name: redis-sentinel-1
    restart: always
    command: >
      sh -c "
        until redis-cli -h redis-master -p 6379 ping | grep -q PONG; do
          echo 'Waiting for redis-master to be ready...';
          sleep 5;
        done;
        echo 'Redis master is ready. Starting Sentinel.';
        redis-sentinel /usr/local/etc/redis/sentinel.conf
      "
    ports:
      - "26379:26379"
    volumes:
      - ./redis/sentinel-1:/usr/local/etc/redis
    depends_on:
      - redis-master
      - redis-slave-1
      - redis-slave-2
    networks:
      steam_net:
        ipv4_address: ***********

  redis-sentinel-2:
    image: redis:7-alpine
    container_name: redis-sentinel-2
    restart: always
    command: >
      sh -c "
        until redis-cli -h redis-master -p 6379 ping | grep -q PONG; do
          echo 'Waiting for redis-master to be ready...';
          sleep 5;
        done;
        echo 'Redis master is ready. Starting Sentinel.';
        redis-sentinel /usr/local/etc/redis/sentinel.conf
      "
    ports:
      - "26380:26379"
    volumes:
      - ./redis/sentinel-2:/usr/local/etc/redis
    depends_on:
      - redis-master
      - redis-slave-1
      - redis-slave-2
    networks:
      steam_net:
        ipv4_address: ***********

  redis-sentinel-3:
    image: redis:7-alpine
    container_name: redis-sentinel-3
    restart: always
    command: >
      sh -c "
        until redis-cli -h redis-master -p 6379 ping | grep -q PONG; do
          echo 'Waiting for redis-master to be ready...';
          sleep 5;
        done;
        echo 'Redis master is ready. Starting Sentinel.';
        redis-sentinel /usr/local/etc/redis/sentinel.conf
      "
    ports:
      - "26381:26379"
    volumes:
      - ./redis/sentinel-3:/usr/local/etc/redis
    depends_on:
      - redis-master
      - redis-slave-1
      - redis-slave-2
    networks:
      steam_net:
        ipv4_address: ***********

volumes:
  postgres_data:
  redis_master_data:
