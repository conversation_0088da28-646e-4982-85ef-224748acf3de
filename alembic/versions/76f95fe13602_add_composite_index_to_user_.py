"""add composite index to user_interactions for cache warmup

Revision ID: 76f95fe13602
Revises:
Create Date: 2025-08-01 03:55:02.123456

"""

from collections.abc import Sequence

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "76f95fe13602"
down_revision: str | None = "ab5bf1bdf3a0"  # Manually set to the last known correct revision
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(
        "ix_user_interactions_created_at_user_id",
        "user_interactions",
        ["created_at", "user_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_user_interactions_created_at_user_id", table_name="user_interactions")
    # ### end Alembic commands ###
