"""add is_default to tag

Revision ID: a46fcf9bed85
Revises: 3c43a0b18b5d
Create Date: 2025-07-23 06:55:57.685632

"""
from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "a46fcf9bed85"
down_revision: str | None = "3c43a0b18b5d"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("tags", sa.Column("is_default", sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("tags", "is_default")
    # ### end Alembic commands ###
