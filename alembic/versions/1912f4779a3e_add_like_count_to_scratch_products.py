"""Add like_count field to scratch_products table

Revision ID: 1912f4779a3e
Revises: 6443a1bae75d
Create Date: 2025-09-09 08:40:21.514000

"""
from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "1912f4779a3e"
down_revision: str | None = "6443a1bae75d"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Add like_count field to scratch_products table"""
    # 添加 like_count 字段
    op.add_column(
        "scratch_products",
        sa.Column(
            "like_count", sa.Integer(), nullable=False, server_default="0", comment="点赞次数"
        ),
    )


def downgrade() -> None:
    """Remove like_count field from scratch_products table"""
    # 删除 like_count 字段
    op.drop_column("scratch_products", "like_count")