"""add slug to video model

Revision ID: 8b30240ed8e2
Revises: d1cac16fbe47
Create Date: 2025-07-29 11:05:50.628200

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "8b30240ed8e2"
down_revision: str | None = "d1cac16fbe47"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("videos", sa.Column("slug", sa.String(length=255), nullable=True))
    op.create_index(op.f("ix_videos_slug"), "videos", ["slug"], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_videos_slug"), table_name="videos")
    op.drop_column("videos", "slug")
    # ### end Alembic commands ###
