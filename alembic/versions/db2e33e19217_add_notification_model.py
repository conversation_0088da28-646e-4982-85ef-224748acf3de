"""Add notification model

Revision ID: db2e33e19217
Revises: b8f46481d615
Create Date: 2025-01-18 00:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'db2e33e19217'
down_revision: Union[str, None] = 'b8f46481d615'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Check if enum types exist, if not create them
    connection = op.get_bind()
    
    # Check notification_type enum - use more robust check
    result = connection.execute(sa.text("""
        SELECT EXISTS (
            SELECT 1 FROM pg_type t
            JOIN pg_namespace n ON t.typnamespace = n.oid
            WHERE n.nspname = 'public' AND t.typname = 'notificationtype'
        )
    """)).scalar()

    if not result:
        try:
            notification_type = sa.Enum('info', 'success', 'warning', 'error', 'task_complete',
                                      'new_message', 'system_update', 'content_recommendation',
                                      'account_activity', name='notificationtype')
            notification_type.create(connection)
            print("Created notificationtype enum")
        except sa.exc.ProgrammingError as e:
            if 'already exists' in str(e):
                print("notificationtype enum already exists, skipping")
            else:
                raise
    
    # Check notification_priority enum - use more robust check
    result = connection.execute(sa.text("""
        SELECT EXISTS (
            SELECT 1 FROM pg_type t
            JOIN pg_namespace n ON t.typnamespace = n.oid
            WHERE n.nspname = 'public' AND t.typname = 'notificationpriority'
        )
    """)).scalar()

    if not result:
        try:
            notification_priority = sa.Enum('low', 'normal', 'high', 'urgent', name='notificationpriority')
            notification_priority.create(connection)
            print("Created notificationpriority enum")
        except sa.exc.ProgrammingError as e:
            if 'already exists' in str(e):
                print("notificationpriority enum already exists, skipping")
            else:
                raise
    
    # Check notification_status enum - use more robust check
    result = connection.execute(sa.text("""
        SELECT EXISTS (
            SELECT 1 FROM pg_type t
            JOIN pg_namespace n ON t.typnamespace = n.oid
            WHERE n.nspname = 'public' AND t.typname = 'notificationstatus'
        )
    """)).scalar()

    if not result:
        try:
            notification_status = sa.Enum('unread', 'read', 'deleted', name='notificationstatus')
            notification_status.create(connection)
            print("Created notificationstatus enum")
        except sa.exc.ProgrammingError as e:
            if 'already exists' in str(e):
                print("notificationstatus enum already exists, skipping")
            else:
                raise
    
    # Check if notifications table exists with more robust check
    result = connection.execute(sa.text("""
        SELECT EXISTS (
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = 'public' AND table_name = 'notifications'
        )
    """)).scalar()

    print(f"Notifications table exists: {result}")

    if not result:
        # Create notifications table using raw SQL to avoid auto-enum creation conflicts
        op.execute("""
            CREATE TABLE notifications (
                id SERIAL PRIMARY KEY,
                user_id INTEGER NOT NULL REFERENCES users(id),
                type notificationtype NOT NULL,
                priority notificationpriority,
                title VARCHAR(200) NOT NULL,
                message TEXT NOT NULL,
                status notificationstatus,
                data JSONB,
                action_url VARCHAR(500),
                expires_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                read_at TIMESTAMP
            )
        """)
        
        # Create indexes using raw SQL
        op.execute("CREATE INDEX IF NOT EXISTS ix_notifications_type ON notifications(type)")
        op.execute("CREATE INDEX IF NOT EXISTS ix_notifications_priority ON notifications(priority)")
        op.execute("CREATE INDEX IF NOT EXISTS ix_notifications_status ON notifications(status)")
        op.execute("CREATE INDEX IF NOT EXISTS ix_notifications_created_at ON notifications(created_at)")
        op.execute("CREATE INDEX IF NOT EXISTS ix_notifications_user_id ON notifications(user_id)")


def downgrade() -> None:
    # Drop table (this will automatically drop indexes)
    op.execute("DROP TABLE IF EXISTS notifications")

    # Note: Enums are intentionally NOT dropped in downgrade
    # because they might be used by other parts of the system
    # They should be dropped manually if needed
    print("Table notifications dropped. Enums preserved for safety.")