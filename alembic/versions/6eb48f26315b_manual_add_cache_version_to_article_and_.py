"""manual_add_cache_version_to_article_and_video

Revision ID: 6eb48f26315b
Revises: fd7c11231d03
Create Date: 2025-08-01 09:02:02.953206

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "6eb48f26315b"
down_revision: str | None = "fd7c11231d03"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "articles",
        sa.Column(
            "cache_version", sa.Integer(), server_default="1", nullable=False, comment="缓存版本号"
        ),
    )
    op.add_column(
        "videos",
        sa.Column(
            "cache_version", sa.Integer(), server_default="1", nullable=False, comment="缓存版本号"
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("videos", "cache_version")
    op.drop_column("articles", "cache_version")
    # ### end Alembic commands ###
