"""add_user_privacy_settings

Revision ID: ab5bf1bdf3a0
Revises: 5c89608094b8
Create Date: 2025-07-31 02:01:53.542956

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "ab5bf1bdf3a0"
down_revision: str | None = "5c89608094b8"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "users",
        sa.Column(
            "likes_privacy_settings",
            sa.Integer(),
            nullable=False,
            server_default="3",
            comment="点赞隐私设置 (位掩码: 1=文章, 2=视频)",
        ),
    )
    op.add_column(
        "users",
        sa.Column(
            "favorites_privacy_settings",
            sa.Integer(),
            nullable=False,
            server_default="3",
            comment="收藏隐私设置 (位掩码: 1=文章, 2=视频)",
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("users", "favorites_privacy_settings")
    op.drop_column("users", "likes_privacy_settings")
    # ### end Alembic commands ###
