"""add_optimized_indexes_for_user_devices

Revision ID: 107198bb9b79
Revises: fd9ddaf1fba1
Create Date: 2025-08-03 04:48:40.424908

"""
from collections.abc import Sequence

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "107198bb9b79"
down_revision: str | None = "fd9ddaf1fba1"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # 添加复合索引以优化常用查询
    # 用于根据用户ID和设备指纹查询设备的复合索引
    op.create_index(
        "ix_user_devices_user_id_fingerprint", 
        "user_devices", 
        ["user_id", "device_fingerprint"], 
        unique=False
    )
    
    # 用于查询用户活跃设备的复合索引
    op.create_index(
        "ix_user_devices_user_id_active_blocked", 
        "user_devices", 
        ["user_id", "is_active", "is_blocked"], 
        unique=False
    )
    
    # 用于查询信任设备的复合索引
    op.create_index(
        "ix_user_devices_user_id_trusted", 
        "user_devices", 
        ["user_id", "is_trusted"], 
        unique=False
    )
    
    # 用于按最后登录时间排序的索引
    op.create_index(
        "ix_user_devices_last_login_at", 
        "user_devices", 
        ["last_login_at"], 
        unique=False
    )
    
    # 用于信任分数查询的索引
    op.create_index(
        "ix_user_devices_trust_score", 
        "user_devices", 
        ["trust_score"], 
        unique=False
    )


def downgrade() -> None:
    # 删除添加的索引
    op.drop_index("ix_user_devices_trust_score", table_name="user_devices")
    op.drop_index("ix_user_devices_last_login_at", table_name="user_devices")
    op.drop_index("ix_user_devices_user_id_trusted", table_name="user_devices")
    op.drop_index("ix_user_devices_user_id_active_blocked", table_name="user_devices")
    op.drop_index("ix_user_devices_user_id_fingerprint", table_name="user_devices")
