"""add code field to scratch_products

Revision ID: 87b46ed94cd9
Revises: 1912f4779a3e
Create Date: 2025-09-10 02:27:22.679535

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '87b46ed94cd9'
down_revision = '1912f4779a3e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('scratch_products', sa.Column(
        'code', sa.JSON(), nullable=True, comment='项目代码'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('scratch_products', 'code')
    # ### end Alembic commands ###
