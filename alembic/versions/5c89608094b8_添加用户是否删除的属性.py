"""添加用户是否删除的属性

Revision ID: 5c89608094b8
Revises: e5a7f3c2b1d0
Create Date: 2025-07-30 08:29:29.079659

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "5c89608094b8"
down_revision: str | None = "e5a7f3c2b1d0"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "users",
        sa.Column("is_deleted", sa.<PERSON>(), nullable=False, server_default=sa.text("false")),
    )
    op.create_index(op.f("ix_users_is_deleted"), "users", ["is_deleted"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_users_is_deleted"), table_name="users")
    op.drop_column("users", "is_deleted")
    # ### end Alembic commands ###
