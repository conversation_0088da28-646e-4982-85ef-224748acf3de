"""添加category_type类型

Revision ID: 9c5bb53a41d3
Revises: ecdf08ebb63f
Create Date: 2025-07-11 09:44:00.527843

"""
from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "9c5bb53a41d3"
down_revision: str | None = "ecdf08ebb63f"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("categories", sa.Column("category_type", sa.String(length=50), nullable=False, comment="类别类型: article 或 video"))
    op.add_column("categories", sa.Column("article_count", sa.Integer(), nullable=False, comment="文章数量"))
    op.add_column("categories", sa.Column("video_count", sa.Integer(), nullable=False, comment="视频数量"))
    op.add_column("categories", sa.Column("tree_id", sa.Integer(), nullable=True))
    op.add_column("categories", sa.Column("lft", sa.Integer(), nullable=False))
    op.add_column("categories", sa.Column("rgt", sa.Integer(), nullable=False))
    op.add_column("categories", sa.Column("level", sa.Integer(), nullable=False))
    op.create_index("categories_level_idx", "categories", ["level"], unique=False)
    op.create_index("categories_lft_idx", "categories", ["lft"], unique=False)
    op.create_index("categories_rgt_idx", "categories", ["rgt"], unique=False)
    op.create_index(op.f("ix_categories_category_type"), "categories", ["category_type"], unique=False)
    op.drop_constraint(op.f("categories_parent_id_fkey"), "categories", type_="foreignkey")
    op.create_foreign_key(None, "categories", "categories", ["parent_id"], ["id"], ondelete="CASCADE")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "categories", type_="foreignkey")
    op.create_foreign_key(op.f("categories_parent_id_fkey"), "categories", "categories", ["parent_id"], ["id"])
    op.drop_index(op.f("ix_categories_category_type"), table_name="categories")
    op.drop_index("categories_rgt_idx", table_name="categories")
    op.drop_index("categories_lft_idx", table_name="categories")
    op.drop_index("categories_level_idx", table_name="categories")
    op.drop_column("categories", "level")
    op.drop_column("categories", "rgt")
    op.drop_column("categories", "lft")
    op.drop_column("categories", "tree_id")
    op.drop_column("categories", "video_count")
    op.drop_column("categories", "article_count")
    op.drop_column("categories", "category_type")
    # ### end Alembic commands ###
