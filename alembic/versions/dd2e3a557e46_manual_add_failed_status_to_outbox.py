"""manual_add_failed_status_to_outbox

Revision ID: dd2e3a557e46
Revises: 629f48cbb942
Create Date: 2025-08-01 08:00:30.123456

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "dd2e3a557e46"
down_revision: str | None = "629f48cbb942"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "outbox_messages",
        "status",
        existing_type=sa.VARCHAR(length=20),
        comment="消息状态 (pending, sent, failed)",
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "outbox_messages",
        "status",
        existing_type=sa.VARCHAR(length=20),
        comment="消息状态 (pending, sent)",
        existing_nullable=False,
    )
    # ### end Alembic commands ###
