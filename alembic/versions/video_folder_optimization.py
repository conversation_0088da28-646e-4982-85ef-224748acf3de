"""视频文件夹模型优化

Revision ID: video_folder_optimization
Revises: 198012b49797
Create Date: 2025-07-01 10:00:00.000000

"""
from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "video_folder_optimization"
down_revision: str | None = "198012b49797"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # 为 video_folders 表添加新字段
    op.add_column("video_folders", sa.Column("parent_id", sa.Integer(), nullable=True))
    op.add_column("video_folders", sa.Column("is_default", sa.<PERSON>(), nullable=False, server_default="false"))
    op.add_column("video_folders", sa.Column("is_public", sa.<PERSON>(), nullable=False, server_default="false"))
    op.add_column("video_folders", sa.Column("access_level", sa.String(length=20), nullable=False, server_default="private"))
    op.add_column("video_folders", sa.Column("cover_url", sa.String(length=255), nullable=True))
    op.add_column("video_folders", sa.Column("sort_order", sa.Integer(), nullable=False, server_default="0"))
    op.add_column("video_folders", sa.Column("is_deleted", sa.Boolean(), nullable=False, server_default="false"))
    op.add_column("video_folders", sa.Column("deleted_at", sa.DateTime(), nullable=True))
    
    # 创建外键约束和索引
    op.create_foreign_key(
        "fk_video_folders_parent_id", 
        "video_folders", "video_folders", 
        ["parent_id"], ["id"]
    )
    op.create_index(
        op.f("ix_video_folders_parent_id"), 
        "video_folders", ["parent_id"], 
        unique=False
    )
    op.create_index(
        op.f("ix_video_folders_is_default"), 
        "video_folders", ["is_default"], 
        unique=False
    )
    op.create_index(
        op.f("ix_video_folders_is_public"), 
        "video_folders", ["is_public"], 
        unique=False
    )
    op.create_index(
        op.f("ix_video_folders_access_level"), 
        "video_folders", ["access_level"], 
        unique=False
    )
    op.create_index(
        op.f("ix_video_folders_sort_order"), 
        "video_folders", ["sort_order"], 
        unique=False
    )
    op.create_index(
        op.f("ix_video_folders_is_deleted"), 
        "video_folders", ["is_deleted"], 
        unique=False
    )
    
    # 为 videos 表添加新字段
    op.add_column("videos", sa.Column("sort_order", sa.Integer(), nullable=False, server_default="0"))
    op.add_column("videos", sa.Column("is_deleted", sa.Boolean(), nullable=False, server_default="false"))
    op.add_column("videos", sa.Column("deleted_at", sa.DateTime(), nullable=True))
    
    # 创建索引
    op.create_index(
        op.f("ix_videos_sort_order"), 
        "videos", ["sort_order"], 
        unique=False
    )
    op.create_index(
        op.f("ix_videos_is_deleted"), 
        "videos", ["is_deleted"], 
        unique=False
    )
    
    # 注意：以下操作需要在应用程序代码中完成
    # 1. 为每个用户创建默认文件夹
    # 2. 将现有无文件夹视频移动到默认文件夹
    # 3. 修改 folder_id 为非空（这是破坏性更改，需谨慎处理）


def downgrade() -> None:
    # 删除 videos 表的索引和字段
    op.drop_index(op.f("ix_videos_is_deleted"), table_name="videos")
    op.drop_index(op.f("ix_videos_sort_order"), table_name="videos")
    op.drop_column("videos", "deleted_at")
    op.drop_column("videos", "is_deleted")
    op.drop_column("videos", "sort_order")
    
    # 删除 video_folders 表的索引、外键和字段
    op.drop_index(op.f("ix_video_folders_is_deleted"), table_name="video_folders")
    op.drop_index(op.f("ix_video_folders_sort_order"), table_name="video_folders")
    op.drop_index(op.f("ix_video_folders_access_level"), table_name="video_folders")
    op.drop_index(op.f("ix_video_folders_is_public"), table_name="video_folders")
    op.drop_index(op.f("ix_video_folders_is_default"), table_name="video_folders")
    op.drop_index(op.f("ix_video_folders_parent_id"), table_name="video_folders")
    op.drop_constraint("fk_video_folders_parent_id", "video_folders", type_="foreignkey")
    op.drop_column("video_folders", "deleted_at")
    op.drop_column("video_folders", "is_deleted")
    op.drop_column("video_folders", "sort_order")
    op.drop_column("video_folders", "cover_url")
    op.drop_column("video_folders", "access_level")
    op.drop_column("video_folders", "is_public")
    op.drop_column("video_folders", "is_default")
    op.drop_column("video_folders", "parent_id")