"""Add backpack_items table for Scratch backpack functionality

Revision ID: add_backpack_items_table
Revises: 4ee560f8471b
Create Date: 2025-09-22 07:50:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'add_backpack_items_table'
down_revision: Union[str, None] = '4ee560f8471b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Create backpack_items table"""
    
    # 创建背包项目类型枚举（如果不存在）
    op.execute("""
        DO $$ BEGIN
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'backpackitemtype') THEN
                CREATE TYPE backpackitemtype AS ENUM ('costume', 'sound', 'sprite', 'script');
            END IF;
        END $$;
    """)
    
    # 创建枚举对象引用已存在的类型
    backpack_item_type = postgresql.ENUM(
        'costume', 'sound', 'sprite', 'script',
        name='backpackitemtype',
        create_type=False
    )
    
    # 创建backpack_items表
    op.create_table(
        'backpack_items',
        sa.Column(
            'id',
            postgresql.UUID(as_uuid=True),
            primary_key=True,
            nullable=False,
            comment='项目唯一标识'
        ),
        sa.Column(
            'username',
            sa.String(50),
            nullable=False,
            index=True,
            comment='所属用户名'
        ),
        sa.Column(
            'type',
            backpack_item_type,
            nullable=False,
            index=True,
            comment='项目类型：costume/sound/sprite/script'
        ),
        sa.Column(
            'mime',
            sa.String(100),
            nullable=False,
            comment='资源MIME类型'
        ),
        sa.Column(
            'name',
            sa.String(255),
            nullable=False,
            comment='用户自定义名称'
        ),
        sa.Column(
            'body_path',
            sa.String(512),
            nullable=False,
            comment='资源文件相对路径'
        ),
        sa.Column(
            'thumbnail_path',
            sa.String(512),
            nullable=False,
            comment='缩略图路径'
        ),
        sa.Column(
            'size_bytes',
            sa.BigInteger,
            nullable=False,
            default=0,
            comment='主体资源大小（字节）'
        ),
        sa.Column(
            'extra_data',
            postgresql.JSONB,
            nullable=True,
            comment='预留字段（如分辨率、持续时长等）'
        ),
        sa.Column(
            'created_at',
            postgresql.TIMESTAMP(timezone=True),
            nullable=False,
            server_default=sa.text('CURRENT_TIMESTAMP'),
            comment='创建时间'
        )
    )
    
    # 创建索引
    op.create_index('ix_backpack_items_id', 'backpack_items', ['id'])
    op.create_index('ix_backpack_items_username', 'backpack_items', ['username'])
    op.create_index('ix_backpack_items_type', 'backpack_items', ['type'])
    op.create_index('ix_backpack_items_username_type', 'backpack_items', ['username', 'type'])
    op.create_index('ix_backpack_items_created_at', 'backpack_items', ['created_at'])


def downgrade() -> None:
    """Drop backpack_items table"""
    
    # 删除索引
    op.drop_index('ix_backpack_items_created_at', 'backpack_items')
    op.drop_index('ix_backpack_items_username_type', 'backpack_items')
    op.drop_index('ix_backpack_items_type', 'backpack_items')
    op.drop_index('ix_backpack_items_username', 'backpack_items')
    op.drop_index('ix_backpack_items_id', 'backpack_items')
    
    # 删除表
    op.drop_table('backpack_items')
    
    # 删除枚举类型
    backpack_item_type = postgresql.ENUM(name='backpackitemtype')
    backpack_item_type.drop(op.get_bind())
