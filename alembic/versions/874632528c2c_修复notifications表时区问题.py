"""修复notifications表时区问题

Revision ID: 874632528c2c
Revises: f0b6e1234567
Create Date: 2025-09-04 03:53:16.902401

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '874632528c2c'
down_revision: Union[str, None] = 'f0b6e1234567'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """将 notifications 表的 timestamp without time zone 字段转换为 timestamp with time zone"""

    # 定义需要修改的字段
    timestamp_columns = ['expires_at', 'created_at', 'read_at']

    # 检查表是否存在
    connection = op.get_bind()
    table_exists = connection.execute(
        sa.text("""
        SELECT EXISTS (
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = 'public' AND table_name = 'notifications'
        )
    """)
    ).scalar()

    if not table_exists:
        print("notifications 表不存在，跳过时区修复")
        return

    # 对每个字段执行转换
    for column_name in timestamp_columns:
        # 检查字段是否存在且为 timestamp without time zone
        result = connection.execute(
            sa.text("""
            SELECT COUNT(*) FROM information_schema.columns
            WHERE table_name = 'notifications'
            AND column_name = :column_name
            AND data_type = 'timestamp without time zone'
        """),
        {'column_name': column_name}
        ).scalar() or 0

        if result > 0:
            # 将 timestamp without time zone 转换为 timestamp with time zone
            # 假设现有数据都是 UTC 时间
            op.execute(f"""
                ALTER TABLE notifications
                ALTER COLUMN {column_name} TYPE timestamp with time zone
                USING {column_name} AT TIME ZONE 'UTC'
            """)
            print(f"已修复: notifications.{column_name}")
        else:
            print(f"跳过: notifications.{column_name} (字段不存在或已经是正确类型)")


def downgrade() -> None:
    """将 notifications 表的 timestamp with time zone 字段转换回 timestamp without time zone"""

    # 定义需要修改的字段
    timestamp_columns = ['expires_at', 'created_at', 'read_at']

    # 检查表是否存在
    connection = op.get_bind()
    table_exists = connection.execute(
        sa.text("""
        SELECT EXISTS (
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = 'public' AND table_name = 'notifications'
        )
    """)
    ).scalar()

    if not table_exists:
        print("notifications 表不存在，跳过时区回滚")
        return

    # 对每个字段执行回滚
    for column_name in timestamp_columns:
        # 检查字段是否存在且为 timestamp with time zone
        result = connection.execute(
            sa.text("""
            SELECT COUNT(*) FROM information_schema.columns
            WHERE table_name = 'notifications'
            AND column_name = :column_name
            AND data_type = 'timestamp with time zone'
        """),
        {'column_name': column_name}
        ).scalar() or 0

        if result > 0:
            # 将 timestamp with time zone 转换回 timestamp without time zone
            # 转换为 UTC 时间
            op.execute(f"""
                ALTER TABLE notifications
                ALTER COLUMN {column_name} TYPE timestamp without time zone
                USING {column_name} AT TIME ZONE 'UTC'
            """)
            print(f"已回滚: notifications.{column_name}")
        else:
            print(f"跳过: notifications.{column_name} (字段不存在或已经是正确类型)")
