"""新增文章slug字段

Revision ID: 13a77ce65fe7
Revises: 8b30240ed8e2
Create Date: 2025-07-29 12:07:42.145450

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "13a77ce65fe7"
down_revision: str | None = "8b30240ed8e2"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("articles", sa.Column("slug", sa.String(length=255), nullable=True))
    op.create_index(op.f("ix_articles_slug"), "articles", ["slug"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_articles_slug"), table_name="articles")
    op.drop_column("articles", "slug")
    # ### end Alembic commands ###
