"""修复时区问题_将timestamp字段改为with_time_zone

Revision ID: b8f46481d615
Revises: 9c0208e1afa1
Create Date: 2025-09-01 06:57:42.475181

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b8f46481d615'
down_revision: Union[str, None] = '9c0208e1afa1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """将所有 timestamp without time zone 字段转换为 timestamp with time zone"""
    
    # 定义需要修改的表和字段
    tables_and_columns = [
        ('articles', ['created_at', 'updated_at']),
        ('banners', ['created_at', 'updated_at']),
        ('categories', ['created_at', 'updated_at']),
        ('comments', ['created_at', 'updated_at']),
        ('favorites', ['created_at']),
        ('file_hash', ['created_at', 'updated_at']),
        ('history', ['created_at', 'updated_at']),
        ('likes', ['created_at']),
        ('outbox', ['created_at']),
        ('reviews', ['created_at', 'updated_at']),
        ('tags', ['created_at', 'updated_at']),
        ('user_behavior', ['created_at']),
        ('user_devices', ['created_at', 'updated_at']),
        ('user_stats', ['created_at', 'updated_at']),
        ('users', ['created_at', 'updated_at']),
        ('video_folders', ['created_at', 'updated_at']),
        ('videos', ['created_at', 'updated_at']),
    ]
    
    # 对每个表的每个字段执行转换
    for table_name, columns in tables_and_columns:
        for column_name in columns:
            # 检查字段是否存在
            connection = op.get_bind()
            result = connection.execute(
                sa.text("""
                SELECT COUNT(*) FROM information_schema.columns
                WHERE table_name = :table_name
                AND column_name = :column_name
                AND data_type = 'timestamp without time zone'
            """),
            {
                'table_name': table_name,
                'column_name': column_name
            }
            ).scalar() or 0
            
            if result > 0:
                # 将 timestamp without time zone 转换为 timestamp with time zone
                # 假设现有数据都是 UTC 时间
                op.execute(f"""
                    ALTER TABLE {table_name} 
                    ALTER COLUMN {column_name} TYPE timestamp with time zone 
                    USING {column_name} AT TIME ZONE 'UTC'
                """)
                print(f"已修复: {table_name}.{column_name}")


def downgrade() -> None:
    """回滚：将 timestamp with time zone 字段转换回 timestamp without time zone"""
    
    # 定义需要回滚的表和字段
    tables_and_columns = [
        ('articles', ['created_at', 'updated_at']),
        ('banners', ['created_at', 'updated_at']),
        ('categories', ['created_at', 'updated_at']),
        ('comments', ['created_at', 'updated_at']),
        ('favorites', ['created_at']),
        ('file_hash', ['created_at', 'updated_at']),
        ('history', ['created_at', 'updated_at']),
        ('likes', ['created_at']),
        ('outbox', ['created_at']),
        ('reviews', ['created_at', 'updated_at']),
        ('tags', ['created_at', 'updated_at']),
        ('user_behavior', ['created_at']),
        ('user_devices', ['created_at', 'updated_at']),
        ('user_stats', ['created_at', 'updated_at']),
        ('users', ['created_at', 'updated_at']),
        ('video_folders', ['created_at', 'updated_at']),
        ('videos', ['created_at', 'updated_at']),
    ]
    
    # 对每个表的每个字段执行回滚
    for table_name, columns in tables_and_columns:
        for column_name in columns:
            # 检查字段是否存在
            connection = op.get_bind()
            result = connection.execute(
                sa.text("""
                SELECT COUNT(*) FROM information_schema.columns
                WHERE table_name = :table_name
                AND column_name = :column_name
                AND data_type = 'timestamp with time zone'
            """),
            {
                'table_name': table_name,
                'column_name': column_name
            }
            ).scalar() or 0
            
            if result > 0:
                # 将 timestamp with time zone 转换回 timestamp without time zone
                # 转换为 UTC 并移除时区信息
                op.execute(f"""
                    ALTER TABLE {table_name} 
                    ALTER COLUMN {column_name} TYPE timestamp without time zone 
                    USING {column_name} AT TIME ZONE 'UTC'
                """)
                print(f"已回滚: {table_name}.{column_name}")
