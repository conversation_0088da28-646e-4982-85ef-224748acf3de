"""add file_metadata to file_hash table

Revision ID: 5a88de76b002
Revises: a46fcf9bed85
Create Date: 2025-07-24 07:02:37.367461

"""
from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "5a88de76b002"
down_revision: str | None = "a46fcf9bed85"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("file_hashes", sa.Column("file_metadata", sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("file_hashes", "file_metadata")
    # ### end Alembic commands ###
