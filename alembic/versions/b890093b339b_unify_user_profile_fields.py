"""unify_user_profile_fields

Revision ID: b890093b339b
Revises:
Create Date: 2025-08-01 11:28:49.983453

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "b890093b339b"
down_revision: str | None = None
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        "UPDATE users SET nickname = wechat_nickname WHERE nickname IS NULL AND wechat_nickname IS NOT NULL"
    )
    op.execute(
        "UPDATE users SET avatar = wechat_avatar WHERE avatar IS NULL AND wechat_avatar IS NOT NULL"
    )
    op.drop_column("users", "wechat_avatar")
    op.drop_column("users", "wechat_nickname")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "users",
        sa.Column(
            "wechat_nickname",
            sa.VARCHAR(length=100),
            autoincrement=False,
            nullable=True,
            comment="微信昵称",
        ),
    )
    op.add_column(
        "users",
        sa.Column(
            "wechat_avatar",
            sa.VARCHAR(length=255),
            autoincrement=False,
            nullable=True,
            comment="微信头像",
        ),
    )
    # ### end Alembic commands ###
