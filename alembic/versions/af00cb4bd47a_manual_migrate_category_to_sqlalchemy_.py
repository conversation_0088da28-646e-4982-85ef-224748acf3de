"""manual_migrate_category_to_sqlalchemy_orm_tree

Revision ID: af00cb4bd47a
Revises: 9c5bb53a41d3
Create Date: 2025-07-14 01:34:34.525115

"""

from collections.abc import Sequence

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "af00cb4bd47a"
down_revision: str | None = "9c5bb53a41d3"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f("ix_categories_parent_id"), "categories", ["parent_id"], unique=False)
    op.create_foreign_key(None, "categories", "categories", ["parent_id"], ["id"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "categories", type_="foreignkey")
    op.drop_index(op.f("ix_categories_parent_id"), table_name="categories")
    # ### end Alembic commands ###
