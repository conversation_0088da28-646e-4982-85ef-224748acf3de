"""Merge notification model

Revision ID: 9250b104347a
Revises: 4641a3860bdf, db2e33e19217
Create Date: 2025-09-04 03:04:00.993835

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9250b104347a'
down_revision: Union[str, None] = ('4641a3860bdf', 'db2e33e19217')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
