"""更新reply_to_id字段引用评论而非用户

Revision ID: 7d75c744a828
Revises: 5524063ab7a6
Create Date: 2025-07-01 11:19:58.795197

"""
from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "7d75c744a828"
down_revision: str | None = "5524063ab7a6"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f("fk_comments_reply_to_id_users"), "comments", type_="foreignkey")
    op.create_foreign_key(None, "comments", "comments", ["reply_to_id"], ["id"])
    op.alter_column("video_folders", "access_level",
               existing_type=sa.INTEGER(),
               nullable=True,
               comment="访问级别：0-私有，1-公开，2-链接可见，3-指定用户可见",
               existing_server_default=sa.text("0"))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("video_folders", "access_level",
               existing_type=sa.INTEGER(),
               nullable=False,
               comment=None,
               existing_comment="访问级别：0-私有，1-公开，2-链接可见，3-指定用户可见",
               existing_server_default=sa.text("0"))
    op.drop_constraint(None, "comments", type_="foreignkey")
    op.create_foreign_key(op.f("fk_comments_reply_to_id_users"), "comments", "users", ["reply_to_id"], ["id"])
    # ### end Alembic commands ###
