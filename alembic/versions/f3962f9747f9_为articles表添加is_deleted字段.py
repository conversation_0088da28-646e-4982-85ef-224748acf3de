"""为articles表添加is_deleted字段

Revision ID: f3962f9747f9
Revises: 107198bb9b79
Create Date: 2025-08-25 06:55:05.327438

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f3962f9747f9'
down_revision: Union[str, None] = '107198bb9b79'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 为articles表添加is_deleted字段和索引
    op.add_column('articles', sa.Column('is_deleted', sa.<PERSON>(), nullable=False, server_default='false'))
    op.create_index(op.f('ix_articles_is_deleted'), 'articles', ['is_deleted'], unique=False)


def downgrade() -> None:
    # 删除articles表的is_deleted字段和索引
    op.drop_index(op.f('ix_articles_is_deleted'), table_name='articles')
    op.drop_column('articles', 'is_deleted')
