"""添加用户封面字段

Revision ID: ecdf08ebb63f
Revises: 7d75c744a828
Create Date: 2025-07-09 01:36:54.178207

"""
from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "ecdf08ebb63f"
down_revision: str | None = "7d75c744a828"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("users", sa.Column("cover", sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("users", "cover")
    # ### end Alembic commands ###
