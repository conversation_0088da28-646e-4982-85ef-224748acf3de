"""Update timestamp fields to use consistent UTC datetime format

Revision ID: 6443a1bae75d
Revises: c071f8ff2365
Create Date: 2025-09-09 02:11:03.403986

This migration updates all timestamp fields in the database to use 
consistent DateTime(timezone=True) format with UTC timezone support.
"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '6443a1bae75d'
down_revision: Union[str, None] = 'c071f8ff2365'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade database schema to update timestamp fields."""
    
    # Update articles table timestamps
    op.alter_column('articles', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('articles', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update banners table timestamps
    op.alter_column('banners', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('banners', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('banners', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update categories table timestamps
    op.alter_column('categories', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('categories', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update comments table timestamps
    op.alter_column('comments', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('comments', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update favorites table timestamps
    op.alter_column('favorites', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('favorites', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update history table timestamps
    op.alter_column('history', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('history', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('history', 'last_visited_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update likes table timestamps
    op.alter_column('likes', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('likes', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update reviews table timestamps
    op.alter_column('reviews', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('reviews', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('reviews', 'reviewed_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update tags table timestamps
    op.alter_column('tags', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('tags', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update user_devices table timestamps
    op.alter_column('user_devices', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('user_devices', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('user_devices', 'last_login_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('user_devices', 'first_login_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update users table timestamps
    op.alter_column('users', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('users', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('users', 'last_login',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update video_folders table timestamps
    op.alter_column('video_folders', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('video_folders', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('video_folders', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update videos table timestamps
    op.alter_column('videos', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('videos', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('videos', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update file_hashes table timestamps
    op.alter_column('file_hashes', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('file_hashes', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update outbox_messages table timestamps
    op.alter_column('outbox_messages', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('outbox_messages', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update user_stats table timestamps
    op.alter_column('user_stats', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update user_role table timestamps
    op.alter_column('user_role', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('user_role', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update permission table timestamps
    op.alter_column('permission', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('permission', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update user_interactions table timestamps
    op.alter_column('user_interactions', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update user_profiles table timestamps
    op.alter_column('user_profiles', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('user_profiles', 'last_updated',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update recommendation_logs table timestamps
    op.alter_column('recommendation_logs', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('recommendation_logs', 'clicked_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update content_similarities table timestamps
    op.alter_column('content_similarities', 'calculated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update user_browse_history table timestamps
    op.alter_column('user_browse_history', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # Update scratch_products table timestamps
    op.alter_column('scratch_products', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('scratch_products', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade database schema to revert timestamp field changes."""
    
    # Revert scratch_products table timestamps
    op.alter_column('scratch_products', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('scratch_products', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert user_browse_history table timestamps
    op.alter_column('user_browse_history', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert content_similarities table timestamps
    op.alter_column('content_similarities', 'calculated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert recommendation_logs table timestamps
    op.alter_column('recommendation_logs', 'clicked_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('recommendation_logs', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert user_profiles table timestamps
    op.alter_column('user_profiles', 'last_updated',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('user_profiles', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert user_interactions table timestamps
    op.alter_column('user_interactions', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert permission table timestamps
    op.alter_column('permission', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('permission', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert user_role table timestamps
    op.alter_column('user_role', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('user_role', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert user_stats table timestamps
    op.alter_column('user_stats', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert outbox_messages table timestamps
    op.alter_column('outbox_messages', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('outbox_messages', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert file_hashes table timestamps
    op.alter_column('file_hashes', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('file_hashes', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert videos table timestamps
    op.alter_column('videos', 'deleted_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('videos', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('videos', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert video_folders table timestamps
    op.alter_column('video_folders', 'deleted_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('video_folders', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('video_folders', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert users table timestamps
    op.alter_column('users', 'last_login',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('users', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('users', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert user_devices table timestamps
    op.alter_column('user_devices', 'first_login_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('user_devices', 'last_login_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('user_devices', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('user_devices', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert tags table timestamps
    op.alter_column('tags', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('tags', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert reviews table timestamps
    op.alter_column('reviews', 'reviewed_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('reviews', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('reviews', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert likes table timestamps
    op.alter_column('likes', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('likes', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert history table timestamps
    op.alter_column('history', 'last_visited_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('history', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('history', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert favorites table timestamps
    op.alter_column('favorites', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('favorites', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert comments table timestamps
    op.alter_column('comments', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('comments', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert categories table timestamps
    op.alter_column('categories', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('categories', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert banners table timestamps
    op.alter_column('banners', 'deleted_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('banners', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('banners', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # Revert articles table timestamps
    op.alter_column('articles', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('articles', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    
    # ### end Alembic commands ###
