"""添加视频文件夹视频数量存储字段

Revision ID: c570640eb42d
Revises: video_folder_optimization
Create Date: 2025-07-01 06:30:59.910750

"""
from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "c570640eb42d"
down_revision: str | None = "video_folder_optimization"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # 添加 _video_count 字段
    op.add_column("video_folders", sa.Column("_video_count", sa.Integer(), nullable=True, comment="文件夹中的视频数量"))
    
    # 创建临时表来计算每个文件夹的视频数量
    conn = op.get_bind()
    conn.execute(sa.text("""
    UPDATE video_folders
    SET _video_count = (
        SELECT COUNT(*)
        FROM videos
        WHERE videos.folder_id = video_folders.id
        AND videos.is_deleted = FALSE
    )
    """))
    
    # 将 NULL 值设置为 0
    conn.execute(sa.text("UPDATE video_folders SET _video_count = 0 WHERE _video_count IS NULL"))
    
    # 修改列为非空
    op.alter_column("video_folders", "_video_count", nullable=False, server_default="0")


def downgrade() -> None:
    # 删除 _video_count 字段
    op.drop_column("video_folders", "_video_count")
