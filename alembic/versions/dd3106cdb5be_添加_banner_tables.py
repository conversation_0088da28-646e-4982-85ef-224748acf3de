"""添加 banner tables

Revision ID: dd3106cdb5be
Revises: add_video_dimensions
Create Date: 2025-07-21 03:04:16.070836

"""
from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "dd3106cdb5be"
down_revision: str | None = "add_video_dimensions"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table("banners",
    sa.Column("id", sa.Integer(), nullable=False),
    sa.Column("title", sa.String(length=255), nullable=False),
    sa.Column("description", sa.Text(), nullable=True),
    sa.Column("image_url", sa.String(length=512), nullable=False, comment="轮播图片URL"),
    sa.Column("link_url", sa.String(length=512), nullable=True, comment="点击跳转链接"),
    sa.Column("video_id", sa.Integer(), nullable=True, comment="关联的视频ID"),
    sa.Column("sort_order", sa.Integer(), nullable=True, comment="排序权重，值越大越靠前"),
    sa.Column("is_active", sa.Boolean(), nullable=False),
    sa.Column("is_deleted", sa.Boolean(), nullable=False),
    sa.Column("created_at", sa.DateTime(), nullable=True),
    sa.Column("updated_at", sa.DateTime(), nullable=True),
    sa.Column("deleted_at", sa.DateTime(), nullable=True),
    sa.Column("banner_type", sa.String(length=50), nullable=True, comment="轮播图类型：home-首页, category-分类页"),
    sa.ForeignKeyConstraint(["video_id"], ["videos.id"], ),
    sa.PrimaryKeyConstraint("id")
    )
    op.create_index(op.f("ix_banners_id"), "banners", ["id"], unique=False)
    op.create_index(op.f("ix_banners_is_active"), "banners", ["is_active"], unique=False)
    op.create_index(op.f("ix_banners_is_deleted"), "banners", ["is_deleted"], unique=False)
    op.create_index(op.f("ix_banners_title"), "banners", ["title"], unique=False)
    op.drop_index(op.f("categories_level_idx"), table_name="categories")
    op.drop_index(op.f("categories_lft_idx"), table_name="categories")
    op.drop_index(op.f("categories_rgt_idx"), table_name="categories")
    op.drop_constraint(op.f("categories_parent_id_fkey"), "categories", type_="foreignkey")
    op.drop_column("categories", "tree_id")
    op.drop_column("categories", "rgt")
    op.drop_column("categories", "lft")
    op.drop_column("categories", "level")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("categories", sa.Column("level", sa.INTEGER(), autoincrement=False, nullable=False))
    op.add_column("categories", sa.Column("lft", sa.INTEGER(), autoincrement=False, nullable=False))
    op.add_column("categories", sa.Column("rgt", sa.INTEGER(), autoincrement=False, nullable=False))
    op.add_column("categories", sa.Column("tree_id", sa.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key(op.f("categories_parent_id_fkey"), "categories", "categories", ["parent_id"], ["id"], ondelete="CASCADE")
    op.create_index(op.f("categories_rgt_idx"), "categories", ["rgt"], unique=False)
    op.create_index(op.f("categories_lft_idx"), "categories", ["lft"], unique=False)
    op.create_index(op.f("categories_level_idx"), "categories", ["level"], unique=False)
    op.drop_index(op.f("ix_banners_title"), table_name="banners")
    op.drop_index(op.f("ix_banners_is_deleted"), table_name="banners")
    op.drop_index(op.f("ix_banners_is_active"), table_name="banners")
    op.drop_index(op.f("ix_banners_id"), table_name="banners")
    op.drop_table("banners")
    # ### end Alembic commands ###
