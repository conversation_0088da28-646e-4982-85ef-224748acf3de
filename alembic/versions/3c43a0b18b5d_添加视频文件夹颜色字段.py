"""添加视频文件夹颜色字段

Revision ID: 3c43a0b18b5d
Revises: dd3106cdb5be
Create Date: 2025-07-22 07:21:37.831559

"""
from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "3c43a0b18b5d"
down_revision: str | None = "dd3106cdb5be"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("video_folders", sa.Column("description", sa.Text(), nullable=True, comment="文件夹描述"))
    op.add_column("video_folders", sa.Column("color", sa.String(length=20), nullable=True, comment="文件夹颜色，如#FF5733"))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("video_folders", "color")
    op.drop_column("video_folders", "description")
    # ### end Alembic commands ###
