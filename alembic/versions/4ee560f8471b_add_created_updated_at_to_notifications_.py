"""add_created_updated_at_to_notifications_only

Revision ID: 4ee560f8471b
Revises: 87b46ed94cd9
Create Date: 2025-09-10 02:59:27.874888

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '4ee560f8471b'
down_revision: Union[str, None] = '87b46ed94cd9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """添加 created_at 和 updated_at 字段到 notifications 表"""

    # 只添加缺失的字段以解决枚举问题
    # created_at 已经存在，跳过
    op.add_column('notifications', sa.Column(
        'updated_at',
        postgresql.TIMESTAMP(timezone=True),
        server_default=sa.text('CURRENT_TIMESTAMP'),
        onupdate=sa.text('CURRENT_TIMESTAMP'),
        nullable=True
    ))

    # 为现有记录填充时间戳
    op.execute("""
        UPDATE notifications
        SET created_at = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE created_at IS NULL OR updated_at IS NULL
    """)


def downgrade() -> None:
    """移除添加的字段"""
    op.drop_column('notifications', 'created_at')
    op.drop_column('notifications', 'updated_at')

    # ### end Alembic commands ###
