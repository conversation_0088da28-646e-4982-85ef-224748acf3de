"""migrate_backpack_username_to_user_id

Revision ID: 96c0db1daac8
Revises: add_backpack_items_table
Create Date: 2025-09-23 01:36:49.038240

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '96c0db1daac8'
down_revision: Union[str, None] = 'add_backpack_items_table'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """将 backpack_items 表的 username 字段替换为 user_id 外键"""
    
    # 步骤 1: 添加 user_id 字段（暂时允许为空）
    op.add_column('backpack_items',
        sa.Column('user_id', sa.Integer, nullable=True, comment='所属用户ID'))
    
    # 步骤 2: 数据迁移 - 根据 username 查找对应的 user.id
    # 注意：如果数据库为空或没有现有数据，这步会被跳过
    op.execute("""
        UPDATE backpack_items
        SET user_id = users.id
        FROM users
        WHERE backpack_items.username = users.username
        AND backpack_items.user_id IS NULL
    """)
    
    # 步骤 3: 删除没有匹配用户的记录（数据清理）
    op.execute("""
        DELETE FROM backpack_items
        WHERE user_id IS NULL
    """)
    
    # 步骤 4: 设置 user_id 为非空约束
    op.alter_column('backpack_items', 'user_id', nullable=False)
    
    # 步骤 5: 添加外键约束
    op.create_foreign_key(
        'fk_backpack_items_user_id',
        'backpack_items',
        'users',
        ['user_id'],
        ['id'],
        ondelete='CASCADE'
    )
    
    # 步骤 6: 重建索引（替换原有的 username 索引）
    # 删除原有索引
    op.drop_index('ix_backpack_items_username', 'backpack_items')
    op.drop_index('ix_backpack_items_username_type', 'backpack_items')
    
    # 创建新索引
    op.create_index('ix_backpack_items_user_id', 'backpack_items', ['user_id'])
    op.create_index('ix_backpack_items_user_id_type', 'backpack_items', ['user_id', 'type'])
    
    # 步骤 7: 删除 username 字段
    op.drop_column('backpack_items', 'username')


def downgrade() -> None:
    """回滚迁移：将 user_id 外键改回 username 字段"""
    
    # 步骤 1: 添加 username 字段
    op.add_column('backpack_items',
        sa.Column('username', sa.String(50), nullable=True, comment='所属用户名'))
    
    # 步骤 2: 数据迁移 - 根据 user_id 查找对应的 username
    op.execute("""
        UPDATE backpack_items
        SET username = users.username
        FROM users
        WHERE backpack_items.user_id = users.id
        AND backpack_items.username IS NULL
    """)
    
    # 步骤 3: 设置 username 为非空约束
    op.alter_column('backpack_items', 'username', nullable=False)
    
    # 步骤 4: 删除外键约束和新索引
    op.drop_constraint('fk_backpack_items_user_id', 'backpack_items', type_='foreignkey')
    op.drop_index('ix_backpack_items_user_id_type', 'backpack_items')
    op.drop_index('ix_backpack_items_user_id', 'backpack_items')
    
    # 步骤 5: 重建原有的 username 索引
    op.create_index('ix_backpack_items_username', 'backpack_items', ['username'])
    op.create_index('ix_backpack_items_username_type', 'backpack_items', ['username', 'type'])
    
    # 步骤 6: 删除 user_id 字段
    op.drop_column('backpack_items', 'user_id')
