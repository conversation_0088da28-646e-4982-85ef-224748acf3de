"""Create scratch_products table with adaptation fields

Revision ID: c071f8ff2365
Revises: 874632528c2c
Create Date: 2025-09-08 03:21:39.505658

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c071f8ff2365'
down_revision: Union[str, None] = '874632528c2c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """创建scratch_products表及其适配字段"""
    op.create_table('scratch_products',
        sa.Column('project_id', sa.Integer(), nullable=False, primary_key=True),
        sa.Column('title', sa.String(255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('cover_url', sa.String(512), comment='封面图URL'),
        sa.Column('author_id', sa.Integer(), nullable=False),
        sa.Column('is_published', sa.<PERSON>(), default=False),
        sa.Column('cache_version', sa.Integer(), nullable=False, server_default="1", comment="缓存版本号"),
        sa.Column('visit_count', sa.Integer(), default=0, comment="访问次数"),
        sa.Column('adapt_count', sa.Integer(), default=0),
        sa.Column('favorite_count', sa.Integer(), default=0),
        sa.Column('can_adapt', sa.Boolean(), default=True),
        # 改编关系字段
        sa.Column('original_project_id', sa.Integer()),
        sa.Column('root_project_id', sa.Integer()),
        sa.Column('adapt_level', sa.Integer(), nullable=False, server_default="0", comment="改编层级，0为原创"),
        sa.Column('adaptation_type', sa.String(20), nullable=False, server_default="'original'", comment="改编类型"),
        sa.Column('created_at', sa.TIMESTAMP(), nullable=False),
        sa.Column('updated_at', sa.TIMESTAMP(), nullable=False)
    )

    # 创建外键约束
    op.create_foreign_key(
        'fk_scratch_products_original_project_id',
        'scratch_products', 'scratch_products',
        ['original_project_id'], ['project_id']
    )
    op.create_foreign_key(
        'fk_scratch_products_root_project_id',
        'scratch_products', 'scratch_products',
        ['root_project_id'], ['project_id']
    )
    op.create_foreign_key(
        'fk_scratch_products_author_id',
        'scratch_products', 'users',
        ['author_id'], ['id']
    )

    # 创建索引
    op.create_index('ix_scratch_title', 'scratch_products', ['title'])
    op.create_index('ix_scratch_author_id', 'scratch_products', ['author_id'])
    op.create_index('ix_scratch_is_published', 'scratch_products', ['is_published'])
    op.create_index('idx_scratch_original_project', 'scratch_products', ['original_project_id'])
    op.create_index('idx_scratch_root_project', 'scratch_products', ['root_project_id'])
    op.create_index('idx_scratch_adapt_level', 'scratch_products', ['adapt_level'])
    op.create_index('idx_scratch_adaptation_type', 'scratch_products', ['adaptation_type'])

    # 添加约束
    op.create_check_constraint('check_no_self_reference', 'scratch_products',
        'project_id != original_project_id')
    op.create_check_constraint('check_adapt_level_limit', 'scratch_products',
        'adapt_level >= 0 AND adapt_level <= 5')


def downgrade() -> None:
    """回滚删除 scratch_products表及其适配字段"""
    # 删除约束
    op.drop_constraint('check_adapt_level_limit', 'scratch_products')
    op.drop_constraint('check_no_self_reference', 'scratch_products')

    # 删除索引
    op.drop_index('idx_scratch_adaptation_type', 'scratch_products')
    op.drop_index('idx_scratch_adapt_level', 'scratch_products')
    op.drop_index('idx_scratch_root_project', 'scratch_products')
    op.drop_index('idx_scratch_original_project', 'scratch_products')
    op.drop_index('ix_scratch_is_published', 'scratch_products')
    op.drop_index('ix_scratch_author_id', 'scratch_products')
    op.drop_index('ix_scratch_title', 'scratch_products')

    # 删除外键约束
    op.drop_constraint('fk_scratch_products_author_id', 'scratch_products')
    op.drop_constraint('fk_scratch_products_root_project_id', 'scratch_products')
    op.drop_constraint('fk_scratch_products_original_project_id', 'scratch_products')

    # 删除表
    op.drop_table('scratch_products')
