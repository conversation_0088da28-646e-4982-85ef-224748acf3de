"""add cache_version to user and create outbox table

Revision ID: 629f48cbb942
Revises: bfeaaee553cd
Create Date: 2025-08-01 07:28:57.682737

"""

from collections.abc import Sequence

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "629f48cbb942"
down_revision: str | None = "bfeaaee553cd"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "outbox_messages",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "topic", sa.String(length=100), nullable=False, comment="消息主题，如 user.updated"
        ),
        sa.Column("payload", postgresql.JSONB(), nullable=False, comment="消息内容"),
        sa.Column(
            "status", sa.String(length=20), nullable=False, comment="消息状态 (pending, sent)"
        ),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("outbox_messages_pkey")),
    )
    op.create_index(op.f("ix_outbox_messages_id"), "outbox_messages", ["id"], unique=False)
    op.create_index(op.f("ix_outbox_messages_status"), "outbox_messages", ["status"], unique=False)
    op.create_index(op.f("ix_outbox_messages_topic"), "outbox_messages", ["topic"], unique=False)
    op.add_column(
        "users",
        sa.Column(
            "cache_version", sa.Integer(), server_default="1", nullable=False, comment="缓存版本号"
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("users", "cache_version")
    op.drop_index(op.f("ix_outbox_messages_topic"), table_name="outbox_messages")
    op.drop_index(op.f("ix_outbox_messages_status"), table_name="outbox_messages")
    op.drop_index(op.f("ix_outbox_messages_id"), table_name="outbox_messages")
    op.drop_table("outbox_messages")
    # ### end Alembic commands ###
