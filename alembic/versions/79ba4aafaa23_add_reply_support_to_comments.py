"""add reply support to comments

Revision ID: 79ba4aafaa23
Revises: c570640eb42d
Create Date: 2025-07-01 09:30:09.541393

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "79ba4aafaa23"
down_revision: str | None = "c570640eb42d"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("comments", sa.Column("parent_id", sa.Integer(), nullable=True))
    op.create_foreign_key(None, "comments", "comments", ["parent_id"], ["id"])
    op.alter_column(
        "video_folders",
        "path",
        existing_type=sa.VARCHAR(length=255),
        comment="存储路径，格式为：/user_{user_id}/{folder_name}",
        existing_nullable=False,
    )
    op.alter_column(
        "video_folders",
        "is_public",
        existing_type=sa.BOOLEAN(),
        comment="是否公开可见",
        existing_nullable=False,
        existing_server_default=sa.text("false"),
    )
    # --- 手动修复 access_level 列的类型和默认值转换 ---
    op.execute("ALTER TABLE video_folders ALTER COLUMN access_level DROP DEFAULT;")
    op.execute("""
        ALTER TABLE video_folders
        ALTER COLUMN access_level TYPE INTEGER
        USING CASE
            WHEN access_level = 'private' THEN 0
            WHEN access_level = 'public' THEN 1
            WHEN access_level = 'link_visible' THEN 2
            WHEN access_level = 'specific_users' THEN 3
            ELSE 0
        END;
    """)
    op.execute("ALTER TABLE video_folders ALTER COLUMN access_level SET DEFAULT 0;")
    # --- 手动修复结束 ---
    op.alter_column(
        "video_folders",
        "cover_url",
        existing_type=sa.VARCHAR(length=255),
        type_=sa.String(length=512),
        comment="文件夹封面图URL",
        existing_nullable=True,
    )
    op.alter_column(
        "video_folders",
        "sort_order",
        existing_type=sa.INTEGER(),
        nullable=True,
        comment="排序权重，值越大越靠前",
        existing_server_default=sa.text("0"),
    )
    op.alter_column(
        "video_folders",
        "_video_count",
        existing_type=sa.INTEGER(),
        nullable=True,
        existing_comment="文件夹中的视频数量",
        existing_server_default=sa.text("0"),
    )
    op.drop_index(op.f("ix_video_folders_access_level"), table_name="video_folders")
    op.drop_index(op.f("ix_video_folders_is_default"), table_name="video_folders")
    op.drop_index(op.f("ix_video_folders_is_public"), table_name="video_folders")
    op.drop_index(op.f("ix_video_folders_sort_order"), table_name="video_folders")
    op.alter_column(
        "videos",
        "sort_order",
        existing_type=sa.INTEGER(),
        nullable=True,
        comment="排序权重，值越大越靠前",
        existing_server_default=sa.text("0"),
    )
    op.alter_column("videos", "folder_id", existing_type=sa.INTEGER(), nullable=False)
    op.drop_index(op.f("ix_videos_sort_order"), table_name="videos")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f("ix_videos_sort_order"), "videos", ["sort_order"], unique=False)
    op.alter_column("videos", "folder_id", existing_type=sa.INTEGER(), nullable=True)
    op.alter_column(
        "videos",
        "sort_order",
        existing_type=sa.INTEGER(),
        nullable=False,
        comment=None,
        existing_comment="排序权重，值越大越靠前",
        existing_server_default=sa.text("0"),
    )
    op.create_index(
        op.f("ix_video_folders_sort_order"), "video_folders", ["sort_order"], unique=False
    )
    op.create_index(
        op.f("ix_video_folders_is_public"), "video_folders", ["is_public"], unique=False
    )
    op.create_index(
        op.f("ix_video_folders_is_default"), "video_folders", ["is_default"], unique=False
    )
    op.create_index(
        op.f("ix_video_folders_access_level"), "video_folders", ["access_level"], unique=False
    )
    op.alter_column(
        "video_folders",
        "_video_count",
        existing_type=sa.INTEGER(),
        nullable=False,
        existing_comment="文件夹中的视频数量",
        existing_server_default=sa.text("0"),
    )
    op.alter_column(
        "video_folders",
        "sort_order",
        existing_type=sa.INTEGER(),
        nullable=False,
        comment=None,
        existing_comment="排序权重，值越大越靠前",
        existing_server_default=sa.text("0"),
    )
    op.alter_column(
        "video_folders",
        "cover_url",
        existing_type=sa.String(length=512),
        type_=sa.VARCHAR(length=255),
        comment=None,
        existing_comment="文件夹封面图URL",
        existing_nullable=True,
    )
    op.alter_column(
        "video_folders",
        "access_level",
        existing_type=sa.Integer(),
        type_=sa.VARCHAR(length=20),
        nullable=False,
        comment=None,
        existing_comment="访问级别：0-私有，1-公开，2-链接可见，3-指定用户可见",
        existing_server_default=sa.text("'private'::character varying"),
    )
    op.alter_column(
        "video_folders",
        "is_public",
        existing_type=sa.BOOLEAN(),
        comment=None,
        existing_comment="是否公开可见",
        existing_nullable=False,
        existing_server_default=sa.text("false"),
    )
    op.alter_column(
        "video_folders",
        "path",
        existing_type=sa.VARCHAR(length=255),
        comment=None,
        existing_comment="存储路径，格式为：/user_{user_id}/{folder_name}",
        existing_nullable=False,
    )
    op.drop_constraint(None, "comments", type_="foreignkey")
    op.drop_column("comments", "parent_id")
    # ### end Alembic commands ###
