"""Add notification model v3

Revision ID: f0b6e1234567
Revises: 9250b104347a
Create Date: 2025-01-18 00:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f0b6e1234567'
down_revision: Union[str, None] = '9250b104347a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # This migration has been modified to only add additional indexes
    # The notifications table was already created by db2e33e19217
    connection = op.get_bind()

    # Check if notifications table exists and add any missing indexes
    result = connection.execute(
        sa.text("""
        SELECT EXISTS (
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = 'public' AND table_name = 'notifications'
        )
    """)
    ).scalar()

    if not result:
        # If table doesn't exist, this should have been created by previous migration
        # Log the issue but don't fail
        print("WARNING: notifications table not found. Please ensure db2e33e19217 migration ran successfully.")
        return  # Exit early if table doesn't exist

    # Add any additional indexes that might be missing
    # Check if index exists before creating
    def index_exists(index_name):
        result = connection.execute(
            sa.text("""
            SELECT EXISTS (
                SELECT 1 FROM pg_indexes
                WHERE indexname = :index_name
            )
        """), {'index_name': index_name}
        ).scalar()
        return result

    # Create indexes only if they don't exist
    if not index_exists('ix_notifications_type'):
        try:
            op.create_index('ix_notifications_type', 'notifications', ['type'], unique=False)
            print("Created ix_notifications_type index")
        except Exception as e:
            print(f"Failed to create ix_notifications_type index: {e}")

    if not index_exists('ix_notifications_user_id'):
        try:
            op.create_index('ix_notifications_user_id', 'notifications', ['user_id'], unique=False)
            print("Created ix_notifications_user_id index")
        except Exception as e:
            print(f"Failed to create ix_notifications_user_id index: {e}")

    if not index_exists('ix_notifications_user_status'):
        try:
            op.create_index('ix_notifications_user_status', 'notifications', ['user_id', 'status'], unique=False)
            print("Created ix_notifications_user_status index")
        except Exception as e:
            print(f"Failed to create ix_notifications_user_status index: {e}")


def downgrade() -> None:
    # Modified to only drop indexes added by this migration
    # The main notifications table will be dropped by db2e33e19217 migration
    connection = op.get_bind()

    # Check if table exists before trying to drop indexes
    result = connection.execute(
        sa.text("""
        SELECT EXISTS (
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = 'public' AND table_name = 'notifications'
        )
    """)
    ).scalar()

    if not result:
        print("notifications table doesn't exist, skipping index drops")
        return

    # Helper function to check if index exists
    def index_exists(index_name):
        result = connection.execute(
            sa.text("""
            SELECT EXISTS (
                SELECT 1 FROM pg_indexes
                WHERE indexname = :index_name
            )
        """), {'index_name': index_name}
        ).scalar()
        return result

    # Drop indexes only if they exist
    if index_exists('ix_notifications_user_status'):
        try:
            op.drop_index('ix_notifications_user_status', table_name='notifications')
            print("Dropped ix_notifications_user_status index")
        except Exception as e:
            print(f"Failed to drop ix_notifications_user_status index: {e}")

    if index_exists('ix_notifications_user_id'):
        try:
            op.drop_index('ix_notifications_user_id', table_name='notifications')
            print("Dropped ix_notifications_user_id index")
        except Exception as e:
            print(f"Failed to drop ix_notifications_user_id index: {e}")

    if index_exists('ix_notifications_type'):
        try:
            op.drop_index('ix_notifications_type', table_name='notifications')
            print("Dropped ix_notifications_type index")
        except Exception as e:
            print(f"Failed to drop ix_notifications_type index: {e}")