"""Restore slug to videos table

Revision ID: e5a7f3c2b1d0
Revises: 62d02008b860
Create Date: 2025-07-30 02:51:00.000000

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "e5a7f3c2b1d0"
down_revision: str | None = "62d02008b860"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "videos", sa.Column("slug", sa.String(length=255), nullable=True, comment="视频别名")
    )
    op.create_index(op.f("ix_videos_slug"), "videos", ["slug"], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_videos_slug"), table_name="videos")
    op.drop_column("videos", "slug")
    # ### end Alembic commands ###
