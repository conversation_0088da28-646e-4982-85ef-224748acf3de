"""add user_stats table

Revision ID: bfeaaee553cd
Revises: 76f95fe13602
Create Date: 2025-08-01 06:30:18.086426

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "bfeaaee553cd"
down_revision: str | None = "76f95fe13602"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "user_stats",
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("total_likes_count", sa.Integer(), server_default="0", nullable=False),
        sa.Column("total_favorites_count", sa.Integer(), server_default="0", nullable=False),
        sa.Column("following_count", sa.Integer(), server_default="0", nullable=False),
        sa.Column("follower_count", sa.Integer(), server_default="0", nullable=False),
        sa.<PERSON>umn("article_count", sa.Integer(), server_default="0", nullable=False),
        sa.Column("video_count", sa.Integer(), server_default="0", nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("user_id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("user_stats")
    # ### end Alembic commands ###
