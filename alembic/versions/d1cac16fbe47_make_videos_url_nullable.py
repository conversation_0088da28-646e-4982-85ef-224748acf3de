"""make videos.url nullable

Revision ID: d1cac16fbe47
Revises: 793d9bbd0b7f
Create Date: 2025-07-24 08:37:10.843056

"""
from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "d1cac16fbe47"
down_revision: str | None = "793d9bbd0b7f"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("videos", "url",
               existing_type=sa.VARCHAR(length=512),
               nullable=True,
               existing_comment="视频URL")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("videos", "url",
               existing_type=sa.VARCHAR(length=512),
               nullable=False,
               existing_comment="视频URL")
    # ### end Alembic commands ###
