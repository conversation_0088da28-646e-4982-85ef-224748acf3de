"""添加历史记录模型-02

Revision ID: a13e82222cd2
Revises: a3ba9f63577c
Create Date: 2025-06-27 10:08:23.427339

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "a13e82222cd2"
down_revision: str | None = "a3ba9f63577c"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "history",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("content_type", sa.String(length=20), nullable=False),
        sa.Column("content_id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "user_id", "content_type", "content_id", name="uq_user_content_history"
        ),
    )
    op.create_index(op.f("ix_history_content_id"), "history", ["content_id"], unique=False)
    op.create_index(op.f("ix_history_content_type"), "history", ["content_type"], unique=False)
    op.create_index(op.f("ix_history_created_at"), "history", ["created_at"], unique=False)
    op.create_index(op.f("ix_history_id"), "history", ["id"], unique=False)
    op.create_index(op.f("ix_history_user_id"), "history", ["user_id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_history_user_id"), table_name="history")
    op.drop_index(op.f("ix_history_id"), table_name="history")
    op.drop_index(op.f("ix_history_created_at"), table_name="history")
    op.drop_index(op.f("ix_history_content_type"), table_name="history")
    op.drop_index(op.f("ix_history_content_id"), table_name="history")
    op.drop_table("history")
    # ### end Alembic commands ###
