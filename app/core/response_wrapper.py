"""统一的响应包装工具模块"""

from datetime import datetime
from enum import Enum
from typing import Any, Generic, TypeVar

from pydantic import BaseModel, Field

T = TypeVar("T")


class APIResponse(BaseModel, Generic[T]):
    """统一的API响应模型"""

    success: bool = Field(..., description="请求是否成功")
    code: str = Field(..., description="业务状态码")
    message: str | None = Field(None, description="响应消息")
    data: T | None = Field(None, description="响应数据")
    timestamp: str = Field(..., description="服务器时间戳")


class ResponseCode(Enum):
    """业务状态码枚举"""
    
    def __init__(self, status_code, error_code, message):
        self._status_code = status_code
        self._error_code = error_code
        self._message = message

    @property
    def status_code(self):
        return self._status_code
        
    @property
    def error_code(self):
        return self._error_code

    @property
    def message(self):
        return self._message
    
    @property
    def code(self):
        """返回状态码的字符串形式"""
        return self._error_code if self._error_code else str(self._status_code)

    # General Success & Errors
    SUCCESS = (200, "OK", "操作成功")
    FAILURE = (400, "FAIL", "操作失败")
    
    # Common Client Errors
    BAD_REQUEST = (400, "COMMON_BAD_REQUEST", "无效的请求")
    UNAUTHORIZED = (401, "COMMON_UNAUTHORIZED", "未经授权")
    FORBIDDEN = (403, "COMMON_FORBIDDEN", "禁止访问")
    NOT_FOUND = (404, "COMMON_NOT_FOUND", "资源未找到")
    VALIDATION_ERROR = (422, "COMMON_VALIDATION_ERROR", "参数验证失败")

    # Common Server Errors
    INTERNAL_SERVER_ERROR = (500, "SERVER_INTERNAL_ERROR", "服务器内部错误")
    SERVICE_UNAVAILABLE = (503, "SERVER_SERVICE_UNAVAILABLE", "服务不可用")

    # --- Authentication Module Errors (AUTH_xxx) ---
    # User Sub-module
    AUTH_USER_NOT_FOUND = (404, "AUTH_USER_001", "用户不存在")
    AUTH_INVALID_CREDENTIALS = (401, "AUTH_USER_002", "无效的凭据或密码错误")
    AUTH_USER_DISABLED = (403, "AUTH_USER_003", "用户已被禁用")
    AUTH_USER_ALREADY_EXISTS = (409, "AUTH_USER_004", "用户已存在")
    
    # Device Sub-module
    AUTH_DEVICE_NOT_TRUSTED = (403, "AUTH_DEVICE_001", "设备不受信任")
    AUTH_DEVICE_VERIFICATION_REQUIRED = (403, "AUTH_DEVICE_002", "需要设备验证")
    AUTH_DEVICE_VERIFICATION_FAILED = (400, "AUTH_DEVICE_009", "设备验证失败")
    
    # SMS Sub-module
    AUTH_SMS_INVALID_CODE = (400, "AUTH_SMS_001", "无效的验证码")
    AUTH_SMS_CODE_EXPIRED = (400, "AUTH_SMS_002", "验证码已过期")
    AUTH_SMS_RATE_LIMIT = (429, "AUTH_SMS_003", "短信发送频率过高")
    
    # WeChat Sub-module
    AUTH_WECHAT_API_ERROR = (502, "AUTH_WECHAT_001", "微信服务调用失败")
    AUTH_WECHAT_INVALID_OPENID = (400, "AUTH_WECHAT_002", "无效的OpenID")

    # --- Article Module Errors (ARTICLE_xxx) ---
    ARTICLE_NOT_FOUND = (404, "ARTICLE_001", "文章不存在")
    ARTICLE_PERMISSION_DENIED = (403, "ARTICLE_002", "无权操作此文章")
    ARTICLE_ALREADY_EXISTS = (409, "ARTICLE_003", "具有相同标题的文章已存在")

    # --- Video Module Errors (VIDEO_xxx) ---
    VIDEO_NOT_FOUND = (404, "VIDEO_001", "视频不存在")
    VIDEO_PERMISSION_DENIED = (403, "VIDEO_002", "无权操作此视频")
    VIDEO_FOLDER_NOT_FOUND = (404, "VIDEO_003", "视频文件夹不存在")


class ResponseWrapper:
    """响应构建辅助工具"""
    
    @staticmethod
    def success(data: Any = None, code: ResponseCode = ResponseCode.SUCCESS, message: str | None = None) -> dict:
        """
        创建成功响应
        
        Args:
            data: 响应数据
            code: 业务状态码
            message: 响应消息
            
        Returns:
            格式化的响应字典
        """
        return {
            "success": True,
            "code": code.code,
            "message": message or code.message,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }

    @staticmethod
    def fail(code: ResponseCode, message: str | None = None, data: Any = None) -> dict:
        """
        创建失败响应
        
        Args:
            code: 业务状态码
            message: 响应消息
            data: 响应数据
            
        Returns:
            格式化的响应字典
        """
        return {
            "success": False,
            "code": code.code,
            "message": message or code.message,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }


def success_response(data: Any = None, code: ResponseCode = ResponseCode.SUCCESS, message: str | None = None) -> dict:
    """
    创建成功响应的便捷函数
    
    Args:
        data: 响应数据
        code: 业务状态码
        message: 响应消息
        
    Returns:
        格式化的响应字典
    """
    return ResponseWrapper.success(data, code, message)


def error_response(code: ResponseCode, message: str | None = None, data: Any = None) -> dict:
    """
    创建错误响应的便捷函数
    
    Args:
        code: 业务状态码
        message: 响应消息
        data: 响应数据
        
    Returns:
        格式化的响应字典
    """
    return ResponseWrapper.fail(code, message, data)
