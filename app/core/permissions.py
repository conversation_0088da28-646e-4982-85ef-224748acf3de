"""
集中化权限常量定义

该文件将所有权限定义为易于访问的常量，以避免在代码中使用“魔法字符串”，
从而提高可读性、可维护性和代码提示的友好性。

设计原则:
1.  **类命名**: 使用 `Permissions` 作为主类名。
2.  **常量命名**: 采用 `RESOURCE_ACTION_SCOPE` 的格式，例如 `ARTICLE_UPDATE_OWN`。
3.  **实例化**: 每个常量都是一个 `Permission` 对象实例。
"""

from app.core.permission_system import Action, Permission, ResourceType, Scope


class Permissions:
    """集中管理所有权限常量"""

    # ==================== 用户 (User) ====================
    USER_READ_ALL = Permission(resource=ResourceType.USER, action=Action.READ, scope=Scope.ALL)
    USER_READ_OWN = Permission(resource=ResourceType.USER, action=Action.READ, scope=Scope.OWN)
    USER_CREATE_ALL = Permission(resource=ResourceType.USER, action=Action.CREATE, scope=Scope.ALL)
    USER_UPDATE_OWN = Permission(resource=ResourceType.USER, action=Action.UPDATE, scope=Scope.OWN)
    USER_UPDATE_ALL = Permission(resource=ResourceType.USER, action=Action.UPDATE, scope=Scope.ALL)
    USER_DELETE_ALL = Permission(resource=ResourceType.USER, action=Action.DELETE, scope=Scope.ALL)
    USER_FOLLOW_ALL = Permission(resource=ResourceType.USER, action=Action.FOLLOW, scope=Scope.ALL)
    USER_MANAGE_ALL = Permission(resource=ResourceType.USER, action=Action.MANAGE, scope=Scope.ALL)

    # ==================== 角色 (Role) ====================
    ROLE_READ_ALL = Permission(resource=ResourceType.ROLE, action=Action.READ, scope=Scope.ALL)
    ROLE_CREATE_ALL = Permission(resource=ResourceType.ROLE, action=Action.CREATE, scope=Scope.ALL)
    ROLE_UPDATE_ALL = Permission(resource=ResourceType.ROLE, action=Action.UPDATE, scope=Scope.ALL)
    ROLE_DELETE_ALL = Permission(resource=ResourceType.ROLE, action=Action.DELETE, scope=Scope.ALL)
    ROLE_MANAGE_ALL = Permission(resource=ResourceType.ROLE, action=Action.MANAGE, scope=Scope.ALL)

    # ==================== 权限 (Permission) ====================
    PERMISSION_READ_ALL = Permission(
        resource=ResourceType.PERMISSION, action=Action.READ, scope=Scope.ALL
    )
    PERMISSION_MANAGE_ALL = Permission(
        resource=ResourceType.PERMISSION, action=Action.MANAGE, scope=Scope.ALL
    )

    # ==================== 文章 (Article) ====================
    ARTICLE_READ_PUBLIC = Permission(
        resource=ResourceType.ARTICLE, action=Action.READ, scope=Scope.PUBLIC
    )
    ARTICLE_READ_ALL = Permission(
        resource=ResourceType.ARTICLE, action=Action.READ, scope=Scope.ALL
    )
    ARTICLE_READ_OWN = Permission(
        resource=ResourceType.ARTICLE, action=Action.READ, scope=Scope.OWN
    )
    ARTICLE_CREATE_OWN = Permission(
        resource=ResourceType.ARTICLE, action=Action.CREATE, scope=Scope.OWN
    )
    ARTICLE_UPDATE_OWN = Permission(
        resource=ResourceType.ARTICLE, action=Action.UPDATE, scope=Scope.OWN
    )
    ARTICLE_UPDATE_ALL = Permission(
        resource=ResourceType.ARTICLE, action=Action.UPDATE, scope=Scope.ALL
    )
    ARTICLE_PUBLISH_OWN = Permission(
        resource=ResourceType.ARTICLE, action=Action.PUBLISH, scope=Scope.OWN
    )
    ARTICLE_PUBLISH_ALL = Permission(
        resource=ResourceType.ARTICLE, action=Action.PUBLISH, scope=Scope.ALL
    )
    ARTICLE_APPROVE_ALL = Permission(
        resource=ResourceType.ARTICLE, action=Action.APPROVE, scope=Scope.ALL
    )
    ARTICLE_DELETE_OWN = Permission(
        resource=ResourceType.ARTICLE, action=Action.DELETE, scope=Scope.OWN
    )
    ARTICLE_DELETE_ALL = Permission(
        resource=ResourceType.ARTICLE, action=Action.DELETE, scope=Scope.ALL
    )
    ARTICLE_MANAGE_ALL = Permission(
        resource=ResourceType.ARTICLE, action=Action.MANAGE, scope=Scope.ALL
    )

    # ==================== 评论 (Comment) ====================
    COMMENT_READ_PUBLIC = Permission(
        resource=ResourceType.COMMENT, action=Action.READ, scope=Scope.PUBLIC
    )
    COMMENT_READ_ALL = Permission(
        resource=ResourceType.COMMENT, action=Action.READ, scope=Scope.ALL
    )
    COMMENT_READ_OWN = Permission(
        resource=ResourceType.COMMENT, action=Action.READ, scope=Scope.OWN
    )
    COMMENT_CREATE_OWN = Permission(
        resource=ResourceType.COMMENT, action=Action.CREATE, scope=Scope.OWN
    )
    COMMENT_UPDATE_OWN = Permission(
        resource=ResourceType.COMMENT, action=Action.UPDATE, scope=Scope.OWN
    )
    COMMENT_UPDATE_ALL = Permission(
        resource=ResourceType.COMMENT, action=Action.UPDATE, scope=Scope.ALL
    )
    COMMENT_DELETE_OWN = Permission(
        resource=ResourceType.COMMENT, action=Action.DELETE, scope=Scope.OWN
    )
    COMMENT_DELETE_ALL = Permission(
        resource=ResourceType.COMMENT, action=Action.DELETE, scope=Scope.ALL
    )
    COMMENT_MANAGE_ALL = Permission(
        resource=ResourceType.COMMENT, action=Action.MANAGE, scope=Scope.ALL
    )

    # ==================== 视频 (Video) ====================
    VIDEO_READ_PUBLIC = Permission(
        resource=ResourceType.VIDEO, action=Action.READ, scope=Scope.PUBLIC
    )
    VIDEO_READ_ALL = Permission(resource=ResourceType.VIDEO, action=Action.READ, scope=Scope.ALL)
    VIDEO_READ_OWN = Permission(resource=ResourceType.VIDEO, action=Action.READ, scope=Scope.OWN)
    VIDEO_CREATE_OWN = Permission(
        resource=ResourceType.VIDEO, action=Action.CREATE, scope=Scope.OWN
    )
    VIDEO_UPDATE_OWN = Permission(
        resource=ResourceType.VIDEO, action=Action.UPDATE, scope=Scope.OWN
    )
    VIDEO_UPDATE_ALL = Permission(
        resource=ResourceType.VIDEO, action=Action.UPDATE, scope=Scope.ALL
    )
    VIDEO_PUBLISH_OWN = Permission(
        resource=ResourceType.VIDEO, action=Action.PUBLISH, scope=Scope.OWN
    )
    VIDEO_PUBLISH_ALL = Permission(
        resource=ResourceType.VIDEO, action=Action.PUBLISH, scope=Scope.ALL
    )
    VIDEO_APPROVE_ALL = Permission(
        resource=ResourceType.VIDEO, action=Action.APPROVE, scope=Scope.ALL
    )
    VIDEO_DELETE_OWN = Permission(
        resource=ResourceType.VIDEO, action=Action.DELETE, scope=Scope.OWN
    )
    VIDEO_DELETE_ALL = Permission(
        resource=ResourceType.VIDEO, action=Action.DELETE, scope=Scope.ALL
    )
    VIDEO_MANAGE_ALL = Permission(
        resource=ResourceType.VIDEO, action=Action.MANAGE, scope=Scope.ALL
    )

    # ==================== Scratch项目 (Scratch Project) ====================
    SCRATCH_PROJECT_READ_PUBLIC = Permission(
        resource=ResourceType.SCRATCH_PROJECT, action=Action.READ, scope=Scope.PUBLIC
    )
    SCRATCH_PROJECT_READ_ALL = Permission(
        resource=ResourceType.SCRATCH_PROJECT, action=Action.READ, scope=Scope.ALL
    )
    SCRATCH_PROJECT_READ_OWN = Permission(
        resource=ResourceType.SCRATCH_PROJECT, action=Action.READ, scope=Scope.OWN
    )
    SCRATCH_PROJECT_CREATE_OWN = Permission(
        resource=ResourceType.SCRATCH_PROJECT, action=Action.CREATE, scope=Scope.OWN
    )
    SCRATCH_PROJECT_UPDATE_OWN = Permission(
        resource=ResourceType.SCRATCH_PROJECT, action=Action.UPDATE, scope=Scope.OWN
    )
    SCRATCH_PROJECT_UPDATE_ALL = Permission(
        resource=ResourceType.SCRATCH_PROJECT, action=Action.UPDATE, scope=Scope.ALL
    )
    SCRATCH_PROJECT_DELETE_OWN = Permission(
        resource=ResourceType.SCRATCH_PROJECT, action=Action.DELETE, scope=Scope.OWN
    )
    SCRATCH_PROJECT_DELETE_ALL = Permission(
        resource=ResourceType.SCRATCH_PROJECT, action=Action.DELETE, scope=Scope.ALL
    )
    SCRATCH_PROJECT_MANAGE_ALL = Permission(
        resource=ResourceType.SCRATCH_PROJECT, action=Action.MANAGE, scope=Scope.ALL
    )

    # 通用内容权限（用于简化API中的权限检查）
    CONTENT_READ_PUBLIC = Permission(
        resource=ResourceType.ARTICLE, action=Action.READ, scope=Scope.PUBLIC
    )
    CONTENT_READ_OWN = Permission(
        resource=ResourceType.ARTICLE, action=Action.READ, scope=Scope.OWN
    )
    CONTENT_CREATE_OWN = Permission(
        resource=ResourceType.ARTICLE, action=Action.CREATE, scope=Scope.OWN
    )
    CONTENT_UPDATE_OWN = Permission(
        resource=ResourceType.ARTICLE, action=Action.UPDATE, scope=Scope.OWN
    )
    CONTENT_DELETE_OWN = Permission(
        resource=ResourceType.ARTICLE, action=Action.DELETE, scope=Scope.OWN
    )
