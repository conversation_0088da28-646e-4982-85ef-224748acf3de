import json
import sys

from loguru import logger

# 移除默认的处理器，以便完全自定义
logger.remove()

# 添加一个处理器，将日志以JSON格式输出到标准错误
logger.add(
    sys.stderr,
    level="DEBUG",
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    serialize=False,  # Change to False for readable output
    enqueue=False,  # Change to False for immediate output
    backtrace=True,
    diagnose=True,
)


# 示例：如何添加文件日志记录器（可以根据需要取消注释）
# log_file_path = "logs/app.log"
# logger.add(
#     log_file_path,
#     level="INFO",
#     format="{message}",
#     serialize=True,
#     enqueue=True,
#     rotation="10 MB",  # 每10MB轮换一个新文件
#     retention="7 days",  # 保留7天的日志
#     compression="zip",  # 压缩旧日志文件
# )


def formatter(record: dict) -> str:
    """
    自定义格式化器，确保日志消息是JSON字符串
    """
    # 确保 'extra' 字段中的内容被正确处理
    record["extra"] = record.get("extra", {})

    # 创建一个包含核心日志信息的字典
    log_object = {
        "timestamp": record["time"].isoformat(),
        "level": record["level"].name,
        "message": record["message"],
        "extra": record["extra"],
        "context": {
            "file": record["file"].name,
            "line": record["line"],
            "function": record["function"],
            "name": record["name"],
        },
    }
    # 如果有异常信息，也添加到日志中
    if record["exception"]:
        exception_info = record["exception"]
        if hasattr(exception_info, "type"):
            log_object["exception"] = {
                "type": str(exception_info.type),
                "value": str(exception_info.value),
                "traceback": bool(exception_info.traceback),
            }
        else:
            # Handle cases where exception structure is different
            log_object["exception"] = {
                "type": str(type(exception_info).__name__),
                "value": str(exception_info),
                "traceback": True,
            }

    return json.dumps(log_object)


# 导出一个配置好的logger实例
__all__ = ["logger"]
