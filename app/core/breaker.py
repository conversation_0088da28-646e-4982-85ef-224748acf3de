"""
应用熔断器模块
基于 pybreaker 实现
"""

import pybreaker

from app.core.logging import logger


# --- 熔断器监听器 ---
class BreakerStateListener(pybreaker.CircuitBreakerListener):
    """监听熔断器状态变化并记录日志"""

    def before_state_change(self, breaker, old_state, new_state):
        """在状态改变前记录日志"""
        # 此处可以根据需要实现，例如通知监控系统
        pass

    def state_changed(self, breaker, old_state, new_state):
        """当状态改变时记录日志"""
        logger.warning(
            f"熔断器 '{breaker.name}' 状态改变: 从 '{old_state.name}' 变为 '{new_state.name}'"
        )

    def failure(self, breaker, exc):
        """当调用失败时记录"""
        # 避免过多的日志，只在关键时刻记录
        if breaker.current_state == "closed" and breaker.fail_counter + 1 >= breaker.fail_max:
            logger.warning(
                f"熔断器 '{breaker.name}' 即将打开，失败次数: {breaker.fail_counter + 1}"
            )

    def success(self, breaker):
        """当调用成功时记录"""
        # 通常不需要记录成功的日志，除非在半开状态下
        if breaker.current_state == "half-open":
            logger.info(f"熔断器 '{breaker.name}' 调用成功，将关闭熔断器")


# 创建一个共享的监听器实例
breaker_listener = BreakerStateListener()


# --- 数据库熔断器 ---
# 如果在 60 秒内失败次数超过 5 次，则打开熔断器
# 熔断器打开后，将在 30 秒后进入半开状态，尝试允许一次请求通过
db_breaker = pybreaker.CircuitBreaker(
    fail_max=5, reset_timeout=30, name="Database", listeners=[breaker_listener]
)

# --- Redis 熔断器 ---
# Redis 的配置可以更敏感一些，例如更快的重置时间
redis_breaker = pybreaker.CircuitBreaker(
    fail_max=5, reset_timeout=20, name="Redis", listeners=[breaker_listener]
)


class CircuitBreakerBase:
    """熔断器上下文管理器和装饰器的基类"""

    def __init__(self, breaker: pybreaker.CircuitBreaker):
        self.breaker = breaker

    async def __aenter__(self):
        if self.breaker.current_state == "open":
            raise pybreaker.CircuitBreakerError(f"{self.breaker.name} 熔断器已打开")
        return self

    async def __aexit__(self, exc_type, exc_val, traceback):
        # issubclass的第一个参数不能是None
        if exc_type and issubclass(exc_type, Exception):
            self.breaker.fail()

    def __call__(self, func):
        """作为装饰器使用"""

        async def wrapper(*args, **kwargs):
            try:
                async with self:
                    return await func(*args, **kwargs)
            except pybreaker.CircuitBreakerError as e:
                # 重新引发，由上层处理
                raise e

        return wrapper


class DatabaseCircuitBreaker(CircuitBreakerBase):
    """数据库调用的熔断器"""

    def __init__(self):
        super().__init__(db_breaker)


class RedisCircuitBreaker(CircuitBreakerBase):
    """Redis 调用的熔断器"""

    def __init__(self):
        super().__init__(redis_breaker)


# 创建实例以供使用
db_circuit_breaker = DatabaseCircuitBreaker()
redis_circuit_breaker = RedisCircuitBreaker()
