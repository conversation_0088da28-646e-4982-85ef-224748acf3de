"""游标分页实现

使用 fastapi-pagination 库实现游标分页，提供更好的性能和用户体验。
游标分页特别适合大数据集和实时数据更新的场景。
"""

from typing import Any, TypeVar

from fastapi_pagination import add_pagination, set_page
from fastapi_pagination.cursor import CursorPage, CursorParams
from fastapi_pagination.ext.sqlalchemy import paginate
from pydantic import BaseModel, Field
from sqlalchemy import asc, desc, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm.attributes import InstrumentedAttribute
from sqlalchemy.schema import Column

from app.core.logging import logger

T = TypeVar("T")


class CursorPaginationParams(BaseModel):
    """游标分页参数"""

    cursor: str | None = Field(None, description="游标位置，用于获取下一页数据")
    size: int = Field(20, ge=1, le=100, description="每页大小，范围1-100")
    order_by: str = Field("id", description="排序字段")
    order_direction: str = Field("desc", pattern="^(asc|desc)$", description="排序方向：asc或desc")


class CursorPaginationResponse[T](BaseModel):
    """游标分页响应"""

    items: list[T] = Field(..., description="数据列表")
    has_next: bool = Field(..., description="是否有下一页")
    has_previous: bool = Field(..., description="是否有上一页")
    next_cursor: str | None = Field(None, description="下一页游标")
    previous_cursor: str | None = Field(None, description="上一页游标")
    total_count: int | None = Field(None, description="总数量（可选，计算成本较高）")


class CursorPaginator:
    """游标分页器"""

    @staticmethod
    async def paginate_query(
        db: AsyncSession,
        query: Any,
        params: CursorPaginationParams,
        cursor_field: Any = "id",
        include_total: bool = False,
    ) -> CursorPaginationResponse:
        """对查询进行游标分页 (升级版)

        Args:
            db: 数据库会话
            query: SQLAlchemy查询对象
            params: 分页参数
            cursor_field: 用作游标的字段名(str)或字段对象(Column)。
                          支持跨表字段，例如 models.Like.created_at
            include_total: 是否包含总数量（会增加查询成本）

        Returns:
            分页响应
        """
        # 确定排序方向
        order_func = desc if params.order_direction == "desc" else asc

        # 获取模型类（假设查询的是单个模型）
        # 使用 "entity" 而不是 "type" 来正确获取 ORM 模型类
        model_class = query.column_descriptions[0]["entity"]

        # 检查查询是否返回标量值（如ID列表）
        is_scalar_query = len(query.column_descriptions) == 1 and isinstance(
            query.column_descriptions[0]["expr"], Column
        )

        # --- 游标字段处理升级 ---
        cursor_attr_name = None
        if isinstance(cursor_field, InstrumentedAttribute):
            cursor_column = cursor_field
            # 如果该属性属于当前 ORM 模型，则记录其属性名用于从对象中取值
            try:
                if getattr(cursor_field, "class_", None) in (model_class,):
                    cursor_attr_name = cursor_field.key
            except Exception:
                cursor_attr_name = cursor_field.key
        elif isinstance(cursor_field, Column):
            cursor_column = cursor_field
            # Column 可能也带有 key
            cursor_attr_name = getattr(cursor_field, "key", None)
        elif is_scalar_query:
            cursor_column = query.column_descriptions[0]["expr"]
        elif isinstance(cursor_field, str):
            cursor_column = getattr(model_class, cursor_field)
            cursor_attr_name = cursor_field
        else:
            # 兜底：尝试直接使用传入对象作为列
            cursor_column = cursor_field

        # 应用排序
        ordered_query = query.order_by(order_func(cursor_column))

        # 应用游标过滤
        # 注意：cursor="0" 或 cursor=0 应该被视为第一页请求，不应用过滤条件
        if params.cursor and params.cursor != "0":
            try:
                cursor_value = int(params.cursor)
                # 确保 cursor_value 有效（大于0）
                if cursor_value > 0:
                    if params.order_direction == "desc":
                        ordered_query = ordered_query.where(cursor_column < cursor_value)
                    else:
                        ordered_query = ordered_query.where(cursor_column > cursor_value)
            except ValueError:
                # 如果游标不是有效的整数，忽略游标过滤
                pass

        # 获取比请求数量多1个的记录，用于判断是否有下一页
        limit_query = ordered_query.limit(params.size + 1)

        # 执行查询
        result = await db.execute(limit_query)
        items = result.scalars().all()

        # 判断是否有下一页
        has_next = len(items) > params.size
        if has_next:
            items = items[:-1]  # 移除多获取的那一个记录

        # 计算游标
        next_cursor = None
        previous_cursor = None

        if items:
            # 检查返回的是ORM对象列表还是标量列表
            is_object_list = hasattr(items[0], "__dict__")

            if has_next:
                last_item = items[-1]
                if is_object_list:
                    value = None
                    if cursor_attr_name and hasattr(last_item, cursor_attr_name):
                        value = getattr(last_item, cursor_attr_name)
                    elif hasattr(last_item, "id"):
                        value = last_item.id
                    if value is not None:
                        next_cursor = str(value)
                else:
                    next_cursor = str(last_item)  # 直接使用标量值

            # 对于上一页游标，我们需要反向查询
            if params.cursor:
                previous_cursor = await CursorPaginator._get_previous_cursor(
                    db, query, params, cursor_field, items[0]
                )

        # 计算总数量（可选）
        total_count = None
        if include_total:
            # 正确处理不同类型的查询进行计数
            try:
                expr = query.column_descriptions[0]["expr"]

                # 检查是否是模型类查询
                if hasattr(expr, "__table__"):
                    # 如果是模型类，使用主键进行计数
                    count_expr = expr.id
                elif hasattr(expr, "type"):
                    # 如果是列表达式，直接使用
                    count_expr = expr
                else:
                    # 备用方案：使用通用的 * 计数
                    count_expr = func.count()

                # 构建计数查询
                if hasattr(expr, "__table__"):
                    count_query = query.with_only_columns(func.count(count_expr)).order_by(None)
                else:
                    count_query = query.with_only_columns(func.count()).order_by(None)

                count_result = await db.execute(count_query)
                total_count = count_result.scalar()

            except Exception as count_error:
                # 如果计数失败，记录错误但不影响主查询
                logger.warning(f"计数查询失败，跳过总数计算: {count_error}")
                total_count = None

        return CursorPaginationResponse(
            items=items,
            has_next=has_next,
            has_previous=bool(params.cursor),
            next_cursor=next_cursor,
            previous_cursor=previous_cursor,
            total_count=total_count,
        )

    @staticmethod
    async def _get_previous_cursor(
        db: AsyncSession,
        base_query: Any,
        params: CursorPaginationParams,
        cursor_field: Any,
        first_item: Any,
    ) -> str | None:
        """获取上一页游标"""
        # 使用 "entity" 而不是 "type" 来正确获取 ORM 模型类
        model_class = base_query.column_descriptions[0]["entity"]

        # 检查查询是否返回标量值
        is_scalar_query = len(base_query.column_descriptions) == 1 and isinstance(
            base_query.column_descriptions[0]["expr"], Column
        )

        # 解析游标列与属性名
        attr_name = None
        if is_scalar_query:
            cursor_column = base_query.column_descriptions[0]["expr"]
        elif isinstance(cursor_field, InstrumentedAttribute):
            cursor_column = cursor_field
            attr_name = cursor_field.key
        elif isinstance(cursor_field, Column):
            cursor_column = cursor_field
            attr_name = getattr(cursor_field, "key", None)
        elif isinstance(cursor_field, str):
            cursor_column = getattr(model_class, cursor_field)
            attr_name = cursor_field
        else:
            cursor_column = cursor_field

        # 检查 first_item 是 ORM 对象还是标量
        if hasattr(first_item, "__dict__"):
            if attr_name and hasattr(first_item, attr_name):
                first_item_cursor = getattr(first_item, attr_name)
            elif hasattr(first_item, "id"):
                first_item_cursor = first_item.id
            else:
                first_item_cursor = None
        else:
            first_item_cursor = first_item

        if first_item_cursor is None:
            return None

        # 反向查询以获取上一页的最后一个元素
        reverse_order_func = asc if params.order_direction == "desc" else desc

        if params.order_direction == "desc":
            reverse_query = base_query.where(cursor_column > first_item_cursor)
        else:
            reverse_query = base_query.where(cursor_column < first_item_cursor)

        reverse_query = reverse_query.order_by(reverse_order_func(cursor_column)).limit(params.size)

        result = await db.execute(reverse_query)
        reverse_items = result.scalars().all()

        if reverse_items:
            last_item_of_previous_page = reverse_items[-1]
            # 同样，检查返回的是对象还是标量
            if hasattr(last_item_of_previous_page, "__dict__"):
                value = None
                if attr_name and hasattr(last_item_of_previous_page, attr_name):
                    value = getattr(last_item_of_previous_page, attr_name)
                elif hasattr(last_item_of_previous_page, "id"):
                    value = last_item_of_previous_page.id
                return str(value) if value is not None else None
            else:
                return str(last_item_of_previous_page)

        return None


class FastAPIPaginationIntegration:
    """FastAPI Pagination 库集成"""

    @staticmethod
    def setup_pagination(app):
        """设置 FastAPI Pagination"""
        add_pagination(app)
        # 设置默认的游标分页
        set_page(CursorPage[Any])

    @staticmethod
    async def paginate_sqlalchemy_query(
        db: AsyncSession, query: Any, params: CursorParams | None = None
    ) -> CursorPage:
        """使用 fastapi-pagination 对 SQLAlchemy 查询进行游标分页"""
        return await paginate(db, query, params)


# 兼容性适配器，用于现有代码的平滑迁移
class PaginationAdapter:
    """分页适配器，提供从传统分页到游标分页的迁移支持"""

    @staticmethod
    def convert_offset_to_cursor(
        offset: int, limit: int, total: int, items: list[Any], cursor_field: str = "id"
    ) -> CursorPaginationResponse:
        """将传统的 offset/limit 分页结果转换为游标分页格式"""
        has_next = (offset + limit) < total
        has_previous = offset > 0

        next_cursor = None
        previous_cursor = None

        if items:
            if has_next:
                next_cursor = str(getattr(items[-1], cursor_field))
            if has_previous and len(items) > 0:
                previous_cursor = str(getattr(items[0], cursor_field))

        return CursorPaginationResponse(
            items=items,
            has_next=has_next,
            has_previous=has_previous,
            next_cursor=next_cursor,
            previous_cursor=previous_cursor,
            total_count=total,
        )

    @staticmethod
    def cursor_params_to_offset(
        cursor: str | None, size: int, model_class: Any, cursor_field: str = "id"
    ) -> tuple[int, int]:
        """将游标参数转换为 offset/limit 参数（用于向后兼容）"""
        if not cursor:
            return 0, size

        try:
            int(cursor)
            # 这里需要根据具体业务逻辑计算 offset
            # 简化实现，实际使用中可能需要更复杂的逻辑
            offset = 0  # 游标分页通常不需要 offset
            return offset, size
        except ValueError:
            return 0, size
