"""认证错误码和消息常量定义"""


class AuthErrorCodes:
    """认证错误码常量类"""
    
    # 用户相关错误 (AUTH_USER_xxx)
    USER_NOT_FOUND = "AUTH_USER_001"
    USER_DISABLED = "AUTH_USER_002"
    INVALID_CREDENTIALS = "AUTH_USER_003"
    USER_ALREADY_EXISTS = "AUTH_USER_004"
    USER_CREATION_FAILED = "AUTH_USER_005"
    USER_UPDATE_FAILED = "AUTH_USER_006"
    INVALID_USER_DATA = "AUTH_USER_007"
    USER_PERMISSION_DENIED = "AUTH_USER_008"
    
    # 设备相关错误 (AUTH_DEVICE_xxx)
    DEVICE_NOT_TRUSTED = "AUTH_DEVICE_001"
    DEVICE_VERIFICATION_REQUIRED = "AUTH_DEVICE_002"
    INVALID_DEVICE_TOKEN = "AUTH_DEVICE_003"
    DEVICE_BLOCKED = "AUTH_DEVICE_004"
    DEVICE_NOT_FOUND = "AUTH_DEVICE_005"
    DEVICE_FINGERPRINT_INVALID = "AUTH_DEVICE_006"
    DEVICE_TRUST_EVALUATION_FAILED = "AUTH_DEVICE_007"
    DEVICE_VERIFICATION_EXPIRED = "AUTH_DEVICE_008"
    DEVICE_VERIFICATION_FAILED = "AUTH_DEVICE_009"
    DEVICE_VERIFICATION_TOKEN_GENERATION_FAILED = "AUTH_DEVICE_010"
    
    # 短信验证相关错误 (AUTH_SMS_xxx)
    INVALID_VERIFICATION_CODE = "AUTH_SMS_001"
    VERIFICATION_CODE_EXPIRED = "AUTH_SMS_002"
    TOO_MANY_ATTEMPTS = "AUTH_SMS_003"
    SMS_SEND_FAILED = "AUTH_SMS_004"
    INVALID_PHONE_NUMBER = "AUTH_SMS_005"
    SMS_TEMPLATE_NOT_FOUND = "AUTH_SMS_006"
    SMS_RATE_LIMIT_EXCEEDED = "AUTH_SMS_007"
    SMS_SERVICE_UNAVAILABLE = "AUTH_SMS_008"
    
    # 微信相关错误 (AUTH_WECHAT_xxx)
    WECHAT_API_ERROR = "AUTH_WECHAT_001"
    INVALID_OPENID = "AUTH_WECHAT_002"
    QR_CODE_EXPIRED = "AUTH_WECHAT_003"
    WECHAT_USER_NOT_FOUND = "AUTH_WECHAT_004"
    WECHAT_BIND_FAILED = "AUTH_WECHAT_005"
    WECHAT_CALLBACK_INVALID = "AUTH_WECHAT_006"
    WECHAT_SCAN_TIMEOUT = "AUTH_WECHAT_007"
    WECHAT_SERVICE_UNAVAILABLE = "AUTH_WECHAT_008"
    INVALID_SCENE_STR = "AUTH_WECHAT_009"
    
    # 令牌相关错误 (AUTH_TOKEN_xxx)
    INVALID_TOKEN = "AUTH_TOKEN_001"
    TOKEN_EXPIRED = "AUTH_TOKEN_002"
    TOKEN_GENERATION_FAILED = "AUTH_TOKEN_003"
    TOKEN_REVOKED = "AUTH_TOKEN_004"
    TOKEN_MALFORMED = "AUTH_TOKEN_005"
    TOKEN_SIGNATURE_INVALID = "AUTH_TOKEN_006"
    REFRESH_TOKEN_INVALID = "AUTH_TOKEN_007"
    
    # 认证流程相关错误 (AUTH_FLOW_xxx)
    AUTHENTICATION_FAILED = "AUTH_FLOW_001"
    INVALID_AUTH_METHOD = "AUTH_FLOW_002"
    AUTH_FLOW_INTERRUPTED = "AUTH_FLOW_003"
    MULTI_STEP_AUTH_REQUIRED = "AUTH_FLOW_004"
    AUTH_STATE_INVALID = "AUTH_FLOW_005"
    AUTH_TIMEOUT = "AUTH_FLOW_006"
    UNSUPPORTED_AUTH_TYPE = "AUTH_FLOW_007"
    AUTHENTICATION_INITIATION_FAILED = "AUTH_FLOW_008"
    AUTHENTICATION_COMPLETION_FAILED = "AUTH_FLOW_009"
    MISSING_REQUIRED_FIELDS = "AUTH_FLOW_010"
    UNSUPPORTED_OPERATION = "AUTH_FLOW_011"
    STATUS_CHECK_FAILED = "AUTH_FLOW_012"
    SMS_INITIATION_FAILED = "AUTH_FLOW_013"
    SMS_COMPLETION_FAILED = "AUTH_FLOW_014"
    WECHAT_INITIATION_FAILED = "AUTH_FLOW_015"
    WECHAT_COMPLETION_FAILED = "AUTH_FLOW_016"
    
    # 权限相关错误 (AUTH_PERM_xxx)
    INSUFFICIENT_PERMISSIONS = "AUTH_PERM_001"
    ROLE_NOT_FOUND = "AUTH_PERM_002"
    PERMISSION_DENIED = "AUTH_PERM_003"
    ROLE_ASSIGNMENT_FAILED = "AUTH_PERM_004"
    
    # 系统相关错误 (AUTH_SYS_xxx)
    INTERNAL_ERROR = "AUTH_SYS_001"
    SERVICE_UNAVAILABLE = "AUTH_SYS_002"
    DATABASE_ERROR = "AUTH_SYS_003"
    CACHE_ERROR = "AUTH_SYS_004"
    CONFIGURATION_ERROR = "AUTH_SYS_005"
    EXTERNAL_SERVICE_ERROR = "AUTH_SYS_006"
    
    # 频率限制错误 (AUTH_RATE_xxx)
    RATE_LIMIT_EXCEEDED = "AUTH_RATE_001"
    TOO_MANY_LOGIN_ATTEMPTS = "AUTH_RATE_002"
    TOO_MANY_SMS_REQUESTS = "AUTH_RATE_003"
    TOO_MANY_DEVICE_VERIFICATIONS = "AUTH_RATE_004"


class AuthErrorMessages:
    """认证错误消息常量类"""
    
    # 用户相关错误消息
    USER_MESSAGES = {
        AuthErrorCodes.USER_NOT_FOUND: "用户不存在",
        AuthErrorCodes.USER_DISABLED: "用户账户已被禁用",
        AuthErrorCodes.INVALID_CREDENTIALS: "用户名或密码错误",
        AuthErrorCodes.USER_ALREADY_EXISTS: "用户已存在",
        AuthErrorCodes.USER_CREATION_FAILED: "用户创建失败",
        AuthErrorCodes.USER_UPDATE_FAILED: "用户信息更新失败",
        AuthErrorCodes.INVALID_USER_DATA: "用户数据格式无效",
        AuthErrorCodes.USER_PERMISSION_DENIED: "用户权限不足",
    }
    
    # 设备相关错误消息
    DEVICE_MESSAGES = {
        AuthErrorCodes.DEVICE_NOT_TRUSTED: "设备未受信任",
        AuthErrorCodes.DEVICE_VERIFICATION_REQUIRED: "需要设备验证",
        AuthErrorCodes.INVALID_DEVICE_TOKEN: "设备验证令牌无效",
        AuthErrorCodes.DEVICE_BLOCKED: "设备已被阻止",
        AuthErrorCodes.DEVICE_NOT_FOUND: "设备不存在",
        AuthErrorCodes.DEVICE_FINGERPRINT_INVALID: "设备指纹无效",
        AuthErrorCodes.DEVICE_TRUST_EVALUATION_FAILED: "设备信任评估失败",
        AuthErrorCodes.DEVICE_VERIFICATION_EXPIRED: "设备验证已过期",
        AuthErrorCodes.DEVICE_VERIFICATION_FAILED: "设备验证失败",
        AuthErrorCodes.DEVICE_VERIFICATION_TOKEN_GENERATION_FAILED: "设备验证令牌生成失败",
    }
    
    # 短信验证相关错误消息
    SMS_MESSAGES = {
        AuthErrorCodes.INVALID_VERIFICATION_CODE: "验证码错误",
        AuthErrorCodes.VERIFICATION_CODE_EXPIRED: "验证码已过期",
        AuthErrorCodes.TOO_MANY_ATTEMPTS: "验证码尝试次数过多",
        AuthErrorCodes.SMS_SEND_FAILED: "短信发送失败",
        AuthErrorCodes.INVALID_PHONE_NUMBER: "手机号格式无效",
        AuthErrorCodes.SMS_TEMPLATE_NOT_FOUND: "短信模板不存在",
        AuthErrorCodes.SMS_RATE_LIMIT_EXCEEDED: "短信发送频率过高",
        AuthErrorCodes.SMS_SERVICE_UNAVAILABLE: "短信服务不可用",
    }
    
    # 微信相关错误消息
    WECHAT_MESSAGES = {
        AuthErrorCodes.WECHAT_API_ERROR: "微信API调用失败",
        AuthErrorCodes.INVALID_OPENID: "微信OpenID无效",
        AuthErrorCodes.QR_CODE_EXPIRED: "二维码已过期",
        AuthErrorCodes.WECHAT_USER_NOT_FOUND: "微信用户不存在",
        AuthErrorCodes.WECHAT_BIND_FAILED: "微信绑定失败",
        AuthErrorCodes.WECHAT_CALLBACK_INVALID: "微信回调数据无效",
        AuthErrorCodes.WECHAT_SCAN_TIMEOUT: "微信扫码超时",
        AuthErrorCodes.WECHAT_SERVICE_UNAVAILABLE: "微信服务不可用",
        AuthErrorCodes.INVALID_SCENE_STR: "场景字符串无效",
    }
    
    # 令牌相关错误消息
    TOKEN_MESSAGES = {
        AuthErrorCodes.INVALID_TOKEN: "令牌无效",
        AuthErrorCodes.TOKEN_EXPIRED: "令牌已过期",
        AuthErrorCodes.TOKEN_GENERATION_FAILED: "令牌生成失败",
        AuthErrorCodes.TOKEN_REVOKED: "令牌已被撤销",
        AuthErrorCodes.TOKEN_MALFORMED: "令牌格式错误",
        AuthErrorCodes.TOKEN_SIGNATURE_INVALID: "令牌签名无效",
        AuthErrorCodes.REFRESH_TOKEN_INVALID: "刷新令牌无效",
    }
    
    # 认证流程相关错误消息
    FLOW_MESSAGES = {
        AuthErrorCodes.AUTHENTICATION_FAILED: "认证失败",
        AuthErrorCodes.INVALID_AUTH_METHOD: "认证方式无效",
        AuthErrorCodes.AUTH_FLOW_INTERRUPTED: "认证流程中断",
        AuthErrorCodes.MULTI_STEP_AUTH_REQUIRED: "需要多步认证",
        AuthErrorCodes.AUTH_STATE_INVALID: "认证状态无效",
        AuthErrorCodes.AUTH_TIMEOUT: "认证超时",
        AuthErrorCodes.UNSUPPORTED_AUTH_TYPE: "不支持的认证类型",
        AuthErrorCodes.AUTHENTICATION_INITIATION_FAILED: "认证发起失败",
        AuthErrorCodes.AUTHENTICATION_COMPLETION_FAILED: "认证完成失败",
        AuthErrorCodes.MISSING_REQUIRED_FIELDS: "缺少必需字段",
        AuthErrorCodes.UNSUPPORTED_OPERATION: "不支持的操作",
        AuthErrorCodes.STATUS_CHECK_FAILED: "状态检查失败",
        AuthErrorCodes.SMS_INITIATION_FAILED: "短信认证发起失败",
        AuthErrorCodes.SMS_COMPLETION_FAILED: "短信认证完成失败",
        AuthErrorCodes.WECHAT_INITIATION_FAILED: "微信认证发起失败",
        AuthErrorCodes.WECHAT_COMPLETION_FAILED: "微信认证完成失败",
    }
    
    # 权限相关错误消息
    PERMISSION_MESSAGES = {
        AuthErrorCodes.INSUFFICIENT_PERMISSIONS: "权限不足",
        AuthErrorCodes.ROLE_NOT_FOUND: "角色不存在",
        AuthErrorCodes.PERMISSION_DENIED: "权限被拒绝",
        AuthErrorCodes.ROLE_ASSIGNMENT_FAILED: "角色分配失败",
    }
    
    # 系统相关错误消息
    SYSTEM_MESSAGES = {
        AuthErrorCodes.INTERNAL_ERROR: "系统内部错误",
        AuthErrorCodes.SERVICE_UNAVAILABLE: "服务不可用",
        AuthErrorCodes.DATABASE_ERROR: "数据库错误",
        AuthErrorCodes.CACHE_ERROR: "缓存错误",
        AuthErrorCodes.CONFIGURATION_ERROR: "配置错误",
        AuthErrorCodes.EXTERNAL_SERVICE_ERROR: "外部服务错误",
    }
    
    # 频率限制错误消息
    RATE_LIMIT_MESSAGES = {
        AuthErrorCodes.RATE_LIMIT_EXCEEDED: "请求频率过高",
        AuthErrorCodes.TOO_MANY_LOGIN_ATTEMPTS: "登录尝试次数过多",
        AuthErrorCodes.TOO_MANY_SMS_REQUESTS: "短信请求次数过多",
        AuthErrorCodes.TOO_MANY_DEVICE_VERIFICATIONS: "设备验证次数过多",
    }
    
    # 合并所有消息
    ALL_MESSAGES = {
        **USER_MESSAGES,
        **DEVICE_MESSAGES,
        **SMS_MESSAGES,
        **WECHAT_MESSAGES,
        **TOKEN_MESSAGES,
        **FLOW_MESSAGES,
        **PERMISSION_MESSAGES,
        **SYSTEM_MESSAGES,
        **RATE_LIMIT_MESSAGES,
    }
    
    @classmethod
    def get_message(cls, error_code: str, default: str = "未知错误") -> str:
        """获取错误码对应的消息"""
        return cls.ALL_MESSAGES.get(error_code, default)