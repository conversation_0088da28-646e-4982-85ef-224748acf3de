"""
应用限流器模块
基于 slowapi 实现
"""

from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.errors import RateLimitExceeded
from slowapi.util import get_remote_address

# 初始化 Limiter
# get_remote_address 是默认的 key function，它将基于客户端IP地址进行限流
limiter = Limiter(key_func=get_remote_address)


def setup_limiter(app):
    """将限流器应用到FastAPI实例"""
    app.state.limiter = limiter
    app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)
