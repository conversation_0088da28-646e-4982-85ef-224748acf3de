"""
权限同步服务

负责将权限常量与数据库权限记录保持同步，确保权限定义的一致性。
"""

import inspect
from typing import Dict, List, Set, Any
from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import logger
from app.core.permissions import Permissions
from app.core.permission_system import Permission as PermissionObject
from app.models.user import Permission, UserRole


class PermissionSyncService:
    """权限同步服务"""
    
    def __init__(self):
        self.permission_constants = self._extract_permission_constants()
    
    def _extract_permission_constants(self) -> Dict[str, PermissionObject]:
        """提取权限常量"""
        constants = {}
        
        for attr_name in dir(Permissions):
            if not attr_name.startswith('_'):
                attr_value = getattr(Permissions, attr_name)
                if isinstance(attr_value, PermissionObject):
                    constants[attr_name] = attr_value
        
        return constants
    
    async def sync_permissions_to_db(self, db: AsyncSession) -> Dict[str, Any]:
        """将权限常量同步到数据库"""
        logger.info("开始同步权限常量到数据库...")
        
        result = {
            "added": [],
            "updated": [],
            "removed": [],
            "errors": []
        }
        
        try:
            # 获取数据库中现有的权限
            existing_permissions = await self._get_existing_permissions(db)
            existing_codes = {p.code for p in existing_permissions}
            
            # 获取常量中定义的权限代码
            constant_codes = set()
            
            # 添加或更新权限
            for const_name, perm_obj in self.permission_constants.items():
                perm_code = str(perm_obj)
                constant_codes.add(perm_code)
                
                existing_perm = next(
                    (p for p in existing_permissions if p.code == perm_code), 
                    None
                )
                
                if existing_perm:
                    # 更新现有权限
                    updated = await self._update_permission(
                        db, existing_perm, perm_obj, const_name
                    )
                    if updated:
                        result["updated"].append(perm_code)
                else:
                    # 添加新权限
                    await self._create_permission(db, perm_obj, const_name)
                    result["added"].append(perm_code)
            
            # 标记不再使用的权限（可选择删除或禁用）
            obsolete_codes = existing_codes - constant_codes
            for obsolete_code in obsolete_codes:
                obsolete_perm = next(
                    (p for p in existing_permissions if p.code == obsolete_code),
                    None
                )
                if obsolete_perm and obsolete_perm.is_active:
                    # 禁用而不是删除，保持数据完整性
                    obsolete_perm.is_active = False
                    result["removed"].append(obsolete_code)
            
            await db.commit()
            logger.info(f"权限同步完成: 添加 {len(result['added'])}, 更新 {len(result['updated'])}, 禁用 {len(result['removed'])}")
            
        except Exception as e:
            await db.rollback()
            error_msg = f"权限同步失败: {str(e)}"
            logger.error(error_msg)
            result["errors"].append(error_msg)
        
        return result
    
    async def _get_existing_permissions(self, db: AsyncSession) -> List[Permission]:
        """获取数据库中现有的权限"""
        result = await db.execute(select(Permission))
        return result.scalars().all()
    
    async def _create_permission(
        self, 
        db: AsyncSession, 
        perm_obj: PermissionObject, 
        const_name: str
    ) -> None:
        """创建新权限"""
        permission = Permission(
            name=self._generate_permission_name(const_name),
            code=str(perm_obj),
            resource=perm_obj.resource.value,
            action=perm_obj.action.value,
            description=perm_obj.description or self._generate_description(perm_obj),
            is_active=True
        )
        db.add(permission)
    
    async def _update_permission(
        self,
        db: AsyncSession,
        existing_perm: Permission,
        perm_obj: PermissionObject,
        const_name: str
    ) -> bool:
        """更新现有权限"""
        updated = False
        
        # 检查是否需要更新
        new_name = self._generate_permission_name(const_name)
        new_description = perm_obj.description or self._generate_description(perm_obj)
        
        if existing_perm.name != new_name:
            existing_perm.name = new_name
            updated = True
        
        if existing_perm.resource != perm_obj.resource.value:
            existing_perm.resource = perm_obj.resource.value
            updated = True
        
        if existing_perm.action != perm_obj.action.value:
            existing_perm.action = perm_obj.action.value
            updated = True
        
        if existing_perm.description != new_description:
            existing_perm.description = new_description
            updated = True
        
        if not existing_perm.is_active:
            existing_perm.is_active = True
            updated = True
        
        return updated
    
    def _generate_permission_name(self, const_name: str) -> str:
        """根据常量名生成权限名称"""
        # 将 ARTICLE_READ_PUBLIC 转换为 "查看公开文章"
        parts = const_name.split('_')
        
        resource_map = {
            'ARTICLE': '文章',
            'VIDEO': '视频',
            'USER': '用户',
            'COMMENT': '评论',
            'ROLE': '角色',
            'PERMISSION': '权限',
            'SCRATCH': 'Scratch项目',
            'PROJECT': '项目'
        }
        
        action_map = {
            'READ': '查看',
            'CREATE': '创建',
            'UPDATE': '更新',
            'DELETE': '删除',
            'MANAGE': '管理',
            'PUBLISH': '发布',
            'APPROVE': '审核',
            'FOLLOW': '关注'
        }
        
        scope_map = {
            'PUBLIC': '公开',
            'OWN': '个人',
            'ALL': '所有'
        }
        
        if len(parts) >= 3:
            resource = resource_map.get(parts[0], parts[0])
            action = action_map.get(parts[1], parts[1])
            scope = scope_map.get(parts[2], parts[2])
            
            return f"{action}{scope}{resource}"
        
        return const_name.replace('_', ' ').title()
    
    def _generate_description(self, perm_obj: PermissionObject) -> str:
        """生成权限描述"""
        resource_desc = {
            'user': '用户',
            'article': '文章',
            'video': '视频',
            'comment': '评论',
            'role': '角色',
            'permission': '权限'
        }.get(perm_obj.resource.value, perm_obj.resource.value)
        
        action_desc = {
            'read': '查看',
            'create': '创建',
            'update': '更新',
            'delete': '删除',
            'manage': '管理'
        }.get(perm_obj.action.value, perm_obj.action.value)
        
        scope_desc = {
            'public': '公开的',
            'own': '自己的',
            'all': '所有'
        }.get(perm_obj.scope.value, perm_obj.scope.value)
        
        return f"允许{action_desc}{scope_desc}{resource_desc}"
    
    async def validate_permission_consistency(self, db: AsyncSession) -> List[str]:
        """验证权限常量与数据库的一致性"""
        issues = []
        
        try:
            existing_permissions = await self._get_existing_permissions(db)
            existing_codes = {p.code for p in existing_permissions}
            constant_codes = {str(perm) for perm in self.permission_constants.values()}
            
            # 检查缺失的权限
            missing_in_db = constant_codes - existing_codes
            if missing_in_db:
                issues.append(f"数据库中缺失的权限: {', '.join(missing_in_db)}")
            
            # 检查多余的权限
            extra_in_db = existing_codes - constant_codes
            if extra_in_db:
                issues.append(f"数据库中多余的权限: {', '.join(extra_in_db)}")
            
            # 检查禁用的权限
            disabled_perms = [p.code for p in existing_permissions if not p.is_active]
            if disabled_perms:
                issues.append(f"已禁用的权限: {', '.join(disabled_perms)}")
            
        except Exception as e:
            issues.append(f"验证过程中出错: {str(e)}")
        
        return issues
    
    async def generate_permission_report(self, db: AsyncSession) -> Dict[str, Any]:
        """生成权限使用报告"""
        report = {
            "total_constants": len(self.permission_constants),
            "constants_by_resource": {},
            "constants_by_action": {},
            "database_permissions": 0,
            "active_permissions": 0,
            "role_permission_usage": {},
            "consistency_issues": []
        }
        
        try:
            # 统计常量
            for perm_obj in self.permission_constants.values():
                resource = perm_obj.resource.value
                action = perm_obj.action.value
                
                report["constants_by_resource"][resource] = \
                    report["constants_by_resource"].get(resource, 0) + 1
                report["constants_by_action"][action] = \
                    report["constants_by_action"].get(action, 0) + 1
            
            # 统计数据库权限
            existing_permissions = await self._get_existing_permissions(db)
            report["database_permissions"] = len(existing_permissions)
            report["active_permissions"] = len([p for p in existing_permissions if p.is_active])
            
            # 统计角色权限使用
            result = await db.execute(select(UserRole))
            roles = result.scalars().all()
            
            for role in roles:
                await db.refresh(role, ["permissions"])
                report["role_permission_usage"][role.name] = len(role.permissions)
            
            # 检查一致性
            report["consistency_issues"] = await self.validate_permission_consistency(db)
            
        except Exception as e:
            report["error"] = str(e)
        
        return report
    
    async def cleanup_unused_permissions(self, db: AsyncSession) -> int:
        """清理未使用的权限（谨慎使用）"""
        logger.warning("开始清理未使用的权限...")
        
        try:
            # 查找没有被任何角色使用的权限
            result = await db.execute(
                select(Permission)
                .outerjoin(Permission.roles)
                .where(UserRole.id.is_(None))
                .where(Permission.is_active == False)
            )
            unused_permissions = result.scalars().all()
            
            if unused_permissions:
                for perm in unused_permissions:
                    await db.delete(perm)
                
                await db.commit()
                logger.info(f"清理了 {len(unused_permissions)} 个未使用的权限")
                return len(unused_permissions)
            
        except Exception as e:
            await db.rollback()
            logger.error(f"清理权限失败: {str(e)}")
            raise
        
        return 0
