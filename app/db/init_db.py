import logging

from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import Base, engine

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


from app.db.init_permissions import init_permissions


async def init_db(db: AsyncSession) -> None:
    """初始化数据库"""
    # 创建数据库表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    logger.info("数据库表已创建")

    # 初始化权限
    await init_permissions(db)
