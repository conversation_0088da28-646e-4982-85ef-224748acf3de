"""Redis连接模块"""

import logging
from typing import Any

from tenacity import (
    after_log,
    before_log,
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

import redis.asyncio as redis
from app.config import settings
from redis import exceptions
from redis.asyncio import Redis
from redis.asyncio.cluster import ClusterNode, RedisCluster
from redis.asyncio.sentinel import Sentinel

logger = logging.getLogger(__name__)

# Redis 客户端全局变量，区分主从
redis_master_client: Redis | RedisCluster = None
redis_slave_client: Redis | RedisCluster = None


async def init_redis():
    """初始化所有Redis连接"""
    await get_redis_master()
    await get_redis_slave()


async def close_redis():
    """关闭所有Redis连接"""
    global redis_master_client, redis_slave_client
    if redis_master_client:
        await redis_master_client.close()
        redis_master_client = None
    if redis_slave_client:
        await redis_slave_client.close()
        redis_slave_client = None
    logger.info("所有Redis连接已关闭")


async def get_redis_master() -> Redis | RedisCluster:
    """
    获取Redis主节点客户端实例，用于写操作。
    支持 standalone, sentinel, 和 cluster 模式。
    在 standalone 和 cluster 模式下，返回唯一的客户端实例。
    """
    global redis_master_client
    if redis_master_client is not None:
        return redis_master_client

    mode = settings.REDIS_MODE.lower()
    logger.info(f"正在以 {mode} 模式初始化Redis MASTER连接...")

    # 为了避免在slave连接失败时重复初始化master，将master的初始化逻辑提取出来
    # 并确保在slave回退时能复用已有的master连接
    if redis_slave_client is not None and mode not in ["sentinel"]:
        logger.info("复用现有连接作为Master连接...")
        redis_master_client = redis_slave_client
        return redis_master_client

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=1, min=2, max=20),
        retry=retry_if_exception_type(
            (exceptions.ConnectionError, exceptions.TimeoutError, exceptions.RedisClusterException)
        ),
        before=before_log(logger, logging.WARNING),
        after=after_log(logger, logging.INFO),
        reraise=True,
    )
    async def _connect():
        global redis_master_client
        if mode == "sentinel":
            sentinels = [tuple(s.split(":")) for s in settings.REDIS_SENTINELS.split(",")]
            sentinel_client = Sentinel(
                sentinels,
                socket_connect_timeout=settings.REDIS_SOCKET_CONNECT_TIMEOUT,
                socket_timeout=settings.REDIS_SOCKET_TIMEOUT,
                decode_responses=True,
            )
            redis_master_client = sentinel_client.master_for(
                settings.REDIS_SENTINEL_MASTER_NAME,
                max_connections=settings.REDIS_MAX_CONNECTIONS,
            )
            logger.info(
                f"Redis Sentinel模式 MASTER 连接成功，主节点: {settings.REDIS_SENTINEL_MASTER_NAME}"
            )

        elif mode == "cluster":
            raw_url = settings.REDIS_URL.replace("redis://", "")
            node_addresses = raw_url.split(",")
            startup_nodes = [
                ClusterNode(host=host, port=int(port))
                for host, port in (addr.split(":") for addr in node_addresses)
            ]
            if not startup_nodes:
                raise ValueError("在REDIS_URL中没有找到有效的集群节点")

            redis_master_client = RedisCluster(
                startup_nodes=startup_nodes,
                decode_responses=True,
                socket_connect_timeout=settings.REDIS_SOCKET_CONNECT_TIMEOUT,
                socket_timeout=settings.REDIS_SOCKET_TIMEOUT,
                max_connections=settings.REDIS_MAX_CONNECTIONS,
            )
            logger.info("Redis Cluster模式 MASTER 连接成功")

        else:  # standalone
            redis_master_client = redis.from_url(
                settings.REDIS_URL,
                decode_responses=True,
                max_connections=settings.REDIS_MAX_CONNECTIONS,
                socket_connect_timeout=settings.REDIS_SOCKET_CONNECT_TIMEOUT,
                socket_timeout=settings.REDIS_SOCKET_TIMEOUT,
                health_check_interval=settings.REDIS_HEALTH_CHECK_INTERVAL,
            )
            logger.info("Redis Standalone模式 MASTER 连接成功")

        await redis_master_client.ping()
        logger.info("Redis MASTER PING 测试成功")
        return redis_master_client

    try:
        return await _connect()
    except Exception as e:
        logger.error(f"Redis MASTER ({mode}模式) 连接失败: {e}")
        redis_master_client = None
        raise


async def get_redis_slave() -> Redis | RedisCluster:
    """
    获取Redis从节点客户端实例，用于读操作。
    在 Sentinel 模式下，连接到从节点。
    在 Standalone 或 Cluster 模式下，优雅地回退到主节点连接。
    """
    global redis_slave_client
    if redis_slave_client is not None:
        return redis_slave_client

    mode = settings.REDIS_MODE.lower()
    if mode != "sentinel":
        logger.info(f"Redis ({mode}模式) 不支持读写分离，SLAVE 连接将回退到 MASTER。")
        # 在非sentinel模式下，slave和master是同一个客户端
        redis_slave_client = await get_redis_master()
        return redis_slave_client

    logger.info(f"正在以 {mode} 模式初始化Redis SLAVE连接...")

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=1, min=2, max=20),
        retry=retry_if_exception_type((exceptions.ConnectionError, exceptions.TimeoutError)),
        before=before_log(logger, logging.WARNING),
        after=after_log(logger, logging.INFO),
        reraise=True,
    )
    async def _connect():
        global redis_slave_client
        sentinels = [tuple(s.split(":")) for s in settings.REDIS_SENTINELS.split(",")]
        sentinel_client = Sentinel(
            sentinels,
            socket_connect_timeout=settings.REDIS_SOCKET_CONNECT_TIMEOUT,
            socket_timeout=settings.REDIS_SOCKET_TIMEOUT,
            decode_responses=True,
        )
        redis_slave_client = sentinel_client.slave_for(
            settings.REDIS_SENTINEL_MASTER_NAME,
            max_connections=settings.REDIS_MAX_CONNECTIONS,
        )
        logger.info(
            f"Redis Sentinel模式 SLAVE 连接成功，主节点: {settings.REDIS_SENTINEL_MASTER_NAME}"
        )
        await redis_slave_client.ping()
        logger.info("Redis SLAVE PING 测试成功")
        return redis_slave_client

    try:
        return await _connect()
    except Exception as e:
        logger.error(f"Redis SLAVE ({mode}模式) 连接失败，将回退到 MASTER: {e}")
        redis_slave_client = None  # 清除失败的连接
        return await get_redis_master()


async def set_key(key: str, value: Any, expire: int = 0) -> bool:
    """
    设置Redis键值（写操作，使用master）。
    遇到连接或超时等可重试异常时，会直接抛出，由上层熔断器处理。
    """
    try:
        redis_conn = await get_redis_master()
        await redis_conn.set(key, value, ex=expire if expire > 0 else None)
        return True
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"设置Redis键 '{key}' 失败，连接或超时错误: {e}")
        raise  # 重新抛出，让熔断器捕获
    except Exception as e:
        logger.error(f"设置Redis键 '{key}' 时发生未知错误: {e}")
        return False  # 对于非连接类问题，可以选择不触发熔断


async def get_key(key: str) -> Any:
    """
    获取Redis键值（读操作，使用slave）。
    遇到连接或超时等可重试异常时，会直接抛出，由上层熔断器和降级逻辑处理。
    """
    try:
        redis_conn = await get_redis_slave()
        return await redis_conn.get(key)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"获取Redis键 '{key}' 失败，连接或超时错误: {e}")
        raise  # 重新抛出，让熔断器和降级逻辑捕获
    except Exception as e:
        logger.error(f"获取Redis键 '{key}' 时发生未知错误: {e}")
        return None  # 对于非连接类问题，可以选择不触发熔断


async def delete_key(key: str) -> bool:
    """
    删除Redis键（写操作，使用master）。
    遇到连接或超时等可重试异常时，会直接抛出，由上层熔断器处理。
    """
    try:
        redis_conn = await get_redis_master()
        result = await redis_conn.delete(key)
        return result > 0
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"删除Redis键 '{key}' 失败，连接或超时错误: {e}")
        raise  # 重新抛出，让熔断器捕获
    except Exception as e:
        logger.error(f"删除Redis键 '{key}' 时发生未知错误: {e}")
        return False  # 对于非连接类问题，可以选择不触发熔断


async def key_exists(key: str) -> bool:
    """
    检查Redis键是否存在（读操作，使用slave）。
    """
    try:
        redis_conn = await get_redis_slave()
        return await redis_conn.exists(key) > 0
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"检查Redis键 '{key}' 是否存在失败，连接或超时错误: {e}")
        raise
    except Exception as e:
        logger.error(f"检查Redis键 '{key}' 是否存在时发生未知错误: {e}")
        return False


async def get_key_ttl(key: str) -> int:
    """
    获取Redis键的TTL（读操作，使用slave）。
    """
    try:
        redis_conn = await get_redis_slave()
        return await redis_conn.ttl(key)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"获取Redis键 '{key}' TTL失败，连接或超时错误: {e}")
        raise
    except Exception as e:
        logger.error(f"获取Redis键 '{key}' TTL时发生未知错误: {e}")
        return -2  # Corresponds to redis-py's error reporting for TTL


async def increment_key(key: str, expire: int) -> int:
    """
    原子性地增加键的值（写操作，使用master），并设置过期时间。
    """
    try:
        redis_conn = await get_redis_master()
        # 使用pipeline确保原子性
        async with redis_conn.pipeline(transaction=True) as pipe:
            pipe.incr(key)
            pipe.expire(key, expire)
            result = await pipe.execute()
        return result[0]
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"增加Redis键 '{key}' 失败，连接或超时错误: {e}")
        raise
    except Exception as e:
        logger.error(f"增加Redis键 '{key}' 时发生未知错误: {e}")
        return 0


async def hash_set(key: str, field: str, value: Any) -> int:
    """
    设置哈希表中的字段值（写操作，使用master）。
    """
    try:
        redis_conn = await get_redis_master()
        return await redis_conn.hset(key, field, value)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"设置Redis哈希字段 '{key}:{field}' 失败: {e}")
        raise
    except Exception as e:
        logger.error(f"设置Redis哈希字段 '{key}:{field}' 时发生未知错误: {e}")
        return 0


async def hash_get(key: str, field: str) -> Any:
    """
    获取哈希表中的字段值（读操作，使用slave）。
    """
    try:
        redis_conn = await get_redis_slave()
        return await redis_conn.hget(key, field)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"获取Redis哈希字段 '{key}:{field}' 失败: {e}")
        raise
    except Exception as e:
        logger.error(f"获取Redis哈希字段 '{key}:{field}' 时发生未知错误: {e}")
        return None


async def hash_get_many(key: str, fields: list[str]) -> list[Any]:
    """
    从哈希表中获取多个字段的值（读操作，使用slave）。
    """
    try:
        redis_conn = await get_redis_slave()
        return await redis_conn.hmget(key, fields)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"从Redis哈希 '{key}' 获取多个字段失败: {e}")
        raise
    except Exception as e:
        logger.error(f"从Redis哈希 '{key}' 获取多个字段时发生未知错误: {e}")
        return [None] * len(fields)


async def hash_set_with_expire(key: str, field: str, value: Any, expire: int) -> None:
    """
    原子性地设置哈希字段并为整个哈希键设置过期时间（写操作，使用master）。
    """
    try:
        redis_conn = await get_redis_master()
        async with redis_conn.pipeline(transaction=True) as pipe:
            pipe.hset(key, field, value)
            pipe.expire(key, expire)
            await pipe.execute()
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"原子性设置并过期Redis哈希字段 '{key}:{field}' 失败: {e}")
        raise
    except Exception as e:
        logger.error(f"原子性设置并过期Redis哈希字段 '{key}:{field}' 时发生未知错误: {e}")


async def sorted_set_add(key: str, mapping: dict[str, float]) -> int:
    """
    向有序集合添加一个或多个成员（写操作，使用master）。
    """
    try:
        redis_conn = await get_redis_master()
        return await redis_conn.zadd(key, mapping)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"向Redis有序集合 '{key}' 添加成员失败: {e}")
        raise
    except Exception as e:
        logger.error(f"向Redis有序集合 '{key}' 添加成员时发生未知错误: {e}")
        return 0


async def sorted_set_increment_by(key: str, member: str, amount: float) -> float:
    """
    为有序集合中指定成员的分数加上增量（写操作，使用master）。
    """
    try:
        redis_conn = await get_redis_master()
        return await redis_conn.zincrby(key, amount, member)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"增加Redis有序集合成员 '{key}:{member}' 分数失败: {e}")
        raise
    except Exception as e:
        logger.error(f"增加Redis有序集合成员 '{key}:{member}' 分数时发生未知错误: {e}")
        return 0.0


async def sorted_set_get_score(key: str, member: str) -> float | None:
    """
    获取有序集合中指定成员的分数（读操作，使用slave）。
    """
    try:
        redis_conn = await get_redis_slave()
        return await redis_conn.zscore(key, member)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"获取Redis有序集合成员 '{key}:{member}' 分数失败: {e}")
        raise
    except Exception as e:
        logger.error(f"获取Redis有序集合成员 '{key}:{member}' 分数时发生未知错误: {e}")
        return None


async def sorted_set_get_range(
    key: str, start: int, end: int, with_scores: bool = False, desc: bool = True
) -> list:
    """
    通过索引区间返回有序集合的成员（读操作，使用slave）。
    """
    try:
        redis_conn = await get_redis_slave()
        return await redis_conn.zrange(key, start, end, desc=desc, withscores=with_scores)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"获取Redis有序集合 '{key}' 范围失败: {e}")
        raise
    except Exception as e:
        logger.error(f"获取Redis有序集合 '{key}' 范围时发生未知错误: {e}")
        return []


async def set_add(key: str, *values: Any) -> int:
    """
    向集合添加一个或多个成员（写操作，使用master）。
    """
    try:
        redis_conn = await get_redis_master()
        return await redis_conn.sadd(key, *values)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"向Redis集合 '{key}' 添加成员失败: {e}")
        raise
    except Exception as e:
        logger.error(f"向Redis集合 '{key}' 添加成员时发生未知错误: {e}")
        return 0


async def set_remove(key: str, *values: Any) -> int:
    """
    从集合中移除一个或多个成员（写操作，使用master）。
    """
    try:
        redis_conn = await get_redis_master()
        return await redis_conn.srem(key, *values)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"从Redis集合 '{key}' 移除成员失败: {e}")
        raise
    except Exception as e:
        logger.error(f"从Redis集合 '{key}' 移除成员时发生未知错误: {e}")
        return 0


async def set_is_member(key: str, value: Any) -> bool:
    """
    判断成员元素是否是集合的成员（读操作，使用slave）。
    """
    try:
        redis_conn = await get_redis_slave()
        return await redis_conn.sismember(key, value)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"检查Redis集合成员 '{key}' 失败: {e}")
        raise
    except Exception as e:
        logger.error(f"检查Redis集合成员 '{key}' 时发生未知错误: {e}")
        return False


async def set_cardinality(key: str) -> int:
    """
    获取集合的成员数（读操作，使用slave）。
    """
    try:
        redis_conn = await get_redis_slave()
        return await redis_conn.scard(key)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"获取Redis集合 '{key}' 成员数失败: {e}")
        raise
    except Exception as e:
        logger.error(f"获取Redis集合 '{key}' 成员数时发生未知错误: {e}")
        return 0


async def execute_pipeline(commands: list[tuple[str, str, Any]], write: bool) -> list[Any]:
    """
    执行一个pipeline事务（根据write参数选择master/slave）。
    """
    redis_conn = await get_redis_master() if write else await get_redis_slave()
    try:
        async with redis_conn.pipeline(transaction=True) as pipe:
            for command, key, *args in commands:
                getattr(pipe, command)(key, *args)
            return await pipe.execute()
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"执行Redis pipeline失败: {e}")
        raise
    except Exception as e:
        logger.error(f"执行Redis pipeline时发生未知错误: {e}")
        return [None] * len(commands)


async def increment_key_by(key: str, amount: int, expire: int | None = None) -> int:
    """
    将 key 中储存的数字值增一,并可选择设置过期时间（写操作，使用master）。
    """
    try:
        redis_conn = await get_redis_master()
        if expire:
            async with redis_conn.pipeline(transaction=True) as pipe:
                pipe.incrby(key, amount)
                pipe.expire(key, expire)
                result = await pipe.execute()
            return result[0]
        else:
            return await redis_conn.incrby(key, amount)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"增加Redis键 '{key}' by {amount} 失败: {e}")
        raise
    except Exception as e:
        logger.error(f"增加Redis键 '{key}' by {amount} 时发生未知错误: {e}")
        return 0


async def decrement_key(key: str, expire: int | None = None) -> int:
    """
    将 key 中储存的数字值减一,并可选择设置过期时间（写操作，使用master）。
    """
    try:
        redis_conn = await get_redis_master()
        if expire:
            async with redis_conn.pipeline(transaction=True) as pipe:
                pipe.decr(key)
                pipe.expire(key, expire)
                result = await pipe.execute()
            return result[0]
        else:
            return await redis_conn.decr(key)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"减少Redis键 '{key}' 失败: {e}")
        raise
    except Exception as e:
        logger.error(f"减少Redis键 '{key}' 时发生未知错误: {e}")
        return 0


async def set_get_all_members(key: str) -> set:
    """
    获取集合中的所有成员（读操作，使用slave）。
    """
    try:
        redis_conn = await get_redis_slave()
        return await redis_conn.smembers(key)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"获取Redis集合 '{key}' 所有成员失败: {e}")
        raise
    except Exception as e:
        logger.error(f"获取Redis集合 '{key}' 所有成员时发生未知错误: {e}")
        return set()


async def set_key_expire(key: str, expire: int) -> bool:
    """
    为一个键设置过期时间（写操作，使用master）。
    """
    try:
        redis_conn = await get_redis_master()
        return await redis_conn.expire(key, expire)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"为Redis键 '{key}' 设置过期时间失败: {e}")
        raise
    except Exception as e:
        logger.error(f"为Redis键 '{key}' 设置过期时间时发生未知错误: {e}")
        return False


async def sorted_set_remove_range_by_rank(key: str, start: int, stop: int) -> int:
    """
    移除有序集合中指定排名区间的所有成员（写操作，使用master）。
    """
    try:
        redis_conn = await get_redis_master()
        return await redis_conn.zremrangebyrank(key, start, stop)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"移除Redis有序集合 '{key}' 范围失败: {e}")
        raise
    except Exception as e:
        logger.error(f"移除Redis有序集合 '{key}' 范围时发生未知错误: {e}")
        return 0


async def scan_keys(pattern: str) -> list[str]:
    """
    安全地迭代匹配的键（读操作，使用slave）。
    """
    try:
        redis_conn = await get_redis_slave()
        cursor = "0"
        keys = []
        while cursor != 0:
            cursor, new_keys = await redis_conn.scan(cursor, match=pattern, count=1000)
            keys.extend(new_keys)
        return keys
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"扫描Redis键失败 (pattern: {pattern}): {e}")
        raise
    except Exception as e:
        logger.error(f"扫描Redis键时发生未知错误 (pattern: {pattern}): {e}")
        return []


async def get_and_delete_key(key: str) -> Any:
    """
    获取并删除一个键（写操作，使用master）。
    """
    try:
        redis_conn = await get_redis_master()
        return await redis_conn.getdel(key)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"获取并删除Redis键 '{key}' 失败: {e}")
        raise
    except Exception as e:
        logger.error(f"获取并删除Redis键 '{key}' 时发生未知错误: {e}")
        return None


async def bloom_filter_add(key: str, item: str) -> int:
    """
    向布隆过滤器添加一个元素（写操作，使用master）。
    """
    try:
        redis_conn = await get_redis_master()
        return await redis_conn.execute_command("BF.ADD", key, item)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"向布隆过滤器 '{key}' 添加元素失败: {e}")
        raise
    except Exception as e:
        logger.error(f"向布隆过滤器 '{key}' 添加元素时发生未知错误: {e}")
        return 0


async def bloom_filter_exists(key: str, item: str) -> int:
    """
    检查元素是否存在于布隆过滤器中（读操作，使用slave）。
    """
    try:
        redis_conn = await get_redis_slave()
        return await redis_conn.execute_command("BF.EXISTS", key, item)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"检查布隆过滤器 '{key}' 元素失败: {e}")
        raise
    except Exception as e:
        logger.error(f"检查布隆过滤器 '{key}' 元素时发生未知错误: {e}")
        return 0


async def bloom_filter_madd(key: str, *items: str) -> list[int]:
    """
    向布隆过滤器批量添加元素（写操作，使用master）。
    """
    try:
        redis_conn = await get_redis_master()
        return await redis_conn.execute_command("BF.MADD", key, *items)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"向布隆过滤器 '{key}' 批量添加元素失败: {e}")
        raise
    except Exception as e:
        logger.error(f"向布隆过滤器 '{key}' 批量添加元素时发生未知错误: {e}")
        return [0] * len(items)


async def bloom_filter_mexists(key: str, *items: str) -> list[int]:
    """
    批量检查元素是否存在于布隆过滤器中（读操作，使用slave）。
    """
    try:
        redis_conn = await get_redis_slave()
        return await redis_conn.execute_command("BF.MEXISTS", key, *items)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"批量检查布隆过滤器 '{key}' 元素失败: {e}")
        raise
    except Exception as e:
        logger.error(f"批量检查布隆过滤器 '{key}' 元素时发生未知错误: {e}")
        return [0] * len(items)


async def bloom_filter_reserve(key: str, error_rate: float, capacity: int) -> str:
    """
    如果布隆过滤器不存在，则创建并保留它（写操作，使用master）。
    """
    try:
        redis_conn = await get_redis_master()
        return await redis_conn.execute_command("BF.RESERVE", key, str(error_rate), str(capacity))
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"创建布隆过滤器 '{key}' 失败: {e}")
        raise
    except Exception as e:
        # "item exists" 错误是预期的并发场景，不应视为错误
        if "item exists" not in str(e).lower():
            logger.error(f"创建布隆过滤器 '{key}' 时发生未知错误: {e}")
        return "OK"  # 即使发生错误，也假装成功以避免阻塞


async def sorted_set_remove(key: str, *members: str) -> int:
    """
    从有序集合中移除一个或多个成员（写操作，使用master）。
    """
    try:
        redis_conn = await get_redis_master()
        return await redis_conn.zrem(key, *members)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"从Redis有序集合 '{key}' 移除成员失败: {e}")
        raise
    except Exception as e:
        logger.error(f"从Redis有序集合 '{key}' 移除成员时发生未知错误: {e}")
        return 0


async def sorted_set_union_store(dest: str, keys: list[str], aggregate: str = "SUM") -> int:
    """
    计算多个有序集合的并集，并将结果存储在新的有序集合中（写操作，使用master）。
    """
    try:
        redis_conn = await get_redis_master()
        return await redis_conn.zunionstore(dest, keys, aggregate=aggregate)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"计算Redis有序集合并集失败: {e}")
        raise
    except Exception as e:
        logger.error(f"计算Redis有序集合并集时发生未知错误: {e}")
        return 0


async def sorted_set_inter_store(dest: str, keys: list[str], aggregate: str = "SUM") -> int:
    """
    计算多个有序集合的交集，并将结果存储在新的有序集合中（写操作，使用master）。
    """
    try:
        redis_conn = await get_redis_master()
        return await redis_conn.zinterstore(dest, keys, aggregate=aggregate)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"计算Redis有序集合交集失败: {e}")
        raise
    except Exception as e:
        logger.error(f"计算Redis有序集合交集时发生未知错误: {e}")
        return 0


async def hash_get_all(key: str) -> dict:
    """
    获取哈希表中所有字段和值（读操作，使用slave）。
    """
    try:
        redis_conn = await get_redis_slave()
        return await redis_conn.hgetall(key)
    except (exceptions.ConnectionError, exceptions.TimeoutError) as e:
        logger.error(f"获取Redis哈希 '{key}' 所有字段失败: {e}")
        raise
    except Exception as e:
        logger.error(f"获取Redis哈希 '{key}' 所有字段时发生未知错误: {e}")
        return {}
