import contextlib
from typing import Annotated

from fastapi import Depends, HTTPException, Query, Request, status
from fastapi.security import OA<PERSON>2<PERSON><PERSON>wordBearer
from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.config import get_settings
from app.core.logging import logger
from app.db.session import get_db
from app.models.user import User, UserRole
from app.services.interfaces.token_service_interface import ITokenService
from app.services.service_factory import get_token_service

settings = get_settings()

# OAuth2密码流的令牌URL
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")

# 可选的OAuth2密码流（用于支持游客访问）
oauth2_scheme_optional = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/auth/login", auto_error=False
)


class TokenData(BaseModel):
    username: str | None = None
    permissions: list[str] = []


async def get_token_from_header_or_cookie(
    request: Request, token_from_header: str | None = Depends(oauth2_scheme_optional)
) -> str | None:
    """
    从 Authorization 头或 Cookie 中获取 token
    优先级: Authorization Header > Cookie
    """
    if token_from_header:
        return token_from_header

    token_from_cookie = request.cookies.get("access_token")
    return token_from_cookie


async def get_current_user(
    db: AsyncSession = Depends(get_db),
    token: str | None = Depends(get_token_from_header_or_cookie),
    token_service: ITokenService = Depends(get_token_service),
) -> User:
    """获取当前用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )

    if not token:
        raise credentials_exception

    # 使用 TokenService 验证 token
    token_info = await token_service.verify_token(token)
    if not token_info:
        raise credentials_exception

    username = token_info.get("sub")
    if not username:
        raise credentials_exception

    # 查询用户（预加载 role 和 role.permissions 关系）
    from sqlalchemy.orm import selectinload

    result = await db.execute(
        select(User)
        .options(selectinload(User.role).selectinload(UserRole.permissions))
        .where(User.username == username)
    )
    user = result.scalar_one_or_none()
    if user is None:
        raise credentials_exception
    if not user.is_active:
        raise HTTPException(status_code=400, detail="用户未激活")

    return user


async def get_current_active_user(
    current_user: Annotated[User, Depends(get_current_user)],
) -> User:
    """获取当前激活用户"""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="用户未激活")
    return current_user


async def get_current_active_superuser(
    current_user: Annotated[User, Depends(get_current_user)],
) -> User:
    """获取当前超级管理员用户"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="用户权限不足")
    return current_user


async def get_current_user_optional(
    db: AsyncSession = Depends(get_db),
    token: str | None = Depends(get_token_from_header_or_cookie),
    token_service: ITokenService = Depends(get_token_service),
) -> User | None:
    """
    获取当前用户（可选）
    用于支持游客访问的端点

    Args:
        db: 数据库会话
        token: 可选的访问令牌

    Returns:
        用户对象或None（游客）
    """
    if not token:
        return None

    # 验证 token，但失败时返回 None 而不是抛出异常
    token_info = await token_service.verify_token(token)
    if not token_info:
        return None

    username = token_info.get("sub")
    if not username:
        return None

    # 查询用户
    from sqlalchemy.orm import selectinload

    result = await db.execute(
        select(User)
        .options(selectinload(User.role).selectinload(UserRole.permissions))
        .where(User.username == username)
    )
    user = result.scalar_one_or_none()

    # 如果用户不存在或未激活，也视为游客
    if user is None or not user.is_active:
        return None

    return user


async def extract_token_from_websocket_protocols(websocket) -> str | None:
    """从WebSocket协议中提取token

    支持的协议格式：
    - Bearer.{token}
    """
    if hasattr(websocket, "headers") and "sec-websocket-protocol" in websocket.headers:
        protocol_header = websocket.headers["sec-websocket-protocol"]

        # 解析协议列表（可能有多个协议用逗号分隔）
        protocols = [p.strip() for p in protocol_header.split(",")]
        with contextlib.suppress(Exception):
            logger.debug(f"WS auth: Sec-WebSocket-Protocol present, candidates={len(protocols)}")

        for protocol in protocols:
            if protocol.startswith("Bearer"):
                token = protocol[7:]  # 移除 'Bearer.' 前缀
                with contextlib.suppress(Exception):
                    logger.debug(
                        f"WS auth: token obtained from subprotocol (len={len(token) if token else 0})"
                    )
                return token

    return None


async def get_token_from_websocket_protocols_or_query(
    websocket,
    token: str = Query(None, description="访问令牌（Query参数）"),
) -> str | None:
    """从 WebSocket 协议、Query 参数或 Cookie 中获取 token

    优先级: Sec-WebSocket-Protocol > Query 参数 > Cookie(access_token)
    """
    # 1) 首先尝试从协议中提取 token
    token_from_protocol = await extract_token_from_websocket_protocols(websocket)
    if token_from_protocol:
        return token_from_protocol

    # 2) 回退到 Query 参数
    if token:
        with contextlib.suppress(Exception):
            logger.debug(f"WS auth: token obtained from query (len={len(token) if token else 0})")
        return token

    # 3) 最后尝试从 Cookie 中读取 access_token
    try:
        if hasattr(websocket, "cookies") and isinstance(websocket.cookies, dict):
            cookie_token = websocket.cookies.get("access_token")
            if cookie_token:
                with contextlib.suppress(Exception):
                    logger.debug(
                        f"WS auth: token obtained from cookie (len={len(cookie_token) if cookie_token else 0})"
                    )
                return cookie_token
    except Exception:
        # 读取 cookie 不应影响后续逻辑
        pass

    return None


async def get_current_user_websocket(
    websocket,
    token: str | None = Depends(get_token_from_websocket_protocols_or_query),
    db: AsyncSession = Depends(get_db),
    token_service: ITokenService = Depends(get_token_service),
) -> User:
    """WebSocket 连接的用户认证

    支持三种认证方式（按优先级）：
    1. 通过 Sec-WebSocket-Protocol: protocols: ['Bearer.{token}']
    2. 通过 Query 参数: ?token={token}
    3. 通过 Cookie: access_token={token}

    兼容 token 中 `sub` 为用户名或用户ID 两种情况。
    """
    try:
        try:
            origin = websocket.headers.get("origin") if hasattr(websocket, "headers") else None
            proto = (
                websocket.headers.get("sec-websocket-protocol")
                if hasattr(websocket, "headers")
                else None
            )
            has_cookie = (
                hasattr(websocket, "cookies")
                and isinstance(websocket.cookies, dict)
                and "access_token" in websocket.cookies
            )
            logger.debug(
                "WS auth: begin, origin=%s, has_subprotocol=%s, has_cookie=%s",
                origin,
                bool(proto),
                bool(has_cookie),
            )
        except Exception:
            pass

        if not token:
            await websocket.close(code=1008, reason="No authentication token provided")
            try:
                logger.warning("WS auth: missing token from all sources")
            except Exception:
                pass
            raise HTTPException(status_code=401, detail="未提供认证令牌")

        # 验证token
        payload = await token_service.verify_token(token)
        if not payload:
            await websocket.close(code=1008, reason="Invalid token")
            try:
                logger.warning(
                    "WS auth: token verification failed (len=%s)", len(token) if token else 0
                )
            except Exception:
                pass
            raise HTTPException(status_code=401, detail="无效的令牌")

        subject = payload.get("sub") or payload.get("username")
        if subject is None:
            await websocket.close(code=1008, reason="Invalid token payload")
            try:
                logger.warning("WS auth: payload missing subject (sub/username)")
            except Exception:
                pass
            raise HTTPException(status_code=401, detail="令牌载荷无效")

        # 获取用户信息：优先按数字ID匹配，否则按用户名匹配
        try:
            if isinstance(subject, int) or (isinstance(subject, str) and subject.isdigit()):
                result = await db.execute(select(User).where(User.id == int(subject)))
            else:
                result = await db.execute(select(User).where(User.username == str(subject)))
        except Exception:
            # 回退为按用户名查询
            result = await db.execute(select(User).where(User.username == str(subject)))

        user = result.scalar_one_or_none()

        if user is None:
            await websocket.close(code=1008, reason="User not found")
            try:
                logger.warning("WS auth: user not found for subject=%s", subject)
            except Exception:
                pass
            raise HTTPException(status_code=404, detail="用户不存在")

        if not user.is_active:
            await websocket.close(code=1008, reason="User inactive")
            try:
                logger.warning("WS auth: user is inactive, user_id=%s", user.id)
            except Exception:
                pass
            raise HTTPException(status_code=400, detail="用户未激活")

        try:
            logger.info("WS auth: success user_id=%s username=%s", user.id, user.username)
        except Exception:
            pass
        return user

    except Exception as e:
        await websocket.close(code=1008, reason=str(e))
        try:
            logger.exception("WS auth: exception during handshake: %s", str(e))
        except Exception:
            pass
        raise
