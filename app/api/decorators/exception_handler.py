"""
异常处理装饰器
"""

import functools
import traceback
from collections.abc import Callable
from typing import Any

from fastapi import HTTPException

from app.core.logging import logger


def handle_exceptions(
    custom_handlers: dict[type[Exception], Callable] = None,
    log_errors: bool = True,
    default_error_message: str = "内部服务器错误",
):
    """
    统一异常处理装饰器

    Args:
        custom_handlers: 自定义异常处理器映射
        log_errors: 是否记录错误日志
        default_error_message: 默认错误消息
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            try:
                return await func(*args, **kwargs)
            except HTTPException:
                # HTTPException 直接向上传播，保持原有的状态码和消息
                raise
            except Exception as e:
                # 检查是否有自定义处理器
                if custom_handlers:
                    exception_type = type(e)
                    if exception_type in custom_handlers:
                        return await custom_handlers[exception_type](e, *args, **kwargs)

                    # 检查父类异常处理器
                    for exc_type, handler in custom_handlers.items():
                        if isinstance(e, exc_type):
                            return await handler(e, *args, **kwargs)

                # 默认处理
                if isinstance(e, ValueError):
                    if log_errors:
                        logger.warning(f"参数验证错误在 {func.__name__}: {str(e)}")
                    raise HTTPException(status_code=400, detail=str(e)) from e

                # 记录未处理的异常
                if log_errors:
                    logger.error(
                        f"未处理的异常在 {func.__name__}: {str(e)}\n{traceback.format_exc()}"
                    )

                raise HTTPException(status_code=500, detail=default_error_message) from e

        return wrapper

    return decorator


def handle_auth_exceptions(func: Callable) -> Callable:
    """认证相关异常处理装饰器"""

    async def phone_already_registered_handler(exc, *args, **kwargs):
        logger.info(f"手机号已注册异常: {str(exc)}")
        raise HTTPException(status_code=400, detail="该手机号已注册，请直接登录") from exc

    async def wechat_bind_exception_handler(exc, *args, **kwargs):
        logger.warning(f"微信绑定异常: {str(exc)}")
        raise HTTPException(status_code=400, detail=str(exc)) from exc

    # 导入异常类型（避免循环导入）
    try:
        from app.core.exceptions import PhoneAlreadyRegisteredError
        from app.core.wechat_exceptions import WeChatBindException

        custom_handlers = {
            PhoneAlreadyRegisteredError: phone_already_registered_handler,
            WeChatBindException: wechat_bind_exception_handler,
        }
    except ImportError:
        custom_handlers = {}

    return handle_exceptions(
        custom_handlers=custom_handlers, default_error_message="认证服务错误，请稍后重试"
    )(func)


def handle_sms_exceptions(func: Callable) -> Callable:
    """短信相关异常处理装饰器"""
    return handle_exceptions(default_error_message="短信服务错误，请稍后重试")(func)


def handle_wechat_exceptions(func: Callable) -> Callable:
    """微信相关异常处理装饰器"""

    async def wechat_api_error_handler(exc, *args, **kwargs):
        logger.error(f"微信API错误: {str(exc)}")
        raise HTTPException(status_code=503, detail="微信服务暂时不可用，请稍后重试") from exc

    try:
        from app.core.wechat_exceptions import WeChatAPIError

        custom_handlers = {
            WeChatAPIError: wechat_api_error_handler,
        }
    except ImportError:
        custom_handlers = {}

    return handle_exceptions(
        custom_handlers=custom_handlers, default_error_message="微信服务错误，请稍后重试"
    )(func)


# 使用示例装饰器
def api_exception_handler(func: Callable) -> Callable:
    """通用API异常处理装饰器"""
    return handle_exceptions(log_errors=True, default_error_message="服务暂时不可用，请稍后重试")(
        func
    )
