"""智能用户行为追踪中间件"""

import asyncio
import json
import re
from typing import Any

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from user_agents import parse

from app.core.logging import logger
from app.db.session import get_db
from app.services.service_factory import get_behavior_tracking_service, get_recommendation_service


class BehaviorTrackingMiddleware(BaseHTTPMiddleware):
    """
    智能用户行为追踪中间件

    功能：
    - 自动识别用户行为类型
    - 统一记录浏览历史和交互行为
    - 智能去重和批量处理
    - 异步更新用户画像和推荐缓存
    """

    def __init__(self, app):
        super().__init__(app)
        self.recommendation_service = get_recommendation_service()
        self.behavior_tracking_service = get_behavior_tracking_service()
        self.behavior_patterns = {
            # 内容浏览行为 (GET请求)
            "content_view": [
                (r"/api/v1/articles/(\d+)$", "article"),
                (r"/api/v1/videos/(\d+)$", "video"),
                (r"/api/v1/scratch/(\d+)$", "scratch"),  # 新增scratch项目浏览追踪
            ],
            # 交互行为 (POST/PUT请求)
            "like": [
                (r"/api/v1/likes/toggle$", None),  # 从请求体获取内容信息
            ],
            "favorite": [
                (r"/api/v1/favorites/toggle$", None),
            ],
            "comment": [
                (r"/api/v1/comments$", None),
            ],
            # 搜索行为
            "search": [
                (r"/api/v1/articles.*[?&]search=", "article"),
                (r"/api/v1/videos.*[?&]search=", "video"),
                (r"/api/v1/scratch.*[?&]search=", "scratch"),  # 新增scratch搜索追踪
            ],
            # 列表浏览
            "browse_list": [
                (r"/api/v1/articles$", "article"),
                (r"/api/v1/videos$", "video"),
                (r"/api/v1/scratch$", "scratch"),  # 新增scratch列表浏览追踪
            ],
        }

    async def dispatch(self, request: Request, call_next) -> Any:
        """处理请求并追踪用户行为"""

        # 先执行原始请求
        response = await call_next(request)

        # 只在成功响应时记录行为 (2xx状态码)
        if not (200 <= response.status_code < 300):
            return response

        # 获取用户信息
        user_info = await self._get_user_from_request(request)
        if not user_info:
            return response  # 未登录用户不记录行为

        # 识别行为类型
        behavior_info = await self._identify_behavior(request, response)
        if not behavior_info:
            return response  # 无法识别的行为

        # 异步记录行为（不阻塞响应）
        asyncio.create_task(self._record_behavior_async(user_info, behavior_info, request))

        return response

    async def _get_user_from_request(self, request: Request) -> dict | None:
        """从请求中获取用户信息"""
        try:
            # 从请求状态中获取用户信息（由认证中间件设置）
            user = getattr(request.state, "user", None)
            if user and hasattr(user, "id"):
                return {"user_id": user.id, "is_authenticated": True}
        except Exception as e:
            logger.debug(f"获取用户信息失败: {e}")

        return None

    async def _identify_behavior(self, request: Request, response) -> dict | None:
        """识别用户行为类型和相关信息"""
        path = request.url.path
        method = request.method
        query_params = dict(request.query_params)

        # 遍历行为模式进行匹配
        for behavior_type, patterns in self.behavior_patterns.items():
            for pattern, default_content_type in patterns:
                match = re.search(pattern, path)
                if match:
                    behavior_info = {
                        "behavior_type": behavior_type,
                        "method": method,
                        "path": path,
                        "query_params": query_params,
                    }

                    # 根据行为类型提取具体信息
                    if behavior_type == "content_view":
                        behavior_info.update(
                            {
                                "content_type": default_content_type,
                                "content_id": int(match.group(1)),
                                "interaction_type": "view",
                            }
                        )

                    elif behavior_type in ["like", "favorite", "comment"]:
                        # 从响应或请求体中获取内容信息
                        content_info = await self._extract_content_info_from_interaction(
                            request, response, behavior_type
                        )
                        if content_info:
                            behavior_info.update(content_info)
                            behavior_info["interaction_type"] = behavior_type
                        else:
                            continue  # 无法获取内容信息，跳过

                    elif behavior_type == "search":
                        search_query = query_params.get("search", "")
                        if search_query:
                            behavior_info.update(
                                {
                                    "content_type": "search",
                                    "search_query": search_query,
                                    "interaction_type": "search",
                                }
                            )
                        else:
                            continue

                    elif behavior_type == "browse_list":
                        behavior_info.update(
                            {
                                "content_type": default_content_type,
                                "interaction_type": "browse_list",
                                "list_params": query_params,
                            }
                        )

                    return behavior_info

        return None

    async def _extract_content_info_from_interaction(
        self, request: Request, response, behavior_type: str
    ) -> dict | None:
        """从交互请求中提取内容信息"""
        try:
            # 尝试从请求体获取内容信息
            if hasattr(request, "_body"):
                body = await request.body()
                if body:
                    data = json.loads(body.decode())
                    content_type = data.get("content_type")
                    content_id = data.get("content_id")

                    if content_type and content_id:
                        return {"content_type": content_type, "content_id": content_id}

            # 如果请求体中没有，尝试从响应中获取
            # 注意：这里需要小心处理，避免消费响应流

        except Exception as e:
            logger.debug(f"提取交互内容信息失败: {e}")

        return None

    async def _record_behavior_async(
        self, user_info: dict, behavior_info: dict, request: Request
    ) -> None:
        """异步记录用户行为"""
        try:
            # 获取数据库连接 - 正确的使用方式，让依赖注入系统管理连接生命周期
            async for db in get_db():
                await self._process_behavior(db, user_info, behavior_info, request)
                break  # 只执行一次

        except Exception as e:
            logger.error(f"记录用户行为失败: {e}")

    async def _process_behavior(
        self, db, user_info: dict, behavior_info: dict, request: Request
    ) -> None:
        """处理具体的行为记录逻辑"""
        user_id = user_info["user_id"]
        behavior_type = behavior_info["behavior_type"]

        # 解析用户代理信息
        user_agent = request.headers.get("user-agent", "")
        parsed_ua = parse(user_agent)
        device_type = (
            "mobile" if parsed_ua.is_mobile else "tablet" if parsed_ua.is_tablet else "desktop"
        )

        # 获取IP地址
        ip_address = self._get_client_ip(request)

        if behavior_type == "content_view":
            # 记录内容浏览行为
            await self.behavior_tracking_service.track_content_view(
                db=db,
                user_id=user_id,
                content_type=behavior_info["content_type"],
                content_id=behavior_info["content_id"],
                request=request,
            )

        elif behavior_type in ["like", "favorite", "comment"]:
            # 记录交互行为
            await self.behavior_tracking_service.track_interaction(
                db=db,
                user_id=user_id,
                content_type=behavior_info["content_type"],
                content_id=behavior_info["content_id"],
                interaction_type=behavior_info["interaction_type"],
                extra_data={
                    "device_type": device_type,
                    "ip_address": ip_address,
                    "user_agent": user_agent,
                },
            )

        elif behavior_type == "search":
            # 记录搜索行为
            await self.behavior_tracking_service.track_search_behavior(
                db=db,
                user_id=user_id,
                search_query=behavior_info["search_query"],
                search_results_count=0,  # 可以从响应中获取
            )

        # 异步更新推荐缓存
        asyncio.create_task(self.recommendation_service.invalidate_user_cache(user_id))

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 优先从代理头获取真实IP
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        # 最后使用客户端IP
        return request.client.host if request.client else "unknown"
