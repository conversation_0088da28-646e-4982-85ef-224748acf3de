"""
背包API端点
实现Scratch背包功能的REST接口
"""

import logging
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Path, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from app import models
from app.api import deps
from app.api.permission_deps import require_permission
from app.core.limiter import limiter
from app.core.permission_system import ResourceType
from app.core.permissions import Permissions
from app.crud.backpack import backpack_crud
from app.schemas.backpack import (
    BackpackItemCreate,
    BackpackItemQuery,
    BackpackItemResponse,
    BackpackItemStats,
)
from app.services.backpack_storage import backpack_storage

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/{user_id}")
@limiter.limit("30/minute")
async def get_backpack_items(
    request: Request,
    user_id: int = Path(..., description="用户ID"),
    query_params: BackpackItemQuery = Depends(),
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(
        require_permission(
            Permissions.USER_READ_OWN, resource_type=ResourceType.USER, resource_id_key="user_id"
        )
    ),
) -> list[BackpackItemResponse]:
    """
    获取用户背包项目列表

    - **user_id**: 用户ID，必须与token中的用户标识一致
    - **limit**: 每页数量，默认20，最大50
    - **offset**: 偏移量，默认0
    - **type**: 按类型筛选（可选）
    """

    try:
        # 获取背包项目列表
        items = await backpack_crud.get_items_by_user_id(
            db=db,
            user_id=user_id,
            limit=query_params.limit,
            offset=query_params.offset,
            item_type=query_params.type,
        )

        # 转换为响应模型
        response_items = []
        for item in items:
            response_item = BackpackItemResponse.model_validate(item)
            response_items.append(response_item)

        logger.info(f"获取用户 {user_id} 的背包项目列表，共 {len(response_items)} 项")
        return response_items

    except Exception as e:
        logger.error(f"获取背包项目列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取背包项目列表失败"
        ) from e


@router.post("/{user_id}")
@limiter.limit("30/minute")
async def create_backpack_item(
    request: Request,
    user_id: int = Path(..., description="用户ID"),
    db: AsyncSession = Depends(deps.get_db),
    item_data: BackpackItemCreate = Depends(),
    current_user: models.User = Depends(
        require_permission(
            Permissions.USER_UPDATE_OWN, resource_type=ResourceType.USER, resource_id_key="user_id"
        )
    ),
) -> BackpackItemResponse:
    """
    创建背包项目

    - **user_id**: 用户ID，必须与token中的用户标识一致
    - **type**: 项目类型（costume/sound/sprite/script）
    - **mime**: MIME类型
    - **name**: 用户自定义名称
    - **body**: Base64编码的资源内容（无data URI前缀）
    - **thumbnail**: Base64编码的缩略图内容（无data URI前缀）
    """

    try:
        # 验证MIME类型与项目类型的兼容性
        if not backpack_storage.validate_mime_type(item_data.mime, item_data.type):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"MIME类型 {item_data.mime} 与项目类型 {item_data.type} 不兼容",
            )

        # 保存文件（支持去重）
        body_path, thumbnail_path, body_size = await backpack_storage.save_backpack_item(
            user_id=user_id,
            item_type=item_data.type,
            mime_type=item_data.mime,
            body_base64=item_data.body,
            thumbnail_base64=item_data.thumbnail,
            db=db,
        )

        # 创建数据库记录
        db_item = await backpack_crud.create_item(
            db=db,
            user_id=user_id,
            item_data=item_data,
            body_path=body_path,
            thumbnail_path=thumbnail_path,
            size_bytes=body_size,
        )

        # 转换为响应模型
        response_item = BackpackItemResponse.model_validate(db_item)

        logger.info(f"用户 {user_id} 创建背包项目成功: {response_item.id}")
        return response_item

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建背包项目失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="创建背包项目失败"
        ) from e


@router.delete("/{user_id}/{item_id}")
@limiter.limit("30/minute")
async def delete_backpack_item(
    request: Request,
    user_id: int = Path(..., description="用户ID"),
    item_id: str = Path(..., description="项目ID"),
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(
        require_permission(
            Permissions.USER_UPDATE_OWN, resource_type=ResourceType.USER, resource_id_key="user_id"
        )
    ),
) -> None:
    """
    删除背包项目

    - **user_id**: 用户ID，必须与token中的用户标识一致
    - **item_id**: 项目ID
    """

    try:
        # 转换UUID
        try:
            uuid_item_id = UUID(item_id)
        except ValueError:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="无效的项目ID格式")

        # 获取项目信息（用于删除文件）
        item = await backpack_crud.get_item_by_id(db, uuid_item_id, user_id)
        if not item:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

        # 删除数据库记录
        success = await backpack_crud.delete_item(db, uuid_item_id, user_id)
        if not success:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

        # 异步删除文件（支持引用计数）
        try:
            await backpack_storage.delete_backpack_item_files(
                item.body_path, item.thumbnail_path, db
            )
        except Exception as e:
            logger.warning(f"删除文件失败，但数据库记录已删除: {str(e)}")

        logger.info(f"用户 {user_id} 删除背包项目成功: {item_id}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除背包项目失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="删除背包项目失败"
        ) from e


@router.get("/{user_id}/stats")
@limiter.limit("10/minute")
async def get_backpack_stats(
    request: Request,
    user_id: int = Path(..., description="用户ID"),
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(
        require_permission(
            Permissions.USER_READ_OWN, resource_type=ResourceType.USER, resource_id_key="user_id"
        )
    ),
) -> BackpackItemStats:
    """
    获取用户背包统计信息

    - **user_id**: 用户ID，必须与token中的用户标识一致
    """

    try:
        # 获取统计信息
        stats = await backpack_crud.get_user_storage_stats(db, user_id)

        # 构建响应
        response_stats = BackpackItemStats(
            costume_count=stats["by_type"].get("costume", {}).get("count", 0),
            sound_count=stats["by_type"].get("sound", {}).get("count", 0),
            sprite_count=stats["by_type"].get("sprite", {}).get("count", 0),
            script_count=stats["by_type"].get("script", {}).get("count", 0),
            total_count=stats["total_count"],
            total_size=stats["total_size"],
        )

        logger.info(f"获取用户 {user_id} 的背包统计信息成功")
        return response_stats

    except Exception as e:
        logger.error(f"获取背包统计信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取背包统计信息失败"
        ) from e
