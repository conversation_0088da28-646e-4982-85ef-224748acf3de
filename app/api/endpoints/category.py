from typing import Literal

from fastapi import APIRouter, Depends, Query
from fastapi_cache.decorator import cache
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session, selectinload

from app import crud, schemas
from app.api import deps
from app.models.category import Category

router = APIRouter()


@router.get(
    "/{category_id}/related_info",
    response_model=schemas.CategoryRelatedInfo,
    summary="获取分类相关的推荐信息",
)
@cache(expire=3600)
async def get_category_related_info(
    category_id: int,
    db: AsyncSession = Depends(deps.get_db),
):
    """
    获取指定分类的详情信息和相关推荐。
    - **分类详情**: 分类的基本信息和统计数据。
    - **相关作者**: 在该分类下发布视频最多的作者。
    - **热门标签**: 在该分类下视频中使用最频繁的标签。
    - 结果会缓存1小时以提高性能。
    """
    # 获取分类详情，预加载 children 关系
    from sqlalchemy import select
    result = await db.execute(
        select(Category)
        .options(selectinload(Category.children))
        .where(Category.id == category_id)
    )
    category = result.scalar_one_or_none()
    if not category:
        from fastapi import HTTPException, status
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="分类不存在"
        )
    
    # 获取相关作者和热门标签
    authors = await crud.user.get_top_authors_in_category(db, category_id=category_id)
    tags = await crud.tag.get_top_tags_in_category(db, category_id=category_id)
    
    return schemas.CategoryRelatedInfo(
        category=category,
        related_authors=authors, 
        hot_tags=tags
    )


@router.get(
    "/tree",
    response_model=list[schemas.Category],
    summary="获取分类层级树",
)
async def get_category_tree(
    db: Session = Depends(deps.get_db),
    category_type: Literal["article", "video"] | None = Query(None, description="分类类型"),
) -> list[schemas.Category]:
    """
    获取所有分类的层级结构树。
    这对于构建分类导航菜单非常有用。
    """
    return await crud.category.get_category_tree(db, category_type=category_type)
