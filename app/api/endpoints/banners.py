from fastapi import APIRouter, Depends, HTTPException, Path, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api import deps
from app.api.permission_deps import require_permission
from app.core.permission_system import Action, ResourceType, Scope
from app.core.permission_system import Permission as PermissionObject
from app.services.service_factory import get_video_aggregation_service
from app.services.video_aggregation_service import VideoAggregationService

router = APIRouter()


@router.post("/", response_model=schemas.Banner, status_code=status.HTTP_201_CREATED)
async def create_banner(
    *,
    db: AsyncSession = Depends(deps.get_db),
    banner_in: schemas.BannerCreate,
    current_user: models.User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.BANNER, action=Action.CREATE, scope=Scope.ALL)
        )
    ),
):
    """创建轮播图"""
    # 如果指定了视频ID，验证视频是否存在
    if banner_in.video_id:
        video = await crud.video.get(db, id=banner_in.video_id)
        if not video:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Video with id {banner_in.video_id} not found",
            )

    # 创建轮播图
    banner = await crud.banner.create(db, obj_in=banner_in)
    return banner


@router.get("/", response_model=list[schemas.Banner])
async def read_banners(
    *,
    db: AsyncSession = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    banner_type: str | None = Query(None, description="轮播图类型筛选"),
    is_active: bool | None = Query(None, description="是否启用筛选"),
):
    """获取轮播图列表"""
    filters = {}
    if banner_type is not None:
        filters["banner_type"] = banner_type
    if is_active is not None:
        filters["is_active"] = is_active

    # 默认只返回未删除的轮播图
    filters["is_deleted"] = False

    banners = await crud.banner.get_multi_with_filters(db, skip=skip, limit=limit, filters=filters)
    return banners


@router.get("/home", response_model=list[schemas.BannerWithVideo])
async def read_home_banners(
    *,
    db: AsyncSession = Depends(deps.get_db),
    limit: int = Query(5, description="返回的轮播图数量"),
    video_aggregation_service: VideoAggregationService = Depends(get_video_aggregation_service),
):
    """获取首页轮播图，包含视频信息"""
    # 获取首页轮播图
    banners = await crud.banner.get_multi_with_filters(
        db,
        skip=0,
        limit=limit,
        filters={"banner_type": "home", "is_active": True, "is_deleted": False},
        order_by="sort_order",
        order_direction="desc",
    )

    # 获取关联的视频信息
    result = []
    for banner in banners:
        banner_dict = banner.__dict__.copy()
        # 处理 None 值字段，确保符合 schema 要求
        if banner_dict.get("sort_order") is None:
            banner_dict["sort_order"] = 0

        if banner.video_id:
            video_out = await video_aggregation_service.get_aggregated_video(
                db=db, video_id=banner.video_id, current_user=None
            )
            if video_out:
                # 从VideoOut中提取VideoBase字段，排除聚合字段
                video_base_data = video_out.model_dump(
                    exclude={
                        "stats",
                        "author",
                        "review",
                        "folder",
                        "meta",
                        "extra",
                        "created_at",
                        "updated_at",
                    }
                )
                banner_dict["video"] = schemas.VideoBase(**video_base_data)
        result.append(banner_dict)

    return result


@router.get("/{banner_id}", response_model=schemas.Banner)
async def read_banner(
    *,
    db: AsyncSession = Depends(deps.get_db),
    banner_id: int = Path(..., description="轮播图ID"),
):
    """获取轮播图详情"""
    banner = await crud.banner.get(db, id=banner_id)
    if not banner or banner.is_deleted:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Banner not found",
        )
    return banner


@router.put("/{banner_id}", response_model=schemas.Banner)
async def update_banner(
    *,
    db: AsyncSession = Depends(deps.get_db),
    banner_id: int = Path(..., description="轮播图ID"),
    banner_in: schemas.BannerUpdate,
    current_user: models.User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.BANNER, action=Action.UPDATE, scope=Scope.ALL)
        )
    ),
):
    """更新轮播图"""
    banner = await crud.banner.get(db, id=banner_id)
    if not banner or banner.is_deleted:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Banner not found",
        )

    # 如果更新了视频ID，验证视频是否存在
    if banner_in.video_id is not None:
        video = await crud.video.get(db, id=banner_in.video_id)
        if not video:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Video with id {banner_in.video_id} not found",
            )

    banner = await crud.banner.update(db, db_obj=banner, obj_in=banner_in)
    return banner


@router.delete("/{banner_id}", response_model=schemas.Banner)
async def delete_banner(
    *,
    db: AsyncSession = Depends(deps.get_db),
    banner_id: int = Path(..., description="轮播图ID"),
    current_user: models.User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.BANNER, action=Action.DELETE, scope=Scope.ALL)
        )
    ),
):
    """删除轮播图（软删除）"""
    banner = await crud.banner.get(db, id=banner_id)
    if not banner or banner.is_deleted:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Banner not found",
        )

    # 软删除
    banner = await crud.banner.update(db, db_obj=banner, obj_in={"is_deleted": True})
    return banner
