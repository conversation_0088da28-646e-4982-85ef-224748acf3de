from fastapi import APIRouter, Depends
from fastapi_cache.decorator import cache
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud
from app.api import deps
from app.schemas.tag import Tag

router = APIRouter()


@router.get("/default", response_model=list[Tag])
@cache(expire=3600)
async def read_default_tags(
    db: AsyncSession = Depends(deps.get_db),
):
    """
    Retrieve default tags.
    """
    tags = await crud.tag.get_default_tags(db)
    return tags
