"""Token 管理相关的 API 端点"""

from fastapi import APIRouter, Depends, HTTPException, Request, Response
from pydantic import BaseModel

from app.api import deps
from app.api.permission_deps import require_permission
from app.core.permission_system import Action, ResourceType, Scope
from app.core.permission_system import Permission as PermissionObject
from app.models.user import User
from app.services.interfaces.token_service_interface import ITokenService
from app.services.service_factory import get_token_service

router = APIRouter()


class TokenInfo(BaseModel):
    """Token 信息模型"""

    username: str
    device_id: int | None
    token_type: str
    created_at: str
    expires_at: str


class TokenListResponse(BaseModel):
    """Token 列表响应模型"""

    tokens: list[TokenInfo]
    total: int


class RevokeTokenRequest(BaseModel):
    """撤销 Token 请求模型"""

    device_id: int | None = None


class RefreshTokenRequest(BaseModel):
    """刷新令牌请求模型"""

    refresh_token: str


class AccessTokenResponse(BaseModel):
    """访问令牌响应模型"""

    access_token: str
    token_type: str = "bearer"


@router.get("/tokens", response_model=TokenListResponse)
async def get_user_tokens(
    current_user: User = Depends(deps.get_current_user),
    token_service: ITokenService = Depends(get_token_service),
) -> TokenListResponse:
    """获取当前用户的所有活跃 token"""
    try:
        tokens = await token_service.get_user_active_tokens(current_user.username)

        token_infos = []
        for token in tokens:
            token_infos.append(
                TokenInfo(
                    username=token.get("username", ""),
                    device_id=token.get("device_id"),
                    token_type=token.get("token_type", "access"),
                    created_at=token.get("created_at", ""),
                    expires_at=token.get("expires_at", ""),
                )
            )

        return TokenListResponse(tokens=token_infos, total=len(token_infos))

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取 token 列表失败：{str(e)}")


@router.post("/revoke-token")
async def revoke_token(
    request: RevokeTokenRequest,
    response: Response,
    current_user: User = Depends(deps.get_current_user),
    token_service: ITokenService = Depends(get_token_service),
) -> dict:
    """撤销指定设备的 token"""
    try:
        if request.device_id:
            # 撤销指定设备的 token
            success = await token_service.revoke_device_token(
                current_user.username, request.device_id
            )
            if success:
                try:
                    from app.utils.cookie_utils import clear_auth_cookies

                    clear_auth_cookies(response)
                except Exception:
                    pass
                return {"message": f"设备 {request.device_id} 的 token 已撤销"}
            else:
                raise HTTPException(status_code=404, detail="未找到指定设备的 token")
        else:
            raise HTTPException(status_code=400, detail="请指定要撤销的设备ID")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"撤销 token 失败：{str(e)}")


@router.post("/revoke-all-tokens")
async def revoke_all_tokens(
    response: Response,
    current_user: User = Depends(deps.get_current_user),
    token_service: ITokenService = Depends(get_token_service),
) -> dict:
    """撤销当前用户的所有 token（除了当前使用的）"""
    try:
        # 注意：这会撤销所有 token，包括当前的，用户需要重新登录
        revoked_count = await token_service.revoke_user_tokens(current_user.username)

        try:
            from app.utils.cookie_utils import clear_auth_cookies

            clear_auth_cookies(response)
        except Exception:
            pass
        return {
            "message": f"已撤销 {revoked_count} 个 token，请重新登录",
            "revoked_count": revoked_count,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"撤销所有 token 失败：{str(e)}")


@router.post("/refresh", response_model=AccessTokenResponse)
async def refresh_access_token(
    request: Request,
    response: Response,
    token_service: ITokenService = Depends(get_token_service),
) -> AccessTokenResponse:
    """使用 refresh_token 刷新并返回新的 access_token"""
    try:
        # 支持从 Cookie 读取 refresh_token
        body = (
            await request.json()
            if request.headers.get("content-type", "").startswith("application/json")
            else {}
        )
        refresh_token = body.get("refresh_token") if isinstance(body, dict) else None
        if not refresh_token:
            refresh_token = request.cookies.get("refresh_token")

        if not refresh_token:
            raise HTTPException(status_code=401, detail="缺少刷新令牌")

        # 验证并轮换刷新令牌
        token_info = await token_service.verify_token(refresh_token)
        if not token_info or token_info.get("type") != "refresh":
            raise HTTPException(status_code=401, detail="无效的刷新令牌")

        username = token_info.get("username") or token_info.get("sub")
        device_id = token_info.get("device_id")
        if not username:
            raise HTTPException(status_code=401, detail="无效的刷新令牌载荷")

        # 新的 access_token
        new_access_token = await token_service.create_access_token(
            data={"sub": username}, device_id=device_id
        )

        # 新的 refresh_token（轮换）
        from datetime import timedelta

        from app.config import get_settings

        settings = get_settings()
        refresh_expires = timedelta(minutes=settings.REFRESH_TOKEN_EXPIRE_MINUTES)
        new_refresh_token = await token_service.create_token(
            username=username,
            device_id=device_id,
            expires_delta=refresh_expires,
            token_type="refresh",
        )

        # 撤销旧的 refresh_token
        await token_service.revoke_token(refresh_token)

        # 设置新的 HttpOnly 刷新令牌 Cookie
        from app.utils.cookie_utils import set_auth_cookies

        set_auth_cookies(response, access_token=None, refresh_token=new_refresh_token)

        return AccessTokenResponse(access_token=new_access_token)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"刷新令牌失败：{str(e)}")


@router.post("/logout")
async def logout(
    response: Response,
    current_user: User = Depends(deps.get_current_user),
    token_service: ITokenService = Depends(get_token_service),
) -> dict:
    """用户登出（撤销当前 token）"""
    try:
        # 这里需要获取当前的 token，但由于 FastAPI 的限制，我们无法直接获取
        # 作为替代方案，我们撤销用户的所有 token
        revoked_count = await token_service.revoke_user_tokens(current_user.username)
        try:
            from app.utils.cookie_utils import clear_auth_cookies

            clear_auth_cookies(response)
        except Exception:
            pass
        return {"message": "登出成功", "revoked_count": revoked_count}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"登出失败：{str(e)}") from e


# 管理员专用端点
@router.post("/admin/revoke-user-tokens")
async def admin_revoke_user_tokens(
    username: str,
    current_user: User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.USER, action=Action.MANAGE, scope=Scope.ALL)
        )
    ),
    token_service: ITokenService = Depends(get_token_service),
) -> dict:
    """管理员撤销指定用户的所有 token"""
    try:
        revoked_count = await token_service.revoke_user_tokens(username)

        return {
            "message": f"已撤销用户 {username} 的所有 token",
            "username": username,
            "revoked_count": revoked_count,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"撤销用户 token 失败：{str(e)}")


@router.get("/admin/user-tokens/{username}", response_model=TokenListResponse)
async def admin_get_user_tokens(
    username: str,
    current_user: User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.USER, action=Action.MANAGE, scope=Scope.ALL)
        )
    ),
    token_service: ITokenService = Depends(get_token_service),
) -> TokenListResponse:
    """管理员获取指定用户的所有活跃 token"""
    try:
        tokens = await token_service.get_user_active_tokens(username)

        token_infos = []
        for token in tokens:
            token_infos.append(
                TokenInfo(
                    username=token.get("username", ""),
                    device_id=token.get("device_id"),
                    token_type=token.get("token_type", "access"),
                    created_at=token.get("created_at", ""),
                    expires_at=token.get("expires_at", ""),
                )
            )

        return TokenListResponse(tokens=token_infos, total=len(token_infos))

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户 token 列表失败：{str(e)}") from e
