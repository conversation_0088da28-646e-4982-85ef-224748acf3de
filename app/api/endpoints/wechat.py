"""
微信认证API

提供PC端微信扫码登录所需的所有接口。
"""

from fastapi import APIRouter, Depends, HTTPException, Request, Response
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession

from app.api import deps
from app.core.enums import AuthType
from app.exceptions.auth import AuthenticationError
from app.schemas.auth import AuthCompletionResponse, AuthInitiationResponse
from app.schemas.wechat import WeChatScanStatus as LoginStatusResponse
from app.services.auth_orchestrator_service import AuthOrchestratorService
from app.services.service_factory import get_auth_orchestrator_service

router = APIRouter()


class WeChatBindIn(BaseModel):
    """用于将微信账号与手机号绑定的请求体。"""

    openid: str = Field(..., description="微信用户的OpenID，在扫码后由状态查询接口返回")
    phone: str = Field(..., description="要绑定的手机号码", examples=["13800138000"])
    code: str = Field(..., min_length=6, max_length=6, description="手机收到的6位验证码")


@router.post(
    "/qr-code/create",
    response_model=AuthInitiationResponse,
    summary="创建微信登录二维码",
    description="为PC端扫码登录流程创建一个有时效性的二维码。",
)
async def create_wechat_qr_code(
    auth_orchestrator: AuthOrchestratorService = Depends(get_auth_orchestrator_service),
) -> AuthInitiationResponse:
    """
    调用此接口来获取一个用于微信扫码登录的二维码。
    前端应使用返回的 `qr_url` 来展示二维码，并保存 `scene_str` 用于后续的状态查询。
    """
    try:
        response = await auth_orchestrator.initiate_authentication(
            auth_type=AuthType.WECHAT, request_data={}
        )
        return response
    except AuthenticationError as e:
        raise HTTPException(status_code=e.status_code, detail=e.message) from e


@router.get(
    "/qr-code/status/{scene_str}",
    response_model=LoginStatusResponse,
    summary="获取二维码扫描状态",
    description="前端在展示二维码后，应定期轮询此接口以获取最新状态。",
)
async def get_wechat_qr_code_status(
    scene_str: str,
    request: Request,
    response: Response,
    db: AsyncSession = Depends(deps.get_db),
    auth_orchestrator: AuthOrchestratorService = Depends(get_auth_orchestrator_service),
) -> LoginStatusResponse:
    """
    获取微信扫码登录的当前状态。

    **状态流程:**
    - `waiting`: 等待用户扫码。
    - `scanned`: 用户已扫码，后端处理中。
    - `confirmed`: 老用户登录成功，直接返回Token。
    - `bind_phone`: 新用户，需要绑定手机号。
    - `device_verification`: 老用户但新设备，需要设备验证。
    - `expired`: 二维码已过期。
    """
    try:
        # The service now returns the correct Pydantic model directly
        result = await auth_orchestrator.get_authentication_status(
            auth_type=AuthType.WECHAT, identifier=scene_str, request=request, db=db
        )
        # 如果已确认登录，设置 HttpOnly cookies，并移除响应体中的 refresh_token
        from app.utils.cookie_utils import set_auth_cookies
        if getattr(result, "status", None) == "confirmed" and (
            getattr(result, "access_token", None) or getattr(result, "refresh_token", None)
        ):
            set_auth_cookies(
                response,
                access_token=getattr(result, "access_token", None),
                refresh_token=getattr(result, "refresh_token", None),
            )
            try:
                result.refresh_token = None
            except Exception:
                pass
        return result
    except AuthenticationError as e:
        raise HTTPException(status_code=e.status_code, detail=e.message) from e


@router.post(
    "/bind",
    response_model=AuthCompletionResponse,
    summary="绑定手机号并完成登录",
    description="在用户扫码且状态为 `bind_phone` 后，调用此接口完成最终的账号绑定和登录。",
)
async def bind_wechat_account(
    bind_in: WeChatBindIn,
    request: Request,
    response: Response,
    db: AsyncSession = Depends(deps.get_db),
    auth_orchestrator: AuthOrchestratorService = Depends(get_auth_orchestrator_service),
) -> AuthCompletionResponse:
    """
    将微信 `openid` 与用户的手机号进行绑定。
    如果手机号未注册，将自动创建新用户。
    成功后，返回最终的访问令牌。
    """
    try:
        result = await auth_orchestrator.complete_authentication(
            auth_type=AuthType.WECHAT,
            verification_data={
                "openid": bind_in.openid,
                "phone": bind_in.phone,
                "code": bind_in.code,
            },
            request=request,
            db=db,
        )
        from app.utils.cookie_utils import set_auth_cookies
        if result.access_token or result.refresh_token:
            set_auth_cookies(response, access_token=result.access_token, refresh_token=result.refresh_token)
            try:
                result.refresh_token = None
            except Exception:
                pass
        return result
    except AuthenticationError as e:
        raise HTTPException(status_code=e.status_code, detail=e.message) from e


@router.get(
    "/callback",
    summary="微信公众号服务器验证",
    description="**【仅供微信服务器调用】** 用于处理微信服务器的URL验证请求。",
    include_in_schema=False,  # 在 OpenAPI 文档中隐藏此端点
)
async def wechat_server_verification(
    request: Request,
    auth_orchestrator: AuthOrchestratorService = Depends(get_auth_orchestrator_service),
):
    """
    处理微信服务器的GET请求，用于验证服务器URL的有效性。
    """
    return await auth_orchestrator.handle_wechat_callback(request)


@router.post(
    "/callback",
    summary="微信公众号事件回调",
    description="**【仅供微信服务器调用】** 用于接收微信服务器推送的事件，如用户扫码、关注等。",
    include_in_schema=True,  # 在 OpenAPI 文档中隐藏此端点
)
async def wechat_server_callback(
    request: Request,
    auth_orchestrator: AuthOrchestratorService = Depends(get_auth_orchestrator_service),
):
    """
    处理微信服务器的事件回调。

    此端点作为微信服务器请求的统一入口，将请求直接转发给认证编排服务进行处理。
    它能够处理GET请求（用于服务器验证）和POST请求（用于事件通知）。
    """
    return await auth_orchestrator.handle_wechat_callback(request)
