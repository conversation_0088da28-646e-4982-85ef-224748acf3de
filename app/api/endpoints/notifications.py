"""通知相关API端点"""

import uuid

from fastapi import APIRouter, Depends, HTTPException, Query, WebSocket, WebSocketDisconnect
from sqlalchemy.ext.asyncio import AsyncSession

from app.api import deps
from app.core.logging import logger
from app.models.user import User
from app.notifications.models import NotificationStatus
from app.notifications.schemas import (
    NotificationCreate,
    NotificationListResponse,
    NotificationResponse,
    UnreadCountResponse,
)
from app.services.notification_service import NotificationService
from app.services.websocket_manager import websocket_handler, websocket_manager

router = APIRouter()


def get_notification_service() -> NotificationService:
    """获取通知服务实例"""
    return NotificationService()


@router.get("/", response_model=NotificationListResponse, summary="获取通知列表")
async def get_notifications(
    status: NotificationStatus | None = Query(None, description="通知状态筛选"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    offset: int = Query(0, ge=0, description="偏移量"),
    db: AsyncSession = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    notification_service: NotificationService = Depends(get_notification_service),
) -> NotificationListResponse:
    """
    获取当前用户的通知列表

    - **status**: 可选的状态筛选 (unread, read, deleted)
    - **limit**: 每页返回的通知数量 (1-100)
    - **offset**: 分页偏移量
    """
    try:
        # 验证通知状态枚举，如果是字符串尝试转换为小写
        if isinstance(status, str):
            try:
                status = NotificationStatus(status.lower())
            except ValueError as e:
                raise HTTPException(
                    status_code=400, detail="无效的通知状态，请使用 'unread', 'read', 或 'deleted'"
                ) from e
        return await notification_service.get_user_notifications(
            db=db, user_id=current_user.id, status=status, limit=limit, offset=offset
        )
    except ValueError as e:
        logger.warning(f"无效的通知状态参数: {str(e)}")
        raise HTTPException(
            status_code=400, detail="无效的通知状态，请使用 'unread', 'read', 或 'deleted'"
        ) from e
    except Exception as e:
        logger.error(f"获取通知列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取通知列表失败") from e


@router.get("/unread-count", response_model=UnreadCountResponse, summary="获取未读通知数量")
async def get_unread_count(
    db: AsyncSession = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    notification_service: NotificationService = Depends(get_notification_service),
) -> UnreadCountResponse:
    """获取当前用户的未读通知数量"""
    try:
        return await notification_service.get_unread_count(db=db, user_id=current_user.id)
    except Exception as e:
        logger.error(f"获取未读通知数量失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取未读通知数量失败") from e


@router.post("/", response_model=NotificationResponse, summary="创建通知")
async def create_notification(
    notification_in: NotificationCreate,
    db: AsyncSession = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    notification_service: NotificationService = Depends(get_notification_service),
) -> NotificationResponse:
    """
    创建新通知（管理员功能）

    注意：此接口通常用于系统内部或管理员创建通知
    """
    try:
        return await notification_service.create_notification(
            db=db,
            user_id=notification_in.user_id,
            notification_type=notification_in.type,
            title=notification_in.title,
            message=notification_in.message,
            priority=notification_in.priority,
            data=notification_in.data,
            action_url=notification_in.action_url,
            expires_in_hours=notification_in.expires_in_hours,
        )
    except Exception as e:
        logger.error(f"创建通知失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建通知失败") from e


@router.patch(
    "/{notification_id}/read", response_model=NotificationResponse, summary="标记通知为已读"
)
async def mark_notification_as_read(
    notification_id: int,
    db: AsyncSession = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    notification_service: NotificationService = Depends(get_notification_service),
) -> NotificationResponse:
    """标记指定通知为已读"""
    try:
        notification = await notification_service.mark_as_read(
            db=db, notification_id=notification_id, user_id=current_user.id
        )

        if not notification:
            raise HTTPException(status_code=404, detail="通知不存在或无权限访问")

        return notification
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"标记通知为已读失败: {str(e)}")
        raise HTTPException(status_code=500, detail="标记通知为已读失败") from e


@router.patch("/read-all", summary="标记所有通知为已读")
async def mark_all_notifications_as_read(
    db: AsyncSession = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    notification_service: NotificationService = Depends(get_notification_service),
) -> dict:
    """标记当前用户的所有未读通知为已读"""
    try:
        count = await notification_service.mark_all_as_read(db=db, user_id=current_user.id)

        return {"message": f"成功标记 {count} 条通知为已读", "count": count}
    except Exception as e:
        logger.error(f"批量标记通知为已读失败: {str(e)}")
        raise HTTPException(status_code=500, detail="批量标记通知为已读失败") from e


@router.delete("/{notification_id}", summary="删除通知")
async def delete_notification(
    notification_id: int,
    db: AsyncSession = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    notification_service: NotificationService = Depends(get_notification_service),
) -> dict:
    """删除指定通知"""
    try:
        success = await notification_service.delete_notification(
            db=db, notification_id=notification_id, user_id=current_user.id
        )

        if not success:
            raise HTTPException(status_code=404, detail="通知不存在或无权限访问")

        return {"message": "通知删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除通知失败: {str(e)}")
        raise HTTPException(status_code=500, detail="删除通知失败") from e


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    db: AsyncSession = Depends(deps.get_db),
    # current_user: User = Depends(deps.get_current_user_websocket),
):
    """

    WebSocket端点用于实时通知推送

    连接后会自动接收：
    - 新通知消息
    - 未读数量更新
    - 系统通知
    """
    connection_id = str(uuid.uuid4())
    # TODO 测试使用 后面需要删除
    try:
        await websocket_handler.handle_connection(
            websocket=websocket, user_id=2, connection_id=connection_id, db=db
        )
    except WebSocketDisconnect:
        logger.info(f"WebSocket连接断开 - 用户ID: {2}")
    except Exception as e:
        logger.error(f"WebSocket连接异常: {str(e)}")


@router.get("/stats", summary="获取通知统计信息")
async def get_notification_stats(current_user: User = Depends(deps.get_current_user)) -> dict:
    """
    获取通知相关统计信息（管理员功能）

    包括：
    - WebSocket连接统计
    - 在线用户数量
    """
    try:
        connection_stats = websocket_manager.get_connection_stats()
        online_users = websocket_manager.get_online_users()

        return {
            "websocket_stats": connection_stats,
            "online_user_count": len(online_users),
            "current_user_connections": websocket_manager.get_user_connection_count(
                current_user.id
            ),
        }
    except Exception as e:
        logger.error(f"获取通知统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取统计信息失败") from e


@router.post("/broadcast", summary="广播通知给所有用户")
async def broadcast_notification(
    broadcast_data: dict,  # 临时使用dict，避免导入问题
    db: AsyncSession = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_superuser),  # 仅管理员
    notification_service: NotificationService = Depends(get_notification_service),
) -> dict:
    """
    广播通知给所有用户（管理员专用）

    **权限要求**: 仅超级管理员可以使用此接口

    **功能说明**:
    - 立即向所有在线用户推送WebSocket消息
    - 异步为所有用户创建通知记录
    - 支持设置过期时间和目标用户类型

    **请求体示例**:
    ```json
    {
        "title": "系统维护通知",
        "message": "系统将于今晚22:00-24:00进行维护，期间服务可能中断",
        "type": "system_update",
        "priority": "high",
        "action_url": "/maintenance",
        "expires_in_hours": 168,
        "target_user_type": "all"
    }
    ```
    """
    try:
        # 验证必需字段
        required_fields = ["title", "message"]
        for field in required_fields:
            if field not in broadcast_data:
                raise HTTPException(status_code=400, detail=f"缺少必需字段: {field}")

        # 设置默认值
        from app.notifications.models import NotificationPriority, NotificationType

        notification_type = NotificationType.SYSTEM_UPDATE
        if "type" in broadcast_data:
            try:
                notification_type = NotificationType(broadcast_data["type"])
            except ValueError as e:
                raise HTTPException(status_code=400, detail="无效的通知类型") from e

        priority = NotificationPriority.HIGH
        if "priority" in broadcast_data:
            try:
                priority = NotificationPriority(broadcast_data["priority"])
            except ValueError as e:
                raise HTTPException(status_code=400, detail="无效的优先级") from e

        # 执行广播
        result = await notification_service.broadcast_notification_to_all_users(
            db=db,
            title=broadcast_data["title"],
            message=broadcast_data["message"],
            notification_type=notification_type,
            priority=priority,
            data=broadcast_data.get("data"),
            action_url=broadcast_data.get("action_url"),
            expires_in_hours=broadcast_data.get("expires_in_hours", 168),
            target_user_type=broadcast_data.get("target_user_type", "all"),
        )

        logger.info(
            f"管理员 {current_user.username} 发起广播通知 - 标题: {broadcast_data['title']}"
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"广播通知失败: {str(e)}")
        raise HTTPException(status_code=500, detail="广播通知失败") from e


@router.post("/broadcast/online-only", summary="仅向在线用户广播消息")
async def broadcast_to_online_users(
    broadcast_data: dict, current_user: User = Depends(deps.get_current_active_superuser)
) -> dict:
    """
    仅向当前在线用户发送实时消息（不创建数据库记录）

    **适用场景**:
    - 临时性通知
    - 系统状态更新
    - 紧急公告

    **请求体示例**:
    ```json
    {
        "title": "紧急通知",
        "message": "系统正在进行紧急维护，请保存您的工作",
        "data": {"urgent": true}
    }
    ```
    """
    try:
        # 验证必需字段
        required_fields = ["title", "message"]
        for field in required_fields:
            if field not in broadcast_data:
                raise HTTPException(status_code=400, detail=f"缺少必需字段: {field}")

        # 获取在线用户统计
        online_users = websocket_manager.get_online_users()
        online_count = len(online_users)

        if online_count == 0:
            return {"message": "当前没有在线用户", "online_users_notified": 0}

        # 发送实时消息
        await websocket_manager.broadcast_system_message(
            title=broadcast_data["title"],
            message=broadcast_data["message"],
            data=broadcast_data.get("data", {}),
        )

        logger.info(
            f"管理员 {current_user.username} 向 {online_count} 个在线用户发送实时消息 - 标题: {broadcast_data['title']}"
        )

        return {
            "message": f"成功向 {online_count} 个在线用户发送消息",
            "online_users_notified": online_count,
        }

    except Exception as e:
        logger.error(f"在线广播失败: {str(e)}")
        raise HTTPException(status_code=500, detail="在线广播失败") from e
