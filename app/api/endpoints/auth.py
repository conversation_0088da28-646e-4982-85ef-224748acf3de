"""
统一认证API

提供所有与用户认证相关的接口，包括短信认证、设备验证和微信认证流程。
所有接口均通过 `AuthOrchestratorService` 进行协调。
"""

from fastapi import APIRouter, Depends, HTTPException, Request, Response
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession

from app.api import deps
from app.core.enums import AuthType
from app.exceptions.auth import AuthenticationError
from app.schemas.auth import AuthCompletionResponse, AuthInitiationResponse
from app.services.auth_orchestrator_service import AuthOrchestratorService
from app.services.service_factory import get_auth_orchestrator_service

router = APIRouter()


class PhoneIn(BaseModel):
    """用于发送短信验证码的请求体。"""

    phone: str = Field(..., description="目标手机号码", examples=["13800138000"])
    template_type: str = Field(default="LOGIN", description="短信模板类型，用于指定发送的短信内容")


class VerifyCodeIn(PhoneIn):
    """用于通过短信验证码完成登录或注册的请求体。"""

    code: str = Field(..., min_length=6, max_length=6, description="收到的6位短信验证码")


class DeviceConfirmIn(BaseModel):
    """用于在新设备上完成安全验证的请求体。"""

    verification_token: str = Field(..., description="上一步认证成功后获取的设备验证令牌")
    code: str = Field(..., min_length=6, max_length=6, description="用于设备验证的6位短信验证码")


@router.post("/sms/send-code", response_model=AuthInitiationResponse, summary="发送短信验证码")
async def send_sms_code(
    phone_in: PhoneIn,
    auth_orchestrator: AuthOrchestratorService = Depends(get_auth_orchestrator_service),
) -> AuthInitiationResponse:
    """
    向指定手机号发送用于登录或注册的短信验证码。

    此接口会进行频率控制，防止滥用。
    """
    try:
        response = await auth_orchestrator.initiate_authentication(
            auth_type=AuthType.SMS, request_data=phone_in.model_dump()
        )
        return response
    except AuthenticationError as e:
        raise HTTPException(status_code=e.status_code, detail=e.message) from e


@router.post("/sms/login", response_model=AuthCompletionResponse, summary="短信登录或注册")
async def sms_login(
    verify_in: VerifyCodeIn,
    request: Request,
    response: Response,
    db: AsyncSession = Depends(deps.get_db),
    auth_orchestrator: AuthOrchestratorService = Depends(get_auth_orchestrator_service),
) -> AuthCompletionResponse:
    """
    使用手机号和短信验证码进行登录或注册。

    - **自动注册**: 如果手机号尚未注册，系统将自动创建一个新用户。
    - **设备验证**: 如果系统检测到用户正在从一个新设备登录，本次请求将不会返回
      最终的访问令牌。取而代之的是，会返回一个 `verification_token`，并要求
      用户调用 `/device/confirm` 接口以完成设备的安全验证。
    """

    result = await auth_orchestrator.complete_authentication(
        auth_type=AuthType.SMS,
        verification_data=verify_in.model_dump(),
        request=request,
        db=db,
    )
    from app.utils.cookie_utils import set_auth_cookies
    if result.access_token or result.refresh_token:
        set_auth_cookies(response, access_token=result.access_token, refresh_token=result.refresh_token)
        try:
            result.refresh_token = None
        except Exception:
            pass
    return result


@router.post("/device/confirm", response_model=AuthCompletionResponse, summary="确认新设备登录")
async def confirm_device(
    confirm_in: DeviceConfirmIn,
    response: Response,
    db: AsyncSession = Depends(deps.get_db),
    auth_orchestrator: AuthOrchestratorService = Depends(get_auth_orchestrator_service),
) -> AuthCompletionResponse:
    """
    在新设备上完成二次安全验证。

    当用户在新设备上登录时，需要调用此接口，提交收到的设备验证码，
    以将该设备标记为受信任，并获取最终的访问令牌。
    """
    try:
        result = await auth_orchestrator.handle_device_verification(
            verification_token=confirm_in.verification_token,
            verification_code=confirm_in.code,
            db=db,
        )
        from app.utils.cookie_utils import set_auth_cookies
        if result.access_token or result.refresh_token:
            set_auth_cookies(response, access_token=result.access_token, refresh_token=result.refresh_token)
            try:
                result.refresh_token = None
            except Exception:
                pass
        return result
    except AuthenticationError as e:
        raise HTTPException(status_code=e.status_code, detail=e.message) from e
