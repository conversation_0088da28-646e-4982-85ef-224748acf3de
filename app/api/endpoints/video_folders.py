from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.api import deps
from app.models.user import User
from app.schemas.video_folder import (
    VideoFolder,
    VideoFolderCreate,
    VideoFolderTree,
    VideoFolderUpdate,
)
from app.services.service_factory import get_video_folder_service
from app.services.video_folder_service import VideoFolderService

router = APIRouter()


@router.post("/", response_model=VideoFolder)
async def create_folder(
    *,
    db: AsyncSession = Depends(deps.get_db),
    folder_in: VideoFolderCreate,
    current_user: User = Depends(deps.get_current_user),
    video_folder_service: VideoFolderService = Depends(get_video_folder_service),
) -> VideoFolder:
    """创建视频文件夹（使用服务层）"""
    return await video_folder_service.create_folder(
        db=db, folder_in=folder_in, user_id=current_user.id
    )


@router.get("/tree", response_model=list[VideoFolderTree])
async def get_folder_tree(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    video_folder_service: VideoFolderService = Depends(get_video_folder_service),
) -> list[VideoFolderTree]:
    """获取文件夹树形结构（使用服务层）"""
    return await video_folder_service.get_folder_tree(db=db, user_id=current_user.id)


@router.put("/{folder_id}", response_model=VideoFolder)
async def update_folder(
    *,
    db: AsyncSession = Depends(deps.get_db),
    folder_id: int,
    folder_in: VideoFolderUpdate,
    current_user: User = Depends(deps.get_current_user),
    video_folder_service: VideoFolderService = Depends(get_video_folder_service),
) -> VideoFolder:
    """更新文件夹（使用服务层）"""
    return await video_folder_service.update_folder(
        db=db, folder_id=folder_id, folder_in=folder_in, current_user=current_user
    )


@router.delete("/{folder_id}")
async def delete_folder(
    *,
    db: AsyncSession = Depends(deps.get_db),
    folder_id: int,
    current_user: User = Depends(deps.get_current_user),
    force: bool = False,  # 是否强制删除（物理删除）
    video_folder_service: VideoFolderService = Depends(get_video_folder_service),
) -> dict:
    """删除文件夹（使用服务层）"""
    await video_folder_service.delete_folder(
        db=db, folder_id=folder_id, current_user=current_user, force=force
    )
    message = "Folder permanently deleted" if force else "Folder soft deleted successfully"
    return {"message": message}
