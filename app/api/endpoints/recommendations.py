"""推荐系统API接口"""

from datetime import datetime, timedelta
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api import deps
from app.api.permission_deps import require_permission
from app.core.permission_system import (
    Action,
    PermissionChecker,
    ResourceType,
    Scope,
)
from app.core.permission_system import (
    Permission as PermissionObject,
)
from app.services.recommendation_evaluation_service import RecommendationEvaluationService
from app.services.service_factory import (
    get_recommendation_evaluation_service,
)

router = APIRouter()


@router.get("/stats", response_model=schemas.RecommendationStats)
async def get_recommendation_stats(
    *,
    db: AsyncSession = Depends(deps.get_db),
    days: int = Query(7, ge=1, le=30, description="统计天数"),
    current_user: models.User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.SYSTEM, action=Action.READ, scope=Scope.ALL)
        )
    ),
) -> Any:
    """获取推荐统计信息（仅管理员可访问）"""
    # 获取推荐统计数据
    total_recommendations = (
        db.query(models.RecommendationLog)
        .filter(models.RecommendationLog.created_at >= datetime.utcnow() - timedelta(days=days))
        .count()
    )

    # 计算总体点击率
    overall_ctr = await crud.recommendation_log.get_click_through_rate(db, days=days)

    # 按算法类型统计性能
    algorithm_performance = {}
    for algorithm in ["collaborative", "content_based", "hot", "hybrid"]:
        ctr = await crud.recommendation_log.get_click_through_rate(
            db, algorithm_type=algorithm, days=days
        )
        algorithm_performance[algorithm] = {"click_through_rate": ctr}

    # 统计热门内容类型
    popular_content_types = {
        "article": 0,
        "video": 0,
    }

    # 这里可以添加更详细的统计逻辑

    return schemas.RecommendationStats(
        total_recommendations=total_recommendations,
        click_through_rate=overall_ctr,
        algorithm_performance=algorithm_performance,
        popular_content_types=popular_content_types,
    )


@router.get("/evaluation/report", response_model=dict)
async def get_evaluation_report(
    *,
    db: AsyncSession = Depends(deps.get_db),
    days: int = Query(7, ge=1, le=30, description="评估天数"),
    current_user: models.User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.SYSTEM, action=Action.READ, scope=Scope.ALL)
        )
    ),
    recommendation_evaluation_service: RecommendationEvaluationService = Depends(
        get_recommendation_evaluation_service
    ),
) -> Any:
    """获取推荐效果评估报告（仅管理员可访问）"""
    # 生成综合评估报告
    report = await recommendation_evaluation_service.generate_comprehensive_report(db, days=days)

    return report


@router.get("/evaluation/algorithm/{algorithm_type}", response_model=dict)
async def get_algorithm_evaluation(
    *,
    db: AsyncSession = Depends(deps.get_db),
    algorithm_type: str,
    days: int = Query(7, ge=1, le=30, description="评估天数"),
    current_user: models.User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.SYSTEM, action=Action.READ, scope=Scope.ALL)
        )
    ),
    recommendation_evaluation_service: RecommendationEvaluationService = Depends(
        get_recommendation_evaluation_service
    ),
) -> Any:
    """获取特定算法的评估结果（仅管理员可访问）"""
    # 验证算法类型
    if algorithm_type not in ["collaborative", "content_based", "hot", "hybrid"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的算法类型",
        )

    # 计算各项指标
    ctr_data = await recommendation_evaluation_service.calculate_click_through_rate(
        db, algorithm_type=algorithm_type, days=days
    )

    diversity_data = await recommendation_evaluation_service.calculate_diversity_score(
        db, algorithm_type=algorithm_type, days=days
    )

    coverage_data = await recommendation_evaluation_service.calculate_coverage_score(
        db, algorithm_type=algorithm_type, days=days
    )

    return {
        "algorithm_type": algorithm_type,
        "evaluation_period": f"{days} days",
        "click_through_rate": ctr_data,
        "diversity": diversity_data,
        "coverage": coverage_data,
        "generated_at": datetime.utcnow().isoformat(),
    }


@router.get("/evaluation/user/{user_id}", response_model=dict)
async def get_user_evaluation(
    *,
    db: AsyncSession = Depends(deps.get_db),
    user_id: int,
    algorithm_type: str = Query("hybrid", description="算法类型"),
    days: int = Query(7, ge=1, le=30, description="评估天数"),
    current_user: models.User = Depends(deps.get_current_active_user),
    recommendation_evaluation_service: RecommendationEvaluationService = Depends(
        get_recommendation_evaluation_service
    ),
) -> Any:
    """获取特定用户的推荐效果评估（仅管理员或用户本人可访问）"""

    # 检查权限：管理员或用户本人
    is_admin = await PermissionChecker.check_permission(
        db,
        current_user,
        PermissionObject(resource=ResourceType.SYSTEM, action=Action.MANAGE, scope=Scope.ALL),
    )
    if not (is_admin or current_user.id == user_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此用户的评估数据",
        )

    # 计算用户相关指标
    precision_recall = await recommendation_evaluation_service.calculate_precision_recall(
        db, user_id=user_id, algorithm_type=algorithm_type, days=days
    )

    novelty_data = await recommendation_evaluation_service.calculate_novelty_score(
        db, user_id=user_id, algorithm_type=algorithm_type, days=days
    )

    return {
        "user_id": user_id,
        "algorithm_type": algorithm_type,
        "evaluation_period": f"{days} days",
        "precision_recall": precision_recall,
        "novelty": novelty_data,
        "generated_at": datetime.utcnow().isoformat(),
    }
