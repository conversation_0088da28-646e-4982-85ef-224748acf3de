from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api import deps
from app.api.permission_deps import require_permission
from app.core.permission_system import Action, ResourceType, Scope
from app.core.permission_system import Permission as PermissionObject
from app.services.content_stats_service import ContentStatsService
from app.services.service_factory import get_content_stats_service

router = APIRouter()


@router.post("/toggle", response_model=schemas.LikeStatus)
async def toggle_like(
    *,
    db: AsyncSession = Depends(deps.get_db),
    like_data: schemas.LikeToggle,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """切换点赞状态"""
    # 验证内容是否存在
    if like_data.content_type == "article":
        content = await crud.article.get(db, id=like_data.content_id)
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文章不存在",
            )
    elif like_data.content_type == "video":
        content = await crud.video.get(db, id=like_data.content_id)
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="视频不存在",
            )
    elif like_data.content_type == "scratch":
        content = await crud.scratch_product.get(db, id=like_data.content_id)
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Scratch项目不存在",
            )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的内容类型",
        )

    # 切换点赞状态
    if like_data.content_type == "scratch":
        # 对于Scratch项目，使用专门的服务
        from app.services.scratch_stats_service import ScratchStatsService

        scratch_service = ScratchStatsService()
        _, is_liked = await scratch_service.toggle_like(
            db=db,
            user_id=current_user.id,
            content_id=like_data.content_id,
        )
    else:
        # 对于其他内容类型，使用通用的CRUD方法
        _, is_liked = await crud.like.toggle_like(
            db,
            user_id=current_user.id,
            content_type=like_data.content_type,
            content_id=like_data.content_id,
            commit=False,  # 明确指出不由CRUD方法提交
        )

    # 在API层统一提交事务，将点赞操作和事件创建包含在同一个事务中
    await db.commit()

    # 统计数据将由后台任务异步更新，这里直接返回当前的点赞状态
    # 为了获取最新的点赞总数，我们可以直接查询数据库
    if like_data.content_type == "scratch":
        # 对于Scratch项目，从scratch_products表获取点赞数量
        scratch_project = await crud.scratch_product.get(db, id=like_data.content_id)
        like_count = scratch_project.like_count if scratch_project else 0
    else:
        like_count = await crud.like.get_content_like_count(
            db, content_type=like_data.content_type, content_id=like_data.content_id
        )

    return schemas.LikeStatus(
        content_type=like_data.content_type,
        content_id=like_data.content_id,
        is_liked=is_liked,
        like_count=like_count,
    )


@router.get("/stats", response_model=schemas.LikeStats)
async def get_like_stats(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_type: str | None = Query(None, description="内容类型筛选"),
    current_user: models.User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.SYSTEM, action=Action.READ, scope=Scope.ALL)
        )
    ),
) -> Any:
    """获取点赞统计信息（仅管理员）"""
    stats = await crud.like.get_like_stats(db, content_type=content_type)
    return schemas.LikeStats(**stats)


@router.post("/batch-status", response_model=list[schemas.ContentLikeInfo])
async def get_batch_like_status(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_items: list[schemas.LikeBase],
    current_user: models.User = Depends(deps.get_current_active_user),
    content_stats_service: ContentStatsService = Depends(get_content_stats_service),
) -> Any:
    """批量获取内容点赞信息"""
    if not content_items:
        return []

    items = [(item.content_type, item.content_id) for item in content_items]

    # 从新服务批量获取统计信息
    stats_dict = await content_stats_service.batch_get_stats(
        db, content_items=items, user_id=current_user.id
    )

    # 构建响应
    result = []
    for content_type, content_id in items:
        stats = stats_dict.get((content_type, content_id), {})
        result.append(
            schemas.ContentLikeInfo(
                content_type=content_type,
                content_id=content_id,
                like_count=stats.get("like_count", 0),
                is_liked_by_user=stats.get("is_liked_by_user", False),
            )
        )

    return result
