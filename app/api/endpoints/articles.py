import logging
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Path, Query, Request, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from app import crud, models, schemas
from app.api import deps
from app.api.permission_deps import require_permission
from app.core.limiter import limiter
from app.core.pagination import (
    CursorPaginationParams,
    CursorPaginationResponse,
)
from app.core.permission_system import ResourceType
from app.core.permissions import Permissions
from app.services.article_aggregation_service import ArticleAggregationService
from app.services.article_cache_service import ArticleCacheService
from app.services.content_service import ContentService
from app.services.recommendation_cache_service import RecommendationCacheService
from app.services.recommendation_service import RecommendationService
from app.services.service_factory import (
    get_article_aggregation_service,
    get_article_cache_service,
    get_article_content_service,
    get_recommendation_cache_service,
    get_recommendation_service,
    get_user_stats_cache_service,
)
from app.services.user_stats_service import UserStatsCacheService
from app.utils.pagination_adapter import PaginationAdapter

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter()


# ==================== 推荐系统相关API ====================


@router.get("/recommendations", response_model=CursorPaginationResponse[schemas.ArticleOut])
async def get_article_recommendations(
    *,
    db: AsyncSession = Depends(deps.get_db),
    pagination: CursorPaginationParams = Depends(),
    current_user: models.User = Depends(deps.get_current_active_user),
    recommendation_service: RecommendationService = Depends(get_recommendation_service),
    article_aggregation_service: ArticleAggregationService = Depends(
        get_article_aggregation_service
    ),
) -> Any:
    """获取分页的文章推荐列表"""
    # 1. 将游标转换为页码
    page = PaginationAdapter.cursor_to_page(pagination.cursor)

    # 2. 从推荐服务获取个性化推荐
    recommendation_items = await recommendation_service.get_paginated_content_recommendations(
        user_id=current_user.id,
        content_type="article",
        page=page,
        limit=pagination.size,
    )

    if not recommendation_items:
        return PaginationAdapter.empty_cursor_response()

    # 3. 聚合数据
    article_ids = [item.content_id for item in recommendation_items]
    aggregated_articles = await article_aggregation_service.get_aggregated_articles_by_ids(
        db=db,
        article_ids=article_ids,
        current_user=current_user,
    )

    # 4. 保持推荐顺序
    articles_map = {article.id: article for article in aggregated_articles}
    sorted_articles = [
        articles_map[article_id] for article_id in article_ids if article_id in articles_map
    ]

    # 5. 使用适配器构建游标分页响应
    # 注意：个性化推荐使用页码分页，判断是否有更多数据
    has_more_items = len(recommendation_items) == pagination.size
    return PaginationAdapter.adapt_page_based_to_cursor(
        items=sorted_articles,
        pagination=pagination,
        has_more_items=has_more_items,
    )


@router.get("/{article_id}/similar", response_model=list[schemas.ArticleOut])
async def get_similar_articles(
    article_id: int,
    *,
    db: AsyncSession = Depends(deps.get_db),
    limit: int = Query(10, ge=1, le=20, description="相似文章数量"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    recommendation_service: RecommendationService = Depends(get_recommendation_service),
    article_cache_service: ArticleCacheService = Depends(get_article_cache_service),
    article_aggregation_service: ArticleAggregationService = Depends(
        get_article_aggregation_service
    ),
) -> Any:
    """获取相似文章列表"""
    # 1. 验证文章是否存在
    article = await article_cache_service.get_entity(db, entity_id=article_id)
    if not article:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="文章不存在")

    # 2. 获取相似内容
    recommendation_items = await recommendation_service.get_similar_content(
        content_type="article", content_id=article_id, limit=limit
    )

    if not recommendation_items:
        return []

    # 3. 聚合数据
    article_ids = [item.content_id for item in recommendation_items]
    aggregated_articles = await article_aggregation_service.get_aggregated_articles_by_ids(
        db=db,
        article_ids=article_ids,
        current_user=current_user,
    )

    # 4. 保持推荐顺序
    articles_map = {article.id: article for article in aggregated_articles}
    sorted_articles = [
        articles_map[article_id] for article_id in article_ids if article_id in articles_map
    ]

    return sorted_articles


@router.get("/hot", response_model=CursorPaginationResponse[schemas.ArticleOut])
async def get_hot_articles(
    *,
    db: AsyncSession = Depends(deps.get_db),
    pagination: CursorPaginationParams = Depends(),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    recommendation_service: RecommendationService = Depends(get_recommendation_service),
    article_aggregation_service: ArticleAggregationService = Depends(
        get_article_aggregation_service
    ),
) -> Any:
    """
    获取热门文章列表（分页）
    """
    # 调试信息：记录请求参数
    logger.info(f"🔥 热门文章请求 - cursor: {pagination.cursor}, size: {pagination.size}")

    # 1. 将游标转换为偏移量
    offset = PaginationAdapter.cursor_to_offset(pagination.cursor)
    logger.info(f"📍 偏移量转换 - offset: {offset}")

    # 2. 从推荐服务获取热门内容
    paginated_result = await recommendation_service.get_hot_content(
        content_type="article", limit=pagination.size, offset=offset
    )

    # 调试信息：推荐服务返回结果
    logger.info(
        f"🎯 推荐服务返回 - items数量: {len(paginated_result.items)}, has_next: {paginated_result.has_next}"
    )
    if paginated_result.items:
        item_ids = [item.content_id for item in paginated_result.items]
        logger.info(f"📝 推荐文章ID列表: {item_ids}")
    else:
        logger.warning("⚠️ 推荐服务返回空结果，可能原因：Redis热门推荐池为空")

    if not paginated_result.items:
        logger.info("🚫 返回空响应")
        return PaginationAdapter.empty_cursor_response()

    # 3. 聚合数据
    article_ids = [item.content_id for item in paginated_result.items]
    aggregated_articles = await article_aggregation_service.get_aggregated_articles_by_ids(
        db=db,
        article_ids=article_ids,
        current_user=current_user,
    )

    # 调试信息：聚合服务返回结果
    logger.info(f"🔗 聚合服务返回 - 文章数量: {len(aggregated_articles)}")
    if len(aggregated_articles) != len(article_ids):
        missing_ids = set(article_ids) - {article.id for article in aggregated_articles}
        logger.warning(f"⚠️ 部分文章未找到，缺失ID: {missing_ids}")

    # 4. 保持推荐顺序
    articles_map = {article.id: article for article in aggregated_articles}
    sorted_articles = [
        articles_map[article_id] for article_id in article_ids if article_id in articles_map
    ]

    # 调试信息：最终结果
    logger.info(f"✅ 最终返回 - 文章数量: {len(sorted_articles)}")

    # 5. 使用适配器构建游标分页响应
    return PaginationAdapter.adapt_recommendation_to_cursor(
        items=sorted_articles,
        pagination=pagination,
        recommendation_result=paginated_result,
    )


@router.post(
    "/{article_id}/recommendation-feedback",
    response_model=dict,
    summary="提交文章推荐反馈",
    description="用户对推荐的文章提供反馈，用于优化推荐算法",
)
async def submit_article_recommendation_feedback(
    *,
    db: AsyncSession = Depends(deps.get_db),
    article_id: int,
    feedback: schemas.RecommendationFeedback,
    current_user: models.User = Depends(deps.get_current_active_user),
    recommendation_service: RecommendationService = Depends(get_recommendation_service),
    article_cache_service: ArticleCacheService = Depends(get_article_cache_service),
    recommendation_cache_service: RecommendationCacheService = Depends(
        get_recommendation_cache_service
    ),
) -> Any:
    """提交文章推荐反馈

    **功能特点**：
    - 收集用户对推荐文章的反馈
    - 支持多种反馈类型：喜欢、不喜欢、不感兴趣、举报
    - 用于优化推荐算法
    - 需要用户登录

    **反馈类型说明**：
    - `like`: 喜欢这个推荐
    - `dislike`: 不喜欢这个推荐
    - `not_interested`: 对此类内容不感兴趣
    - `report`: 举报不当内容
    """

    # 验证文章是否存在
    article = await article_cache_service.get_entity(db, entity_id=article_id)
    if not article:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="文章不存在")

    # 验证推荐记录是否存在且属于当前用户
    recommendation_log = await crud.recommendation_log.get(db, id=feedback.recommendation_log_id)
    if not recommendation_log:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="推荐记录不存在")

    if recommendation_log.user_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权限操作此推荐记录")

    # 验证推荐记录中是否包含该文章
    if feedback.content_id and feedback.content_id != article_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="反馈的文章ID与URL中的文章ID不匹配"
        )

    # 设置反馈的内容ID为当前文章ID
    feedback.content_id = article_id

    # 记录反馈
    await recommendation_service.record_feedback(
        db=db,
        user_id=current_user.id,
        recommendation_log_id=feedback.recommendation_log_id,
        feedback_type=feedback.feedback_type,
        content_id=article_id,
        reason=feedback.reason,
    )

    # 根据反馈类型更新用户画像
    if feedback.feedback_type in ["dislike", "not_interested"]:
        # 降低相关标签和分类的权重
        await recommendation_service.update_user_profile_from_feedback(
            db=db,
            user_id=current_user.id,
            content_type="article",
            content_id=article_id,
            feedback_type=feedback.feedback_type,
        )

    # 清除相关缓存
    await recommendation_cache_service.clear_user_cache(current_user.id)

    return {"message": "反馈提交成功", "status": "success"}


@router.post("/create")
async def create_article(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(require_permission(Permissions.ARTICLE_CREATE_OWN)),
    article_cache_service: ArticleCacheService = Depends(get_article_cache_service),
    user_stats_service: UserStatsCacheService = Depends(get_user_stats_cache_service),
) -> dict:
    """
    创建文章 - 统一响应格式

    权限检查已在依赖中完成，无需重复检查。
    创建成功后返回文章ID，内容可在后续更新中丰富。
    """

    # 创建文章逻辑保持不变
    article = await crud.article.create(
        db=db,
        obj_in={
            "author_id": current_user.id,
            "title": "未命名文章",
            "content": "",
        },
    )

    # 更新用户文章数
    await crud.user_stats.increment(db=db, user_id=current_user.id, field="article_count", value=1)

    # 使用户统计缓存失效
    await user_stats_service.invalidate_user_stats(current_user.id)

    # 记录文章创建日志
    logger.info(
        f"User {current_user.id} created article {article.id}",
        extra={
            "user_id": current_user.id,
            "article_id": article.id,
            "action": "create_article",
        },
    )

    return {"article_id": article.id}


@router.get(
    "/category/{category_id}",
    response_model=CursorPaginationResponse[schemas.ArticleOut],
    summary="获取指定分类下的文章列表",
)
async def get_category_articles(
    *,
    db: AsyncSession = Depends(deps.get_db),
    category_id: int = Path(..., ge=0, description="分类ID（使用0表示获取所有分类的文章）"),
    pagination: CursorPaginationParams = Depends(),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    article_aggregation_service: ArticleAggregationService = Depends(
        get_article_aggregation_service
    ),
) -> CursorPaginationResponse[schemas.ArticleOut]:
    """
    获取指定分类下的文章列表，并进行分页。

    - **权限**: 公开接口，游客和登录用户均可访问。
    - **过滤**: 仅返回已发布、已审核且未删除的文章。
    - **分页**: 支持高效的游标分页。
    - **特殊说明**: 当category_id=0时，返回所有已发布和已审核的文章（不区分分类）
    """
    # 特殊处理：category_id为0时，获取所有已发布且已审核的文章
    if int(category_id) == 0:
        # 1. 从 CRUD 层获取所有已发布文章的分页数据
        paginated_result = await crud.article.get_paginated_published_articles(
            db=db,
            params=pagination,
            include_total=True,
        )
    else:
        # 检查分类是否存在
        category = await crud.category.get(db=db, id=category_id)
        if not category:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="分类不存在")

        # 1. 从 CRUD 层获取分页后的基础文章数据
        paginated_result = await crud.article.get_paginated_category_articles(
            db=db,
            category_id=category_id,
            params=pagination,
        )

    if not paginated_result.items:
        return CursorPaginationResponse(
            items=[],
            total_count=0,
            has_next=False,
            has_previous=False,
            next_cursor=None,
            previous_cursor=None,
        )

    # 2. 使用聚合服务获取完整的文章信息
    article_ids = [article.id for article in paginated_result.items]
    aggregated_articles = await article_aggregation_service.get_aggregated_articles_by_ids(
        db=db,
        article_ids=article_ids,
        current_user=current_user,
    )

    # 3. 构建并返回最终的分页响应
    return CursorPaginationResponse(
        items=aggregated_articles,
        total_count=paginated_result.total_count,
        has_next=paginated_result.has_next,
        has_previous=bool(pagination.cursor),
        next_cursor=paginated_result.next_cursor,
        previous_cursor=pagination.cursor,
    )


@router.get(
    "/{article_id}", response_model=schemas.ArticleOut, summary="获取文章详情（聚合服务版）"
)
@limiter.limit("100/minute")
async def get_article(
    *,
    request: Request,
    db: AsyncSession = Depends(deps.get_db),
    article_id: int = Path(..., description="文章ID"),
    current_user: models.User | None = Depends(
        require_permission(
            Permissions.ARTICLE_READ_PUBLIC,
            resource_type=ResourceType.ARTICLE,
            resource_id_key="article_id",
            optional_user=True,
        )
    ),
    article_aggregation_service: ArticleAggregationService = Depends(
        get_article_aggregation_service
    ),
) -> schemas.ArticleOut:
    """
    获取文章详情 - 聚合服务版
    """
    article = await article_aggregation_service.get_aggregated_article(
        db=db,
        article_id=article_id,
        current_user=current_user,
        include_content=True,  # 获取文章详情默认返回正文
    )
    if not article:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="文章不存在")

    return article


@router.put("/{article_id}", response_model=schemas.ArticleOut)
async def update_article(
    *,
    db: AsyncSession = Depends(deps.get_db),
    article_id: int = Path(..., description="文章ID"),
    article_in: schemas.ArticleUpdate,
    current_user: models.User = Depends(
        require_permission(
            Permissions.ARTICLE_UPDATE_OWN,
            resource_type=ResourceType.ARTICLE,
            resource_id_key="article_id",
        )
    ),
    article_cache_service: ArticleCacheService = Depends(get_article_cache_service),
    article_service: ContentService = Depends(get_article_content_service),
    article_aggregation_service: ArticleAggregationService = Depends(
        get_article_aggregation_service
    ),
) -> Any:
    """更新文章
    - 如果文章从未发布变为发布，需要创建审核记录
    - 如果已发布文章的内容有更新，需要重新审核
    - 未发布的文章（草稿）可以自由修改，不需要审核
    """
    # 对于更新操作，我们仍然需要从数据库获取完整的ORM对象
    article = await crud.article.get(
        db=db,
        id=article_id,
        options=[joinedload(models.Article.tags), joinedload(models.Article.category)],
    )
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在",
        )

    article_in_dict = article_in.dict(exclude_unset=True)

    # 处理发布状态变更
    if "is_published" in article_in_dict:
        article = await article_service.handle_publish_status(
            db=db, content=article, is_published=article_in.is_published
        )

    # 处理内容更新
    has_content_update = bool(article_in.title or article_in.content)
    article = await article_service.handle_content_update(
        db=db, content=article, has_content_update=has_content_update
    )

    # 处理标签更新
    if article_in.tags is not None:
        article = await article_service.handle_tags_update(
            db=db, content=article, tags=article_in.tags
        )
        # 标签已由服务处理，从字典中移除以避免在基础CRUD中重复处理
        del article_in_dict["tags"]

    # 更新文章
    article = await crud.article.update(db=db, db_obj=article, obj_in=article_in_dict)

    # 使文章缓存失效
    await article_cache_service.invalidate_entity(article_id)

    # 使用聚合服务获取更新后的文章数据，避免序列化问题
    updated_article = await article_aggregation_service.get_aggregated_article(
        db=db,
        article_id=article_id,
        current_user=current_user,
        include_content=True,
    )

    # 记录文章更新日志
    logger.info(
        f"User {current_user.id} updated article {article_id}",
        extra={
            "user_id": current_user.id,
            "article_id": article_id,
            "action": "update_article",
            "updated_fields": list(article_in_dict.keys()),
        },
    )

    return updated_article


@router.delete("/{article_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_article(
    *,
    db: AsyncSession = Depends(deps.get_db),
    article_id: int,
    current_user: models.User = Depends(
        require_permission(
            Permissions.ARTICLE_DELETE_OWN,
            resource_type=ResourceType.ARTICLE,
            resource_id_key="article_id",
        )
    ),
    article_cache_service: ArticleCacheService = Depends(get_article_cache_service),
    recommendation_cache_service: RecommendationCacheService = Depends(
        get_recommendation_cache_service
    ),
    user_stats_service: UserStatsCacheService = Depends(get_user_stats_cache_service),
) -> None:
    """删除文章"""
    article = await article_cache_service.get_entity(db, entity_id=article_id)
    if not article:
        # 在权限检查前，资源可能已被删除，这是正常的
        return

    # 删除关联的审核记录
    review = await crud.review.get_by_content(db, content_type="article", content_id=article.id)
    if review:
        await crud.review.remove(db=db, id=review.id)

    # 立即删除文章缓存
    await article_cache_service.invalidate_entity(article_id)

    # 立即删除推荐缓存
    await recommendation_cache_service.invalidate_content_cache("article", article_id)
    await recommendation_cache_service.invalidate_user_cache(article.author_id)

    # 延迟异步删除文章缓存（延迟双删策略）
    from app.tasks.cache_tasks import task_invalidate_article_cache

    task_invalidate_article_cache.apply_async(args=[article_id], countdown=1)

    await crud.article.remove(db=db, id=article_id)

    # 更新用户文章数
    await crud.user_stats.increment(
        db=db, user_id=article.author_id, field="article_count", value=-1
    )

    # 使用户统计缓存失效
    await user_stats_service.invalidate_user_stats(article.author_id)


@router.get(
    "/{user_id}/articles",
    response_model=CursorPaginationResponse[schemas.ArticleOut],
    summary="获取指定用户发布的文章列表",
)
async def get_user_articles(
    user_id: int,
    *,
    db: AsyncSession = Depends(deps.get_db),
    pagination: CursorPaginationParams = Depends(),
    status_filter: schemas.ArticleStatus | None = Query(
        None,
        alias="status",
        description="文章状态筛选（仅作者本人或管理员可用）",
    ),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    article_aggregation_service: ArticleAggregationService = Depends(
        get_article_aggregation_service
    ),
) -> CursorPaginationResponse[schemas.ArticleOut]:
    """
    获取指定用户**发布**的文章列表。
    - 普通用户只能查看已发布且审核通过的文章。
    - 作者本人或管理员可以通过 `status_filter` 查看草稿、待审核等状态的文章。
    """
    # 检查目标用户是否存在
    target_user = await crud.user.get(db=db, id=user_id)
    if not target_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

    if status_filter is None:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="状态筛选不能为空")

    is_author = current_user and current_user.id == user_id
    is_admin = current_user and current_user.is_superuser
    can_view_all_statuses = is_author or is_admin

    # 权限检查
    if (
        status_filter
        in [
            schemas.ArticleStatus.DRAFT,
            schemas.ArticleStatus.PUBLISHED_PENDING,
            schemas.ArticleStatus.PUBLISHED_REJECTED,
        ]
        and not can_view_all_statuses
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="没有权限查看草稿或待审核文章"
        )

    # 构建筛选条件
    filters = {"author_id": user_id, "status": status_filter}

    # 1. 直接调用 CRUD 层进行分页查询
    paginated_result = await crud.article.get_paginated_articles(
        db=db,
        params=pagination,
        filters=filters,
        can_view_all=can_view_all_statuses,
        include_total=False,
    )
    print("paginated_result", paginated_result)

    if not paginated_result.items:
        return CursorPaginationResponse(
            items=[],
            total_count=0,
            has_next=False,
            has_previous=False,
            next_cursor=None,
            previous_cursor=None,
        )

    # 2. 将分页后的文章列表交由服务层进行数据组装
    article_ids = [article.id for article in paginated_result.items]
    print(f"DEBUG: 传递给聚合服务的article_ids: {article_ids}")

    aggregated_articles = await article_aggregation_service.get_aggregated_articles_by_ids(
        db=db,
        article_ids=article_ids,
        current_user=current_user,
        include_review=can_view_all_statuses,
        include_content=False,
    )

    print(f"DEBUG: 聚合服务返回的文章数量: {len(aggregated_articles)}")

    # 保持分页顺序
    articles_map = {article.id: article for article in aggregated_articles}
    sorted_articles = [
        articles_map[article_id] for article_id in article_ids if article_id in articles_map
    ]

    # 3. 构建并返回最终的分页响应
    return CursorPaginationResponse(
        items=sorted_articles,
        total_count=paginated_result.total_count,
        has_next=paginated_result.has_next,
        has_previous=bool(pagination.cursor),
        next_cursor=paginated_result.next_cursor,
        previous_cursor=pagination.cursor,
    )


@router.post(
    "/multiple",
    response_model=list[schemas.ArticleOut],
    summary="批量获取文章详情（聚合服务版）",
    description="支持批量获取文章详情，优化了查询效率。",
)
async def get_multiple_articles(
    *,
    db: AsyncSession = Depends(deps.get_db),
    data: schemas.MultipleData,
    preserve_order: bool = Query(True, description="是否保持传入ID的顺序"),
    include_content: bool = Query(False, description="是否包含文章内容字段，默认不包含以提升性能"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    article_aggregation_service: ArticleAggregationService = Depends(
        get_article_aggregation_service
    ),
) -> Any:
    """批量获取文章详情（优化版）

    **功能特点**：
    - 用于在其他内容中嵌入文章信息，如评论、回复等
    - 根据参数控制返回的数据内容，避免不必要的数据传输
    - 自动过滤用户无权限访问的文章
    - 支持批量预加载，避免N+1查询问题
    - 支持保持传入ID的顺序
    - 默认不包含文章内容字段，通过参数控制是否包含

    **权限控制**：
    - 自动过滤用户无权限访问的文章
    - 对于未登录用户，只返回已发布且已审核的公开文章
    - 对于登录用户，可以访问自己的文章和公开文章

    **性能优化**：
    - 使用selectinload预加载关联数据
    - 批量权限检查
    - 可选的数据字段，减少不必要的数据传输
    """
    # 1. 参数验证
    if not data.articleIds:
        return []

    if len(data.articleIds) > 100:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="一次最多只能获取100篇文章"
        )

    # 2. 去重并保持顺序
    unique_ids = list(dict.fromkeys(data.articleIds))  # 去重但保持顺序

    # 3. 过滤用户无权限访问的文章
    # 注意：批量权限检查逻辑已移至聚合服务内部，以实现更好的性能和封装
    # accessible_articles = await article_permission_service.filter_accessible_articles(
    #     db=db, user=current_user, article_ids=unique_ids
    # )
    # if not accessible_articles:
    #     return []

    # 4. 批量获取聚合后的文章详情
    articles = await article_aggregation_service.get_aggregated_articles_by_ids(
        db,
        article_ids=unique_ids,  # 直接传入所有ID，由聚合服务处理权限
        current_user=current_user,
        include_content=include_content,
    )

    # 5. 如果需要保持顺序，按照原始ID顺序重新排序
    if preserve_order and len(articles) > 1:
        items_dict = {item.id: item for item in articles}
        ordered_items = [items_dict[id] for id in unique_ids if id in items_dict]
        return ordered_items

    return articles
