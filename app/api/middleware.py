import json
from typing import Any

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.logging import logger
from app.core.response_wrapper import success_response


class ResponseFormatterMiddleware(BaseHTTPMiddleware):
    """统一响应格式化中间件"""

    def _is_formatted_response(self, content: dict) -> bool:
        """检测是否已经是格式化的响应"""
        if not isinstance(content, dict):
            return False

        # 检查是否包含新的APIResponse格式关键字段
        required_fields = {"success", "code", "message", "timestamp"}
        content_fields = set(content.keys())

        return len(content_fields.intersection(required_fields)) >= 3

    async def dispatch(self, request: Request, call_next) -> Any:
        response = await call_next(request)

        # 对于OPTIONS请求（CORS预检）和204状态码，直接返回原始响应
        if request.method == "OPTIONS" or response.status_code == 204:
            return response

        # 只对API v1路径的成功响应进行格式化
        if (
            request.url.path.startswith("/api/v1")
            and response.status_code < 400
            and response.status_code != 204
            and request.url.path not in ["/api/v1/health", "/api/v1/wechat/callback"]
        ):
            try:
                body = b""
                async for chunk in response.body_iterator:
                    body += chunk
                content = json.loads(body.decode("utf-8")) if body else {}

                # 检查是否已经是格式化的响应
                if self._is_formatted_response(content):
                    # 已经是格式化响应，直接返回
                    formatted_content = content
                else:
                    # 普通数据，使用新的格式进行包装
                    formatted_content = success_response(content)

                response_body = json.dumps(
                    formatted_content, ensure_ascii=False, default=str
                ).encode("utf-8")

                # 创建新的headers字典，排除Content-Length让FastAPI自动计算
                new_headers = {
                    k: v for k, v in response.headers.items() if k.lower() != "content-length"
                }
                return Response(
                    content=response_body,
                    status_code=response.status_code,
                    headers=new_headers,
                    media_type=response.media_type or "application/json",
                )
            except Exception as e:
                logger.error(f"响应格式化失败: {str(e)}", exc_info=True)
                # 异常情况下也要排除Content-Length头部
                new_headers = {
                    k: v for k, v in response.headers.items() if k.lower() != "content-length"
                }
                return Response(
                    content=json.dumps(
                        success_response({"error": "响应格式化失败"}), ensure_ascii=False
                    ).encode("utf-8"),
                    status_code=500,
                    headers=new_headers,
                    media_type="application/json",
                )
        return response
