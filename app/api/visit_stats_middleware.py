"""访问统计中间件"""

import re
from typing import Any

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.logging import logger
from app.services.service_factory import get_content_stats_service


class VisitStatsMiddleware(BaseHTTPMiddleware):
    """访问统计中间件

    自动记录API访问统计，支持以下路径格式：
    - /api/v1/videos/{video_id} - 视频详情
    - /api/v1/articles/{article_id} - 文章详情
    - /api/v1/videos/{video_id}/with-stats - 带统计信息的视频详情
    - /api/v1/articles/{article_id}/with-stats - 带统计信息的文章详情
    """

    def __init__(self, app):
        super().__init__(app)
        self.content_stats_service = get_content_stats_service()

    # 定义路径匹配模式
    VIDEO_PATTERN = r"/api/v1/videos/(\d+)(?:/with-stats)?$"
    ARTICLE_PATTERN = r"/api/v1/articles/(\d+)(?:/with-stats)?$"

    async def dispatch(self, request: Request, call_next) -> Any:
        # 只处理GET请求
        if request.method != "GET":
            return await call_next(request)

        path = request.url.path
        content_type = None
        content_id = None

        # 匹配视频详情路径
        video_match = re.match(self.VIDEO_PATTERN, path)
        if video_match:
            content_type = "video"
            content_id = int(video_match.group(1))

        # 匹配文章详情路径
        article_match = re.match(self.ARTICLE_PATTERN, path)
        if article_match:
            content_type = "article"
            content_id = int(article_match.group(1))

        # 如果匹配到内容，增加访问次数
        if content_type and content_id:
            try:
                # 异步增加访问次数，不等待结果
                await self.content_stats_service.increment_visit_count(content_type, content_id)
                logger.info(f"增加{content_type} ID:{content_id}的访问次数")
            except Exception as e:
                logger.error(f"增加访问次数失败: {e}")

        # 继续处理请求
        response = await call_next(request)
        return response
