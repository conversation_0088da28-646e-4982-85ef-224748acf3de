import logging
import random

from app.config import settings
from app.db import redis
from app.schemas.recommendation import RecommendationItem

logger = logging.getLogger(__name__)


class RecommendationCacheService:
    """
    封装推荐系统所有与Redis缓存相关的操作。
    - 用户个人推荐队列 (Sorted Set)
    - 结果分页缓存 (String)
    """

    def __init__(self):
        self.cache_enabled = settings.RECOMMENDATION_CACHE_ENABLED
        self.user_queue_expire_seconds = settings.RECOMMENDATION_USER_QUEUE_EXPIRE_SECONDS
        self.result_cache_expire_seconds = settings.RECOMMENDATION_CACHE_EXPIRE_SECONDS
        self.null_cache_expire_seconds = settings.RECOMMENDATION_NULL_CACHE_EXPIRE_SECONDS
        self.cache_jitter_seconds = settings.RECOMMENDATION_CACHE_JITTER_SECONDS
        logger.info(
            f"RecommendationCacheService initialized. User queue TTL: {self.user_queue_expire_seconds}s, "
            f"Result cache TTL: {self.result_cache_expire_seconds}s"
        )

    # --- 用户个人推荐队列 (recs:user:{user_id}) ---

    def _get_user_queue_key(self, user_id: int) -> str:
        """获取用户个人推荐队列的Redis键"""
        return f"recs:user:{user_id}"

    async def get_user_recommendation_queue(
        self, user_id: int, page: int, limit: int
    ) -> list[tuple[str, float]]:
        """从用户的个人推荐队列（Sorted Set）中分页获取内容ID和分数"""
        if not self.cache_enabled:
            return []

        key = self._get_user_queue_key(user_id)
        start = (page - 1) * limit
        end = start + limit - 1

        try:
            # 使用 ZREVRANGE 获取成员和分数
            items_with_scores = await redis.redis_master_client.zrevrange(
                key, start, end, withscores=True
            )
            # zrevrange 返回的是 bytes, 需要解码
            return [(item.decode("utf-8"), score) for item, score in items_with_scores]
        except Exception as e:
            logger.error(
                f"Failed to get user recommendation queue from Redis for user {user_id}: {e}"
            )
            return []

    async def set_user_recommendation_queue(
        self, user_id: int, recommendations: list[RecommendationItem]
    ):
        """将完整的推荐列表（ID和分数）写入用户的个人推荐队列（Sorted Set）"""
        if not self.cache_enabled or not recommendations:
            return

        key = self._get_user_queue_key(user_id)
        # 构建 ZADD 的映射参数
        mapping = {f"{item.content_type}:{item.content_id}": item.score for item in recommendations}

        try:
            # 使用 pipeline 保证原子性
            async with redis.redis_master_client.pipeline() as pipe:
                # 先删除旧的 key，再添加新的
                pipe.delete(key)
                pipe.zadd(key, mapping)
                # 设置TTL
                expire_time = self.user_queue_expire_seconds + random.randint(
                    0, self.cache_jitter_seconds
                )
                pipe.expire(key, expire_time)
                await pipe.execute()
            logger.info(
                f"User recommendation queue for user {user_id} has been set. Items: {len(recommendations)}"
            )
        except Exception as e:
            logger.error(
                f"Failed to set user recommendation queue to Redis for user {user_id}: {e}"
            )

    async def check_user_recommendation_queue_exists(self, user_id: int) -> bool:
        """检查用户的个人推荐队列是否存在"""
        if not self.cache_enabled:
            return False

        key = self._get_user_queue_key(user_id)
        try:
            return await redis.redis_master_client.exists(key) > 0
        except Exception as e:
            logger.error(
                f"Failed to check existence of user recommendation queue for user {user_id}: {e}"
            )
            return False
