"""视频缓存服务模块"""

from typing import TYPE_CHECKING, Union

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app import crud, models, schemas
from app.config import settings
from app.services.base_cache_service import BaseCacheService
from app.utils.bloom_filters import video_bloom_filter

if TYPE_CHECKING:
    from sqlalchemy.ext.asyncio import AsyncSession
    from app.models.video import Video


class VideoCacheService(BaseCacheService["models.Video", "schemas.VideoBase", "crud.CRUDVideo"]):
    """
    视频缓存服务
    - 继承自 BaseCacheService，提供视频相关的特定配置
    - 重写数据库获取方法，使用 get_multi_by_ids_raw 避免状态过滤
    """

    def __init__(self):
        """初始化服务，配置视频特定的缓存参数"""
        super().__init__(
            crud_model=crud.video,
            schema_model=schemas.VideoBase,
            bloom_filter=video_bloom_filter,
            cache_key_prefix="video:base",
            entity_name="视频",
            cache_enabled_setting=settings.VIDEO_CACHE_ENABLED,
            cache_expire_seconds=settings.VIDEO_CACHE_EXPIRE_SECONDS,
        )

    async def _get_entity_from_db(
        self, db: AsyncSession, entity_id: int
    ) -> Union["Video", None]:
        """从数据库获取视频实体，预加载相关关系"""
        async with self.db_query_semaphore:
            # CRUDVideo.get 已经预加载了 tags 和 category
            return await self.crud.get(db=db, id=entity_id)

    async def _get_entities_batch_from_db(
        self, db: "AsyncSession", entity_ids: list[int]
    ) -> list["models.Video"]:
        """从数据库批量获取视频实体（使用原始方法避免状态过滤）"""
        async with self.db_query_semaphore:
            return await self.crud.get_multi_by_ids_raw(db=db, ids=entity_ids)


# 全局实例将在 service_factory 中创建和管理