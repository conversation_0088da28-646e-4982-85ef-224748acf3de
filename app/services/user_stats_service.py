import random

from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.config import settings
from app.core.logging import logger
from app.db import redis

NULL_CACHE_VALUE = "null"


class UserStatsCacheService:
    def _get_cache_key(self, user_id: int) -> str:
        return f"user:stats:{user_id}"

    async def get_user_stats(self, db: AsyncSession, user_id: int) -> schemas.UserStats | None:
        cache_key = self._get_cache_key(user_id)

        # 1. 尝试从缓存获取
        cached_data = await redis.get_key(cache_key)
        if cached_data:
            if cached_data == NULL_CACHE_VALUE:
                return None
            return schemas.UserStats.model_validate_json(cached_data)

        # 2. 缓存未命中，从数据库读取
        stats = await crud.user_stats.get(db, user_id)

        # 3. 回填缓存
        if stats:
            await self.set_user_stats(stats)
            return schemas.UserStats.from_orm(stats)
        else:
            await self.set_null_cache(user_id)
            return None

    async def set_user_stats(self, stats: models.UserStats):
        cache_key = self._get_cache_key(stats.user_id)
        stats_schema = schemas.UserStats.from_orm(stats)
        stats_json = stats_schema.model_dump_json()
        expire_time = settings.USER_CACHE_EXPIRE_SECONDS + random.randint(
            0, settings.CACHE_EXPIRE_JITTER_SECONDS
        )
        await redis.set_key(cache_key, stats_json, expire=expire_time)

    async def set_null_cache(self, user_id: int):
        cache_key = self._get_cache_key(user_id)
        await redis.set_key(cache_key, NULL_CACHE_VALUE, expire=settings.NULL_CACHE_EXPIRE_SECONDS)

    async def invalidate_user_stats(self, user_id: int):
        cache_key = self._get_cache_key(user_id)
        await redis.delete_key(cache_key)
        logger.info(f"用户统计缓存已删除: {cache_key}")


# 全局实例将在 service_factory 中创建和管理
