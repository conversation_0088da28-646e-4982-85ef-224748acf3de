"""短信验证码服务"""

import random
import string
from datetime import timedelta

from app.config import settings
from app.core.logging import logger
from app.core.sms_template import SmsTemplate
from app.db.redis import (
    delete_key,
    get_key,
    increment_key,
    set_key,
)
from app.exceptions.auth import (
    SMSInvalidCodeError,
    SMSVerificationError,
)
from app.schemas.sms import SMSIn
from app.services.interfaces.sms_service_interface import ISmsService
from app.tasks.auth import send_sms_task


class SmsService(ISmsService):
    """统一短信服务"""

    def __init__(self):
        self.expire_minutes = 5  # 验证码有效期5分钟
        self.max_attempts = 5  # 最大尝试次数
        self.logger = logger  # 添加logger属性

    def _generate_code(self, length: int = 6) -> str:
        """生成指定长度的随机验证码"""
        if settings.MODE == "dev":
            return "123456"
        return "".join(random.choices(string.digits, k=length))

    def _get_redis_key(self, phone: str, template_type: SmsTemplate) -> str:
        """生成Redis键名"""
        return f"sms:code:{template_type.name}:{phone}"

    def _get_attempts_key(self, phone: str) -> str:
        """生成验证尝试次数的Redis键名"""
        return f"sms:attempts:{phone}"

    async def _get_code_with_retry(
        self, redis_key: str, phone: str, template_type: SmsTemplate
    ) -> str:
        """获取验证码带重试机制

        Args:
            redis_key: Redis键名
            phone: 手机号
            template_type: 短信模板类型

        Returns:
            str: 验证码，如果不存在返回None
        """
        import asyncio

        from app.db.redis import get_key, get_redis_master

        # 首先尝试从slave读取
        stored_code = await get_key(redis_key)
        if stored_code:
            self.logger.info(f"从slave读取验证码成功 - 手机号: {phone}")
            return stored_code

        # 如果slave读取失败，等待短暂时间后重试
        self.logger.warning(f"从slave读取验证码失败，准备重试 - 手机号: {phone}")

        # 重试3次，每次间隔100ms
        for attempt in range(3):
            await asyncio.sleep(0.1 * (attempt + 1))  # 递增延迟：100ms, 200ms, 300ms
            stored_code = await get_key(redis_key)
            if stored_code:
                self.logger.info(f"重试{attempt + 1}次后从slave读取验证码成功 - 手机号: {phone}")
                return stored_code

        # 如果所有重试都失败，尝试从master读取
        self.logger.warning(f"slave重试失败，尝试从master读取 - 手机号: {phone}")
        try:
            redis_master = await get_redis_master()
            stored_code = await redis_master.get(redis_key)
            if stored_code:
                self.logger.info(f"从master读取验证码成功 - 手机号: {phone}")
                return stored_code
        except Exception as e:
            self.logger.error(f"从master读取验证码失败 - 手机号: {phone}, 错误: {e}")

        self.logger.warning(f"所有重试失败，未找到验证码 - 手机号: {phone}")
        return None

    async def send_verification_code(self, phone: str, template_type: SmsTemplate) -> None:
        """发送验证码

        Args:
            phone: 手机号
            template_type: 短信模板类型

        Raises:
            ValueError: 发送过于频繁
        """
        # 检查是否在一分钟内重复发送
        redis_key = self._get_redis_key(phone, template_type)

        # 生成验证码
        code = self._generate_code()

        try:
            # 先存储验证码到Redis，设置过期时间

            expire_seconds = int(timedelta(minutes=self.expire_minutes).total_seconds())
            await set_key(redis_key, code, expire=expire_seconds)

            # 发送短信验证码
            if settings.MODE == "dev":
                logger.info(f"开发模式下不发送短信，验证码为：{code}")
            else:
                # 发送短信
                sms_request = SMSIn(
                    phone_numbers=phone,
                    sign_name=template_type.sign_name,
                    template_code=template_type.template_code,
                    template_param='{"code":"' + code + '"}',
                )
                send_sms_task.apply_async(args=[sms_request.model_dump()])
            logger.info(f"验证码发送任务已创建，手机号：{phone}，模板类型：{template_type.name}")
        except Exception as e:
            # 发送失败时删除Redis中的验证码
            await delete_key(redis_key)
            logger.error(f"创建验证码发送任务失败：{str(e)}")
            raise SMSVerificationError("发送验证码失败") from e

    async def verify_code(self, phone: str, code: str, template_type: SmsTemplate) -> bool:
        """验证验证码

        Args:
            phone: 手机号
            code: 验证码
            template_type: 短信模板类型

        Returns:
            bool: 验证是否成功

        Raises:
            ValueError: 验证码错误次数过多
        """
        self.logger.info(
            f"开始验证短信验证码 - 手机号: {phone}, 输入验证码: {code}, 模板类型: {template_type}"
        )

        # 检查尝试次数
        attempts_key = self._get_attempts_key(phone)
        attempts = await get_key(attempts_key)
        self.logger.info(f"当前尝试次数: {attempts}, 最大尝试次数: {self.max_attempts}")
        if attempts and int(attempts) >= self.max_attempts:
            self.logger.error(f"验证码错误次数过多 - 手机号: {phone}, 当前尝试次数: {attempts}")
            raise SMSInvalidCodeError(
                "验证码错误次数过多，请重新获取验证码", details={"phone": phone}
            )

        # 获取存储的验证码 - 添加重试机制
        redis_key = self._get_redis_key(phone, template_type)

        # 开发模式下的万能验证码
        from app.config import settings

        self.logger.info(f"当前运行模式: {settings.MODE}")

        if settings.MODE == "dev" and code == "000000":
            self.logger.info("使用开发模式万能验证码")
            # 开发模式万能验证码，删除验证码和尝试次数
            await delete_key(redis_key)
            await delete_key(attempts_key)
            return True

        stored_code = await self._get_code_with_retry(redis_key, phone, template_type)
        self.logger.info(f"从Redis获取的存储验证码: {stored_code}, Redis键: {redis_key}")

        if not stored_code:
            self.logger.warning(f"Redis中未找到验证码 - 手机号: {phone}, 模板类型: {template_type}")
            return False

        if code == stored_code:
            # 验证成功，删除验证码和尝试次数
            self.logger.info(f"验证码验证成功 - 手机号: {phone}")
            await delete_key(redis_key)
            await delete_key(attempts_key)
            return True
        else:
            # 验证失败，增加尝试次数
            self.logger.warning(
                f"验证码验证失败 - 手机号: {phone}, 输入: {code}, 存储: {stored_code}"
            )
            expire_seconds = int(timedelta(minutes=self.expire_minutes).total_seconds())
            await increment_key(attempts_key, expire=expire_seconds)
            self.logger.info(f"已增加尝试次数，过期时间: {expire_seconds}秒")
            return False
