"""文章缓存服务模块"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app import crud, models, schemas
from app.config import settings
from app.services.base_cache_service import BaseCacheService
from app.utils.bloom_filters import article_bloom_filter


class ArticleCacheService(
    BaseCacheService["models.Article", "schemas.ArticleBase", "crud.CRUDArticle"]
):
    """
    文章缓存服务
    - 继承自 BaseCacheService，提供文章相关的特定配置
    """

    def __init__(self):
        """初始化服务，配置文章特定的缓存参数"""
        super().__init__(
            crud_model=crud.article,
            schema_model=schemas.ArticleBase,
            bloom_filter=article_bloom_filter,
            cache_key_prefix="article:base",
            entity_name="文章",
            cache_enabled_setting=settings.ARTICLE_CACHE_ENABLED,
            cache_expire_seconds=settings.ARTICLE_CACHE_EXPIRE_SECONDS,
        )

    async def _get_entity_from_db(self, db: AsyncSession, entity_id: int) -> models.Article | None:
        """从数据库获取文章实体，预加载相关关系"""
        async with self.db_query_semaphore:
            return await self.crud.get(
                db=db, 
                id=entity_id,
                options=[
                    selectinload(models.Article.tags),
                    selectinload(models.Article.category),
                ]
            )


# 全局实例将在 service_factory 中创建和管理
