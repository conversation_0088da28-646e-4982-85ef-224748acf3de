"""用户管理服务模块

专注于用户生命周期管理的服务，包括用户创建、更新、状态管理等核心功能。
遵循单一职责原则，只处理用户相关的业务逻辑。
"""

import random
from datetime import datetime

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.crud.crud_user_stats import user_stats
from app.models.user import UserRole
from app.services.interfaces.user_management_service_interface import IUserManagementService
from app.services.password_service import PasswordHashService
from app.services.video_folder_service import VideoFolderService
from app.utils.time_utils import TimeUtils


class UserManagementService(IUserManagementService):
    def __init__(self, password_service: PasswordHashService):
        self.password_service = password_service

    """用户管理服务

    专门处理用户的创建、更新和状态管理，遵循单一职责原则。
    所有数据库操作通过统一的CRUD层进行。
    """

    async def get_or_create_user_by_phone(
        self, phone: str, db: AsyncSession
    ) -> tuple[bool, models.User]:
        """根据手机号获取或创建用户

        Args:
            phone: 用户手机号
            db: 数据库会话

        Returns:
            Tuple[bool, models.User]: (是否为新用户, 用户实例)
        """
        user = await crud.user.get_by_username(db, username=phone)
        is_new = False

        if not user:
            is_new = True
            user_create = schemas.UserCreate(username=phone)
            user = await self._create_user_with_defaults(db, obj_in=user_create)

        # 仅为已存在的用户更新最后登录时间
        if not is_new:
            await self.update_last_login(user.id, db)

        await db.commit()
        await db.refresh(user)

        return is_new, user

    async def get_user_by_id(self, user_id: int, db: AsyncSession) -> models.User | None:
        """根据用户ID获取用户

        Args:
            user_id: 用户ID
            db: 数据库会话

        Returns:
            Optional[models.User]: 用户实例或None
        """
        return await crud.user.get(db, id=user_id)

    async def get_user_by_phone(self, phone: str, db: AsyncSession) -> models.User | None:
        """根据手机号获取用户

        Args:
            phone: 用户手机号
            db: 数据库会话

        Returns:
            Optional[models.User]: 用户实例或None
        """
        return await crud.user.get_by_username(db, username=phone)

    async def get_user_by_openid(self, openid: str, db: AsyncSession) -> models.User | None:
        """根据微信OpenID获取用户

        Args:
            openid: 微信OpenID
            db: 数据库会话

        Returns:
            Optional[models.User]: 用户实例或None
        """
        user = await crud.user.get_by_openid(db, openid=openid)
        if user:
            await self.update_last_login(user.id, db)
            await db.commit()
            await db.refresh(user)
        return user

    async def create_user_with_wechat_info(
        self, user_data: schemas.UserCreate, db: AsyncSession
    ) -> models.User:
        """使用微信信息创建用户

        Args:
            user_data: 包含微信信息的用户创建数据
            db: 数据库会话

        Returns:
            models.User: 创建的用户实例
        """
        # 处理微信用户信息
        wechat_info = self._extract_wechat_info(user_data)

        # 智能处理昵称和头像
        if not user_data.nickname and wechat_info:
            user_data.nickname = wechat_info.get("nickname")
        if not user_data.nickname:
            user_data.nickname = await self._generate_random_nickname()

        user = await self._create_user_with_defaults(db, obj_in=user_data, wechat_info=wechat_info)

        await db.commit()
        await db.refresh(user)

        return user

    async def update_last_login(self, user_id: int, db: AsyncSession) -> None:
        """更新用户最后登录时间

        Args:
            user_id: 用户ID
            db: 数据库会话
        """
        user = await crud.user.get(db, id=user_id)
        if user:
            # 使用有时区信息的UTC时间
            user.last_login = TimeUtils.get_utc_now()
            await db.commit()

    async def update_user(
        self, user_id: int, user_update: schemas.UserUpdate, db: AsyncSession
    ) -> models.User | None:
        """更新用户信息"""
        user = await crud.user.get(db, id=user_id)
        if not user:
            return None
        return await crud.user.update(db, db_obj=user, obj_in=user_update)

    async def update_user_profile(
        self, user_id: int, update_data: schemas.UserUpdate, db: AsyncSession
    ) -> models.User:
        """更新用户资料

        Args:
            user_id: 用户ID
            update_data: 更新数据
            db: 数据库会话

        Returns:
            models.User: 更新后的用户实例
        """
        user = await crud.user.get(db, id=user_id)
        if not user:
            raise ValueError(f"User with id {user_id} not found")

        updated_user = await crud.user.update(db, db_obj=user, obj_in=update_data)
        return updated_user

    async def activate_user(self, user_id: int, db: AsyncSession) -> models.User:
        """激活用户

        Args:
            user_id: 用户ID
            db: 数据库会话

        Returns:
            models.User: 激活后的用户实例
        """
        return await self.update_user_profile(user_id, schemas.UserUpdate(is_active=True), db)

    async def deactivate_user(self, user_id: int, db: AsyncSession) -> models.User:
        """停用用户

        Args:
            user_id: 用户ID
            db: 数据库会话

        Returns:
            models.User: 停用后的用户实例
        """
        return await self.update_user_profile(user_id, schemas.UserUpdate(is_active=False), db)

    async def bind_wechat_info(
        self, user_id: int, wechat_info: schemas.user.WeChatUserInfo, db: AsyncSession
    ) -> models.User:
        """绑定微信信息到现有用户

        Args:
            user_id: 用户ID
            wechat_info: 微信用户信息
            db: 数据库会话

        Returns:
            models.User: 绑定后的用户实例
        """
        update_data = schemas.UserUpdate()

        # 更新微信相关字段
        if wechat_info.openid:
            update_data.wechat_openid = wechat_info.openid
        if wechat_info.unionid:
            update_data.wechat_unionid = wechat_info.unionid
        if wechat_info.nickname and not update_data.nickname:
            update_data.nickname = wechat_info.nickname
        if wechat_info.headimgurl and not update_data.avatar:
            update_data.avatar = wechat_info.headimgurl

        return await self.update_user_profile(user_id, update_data, db)

    async def _create_user_with_defaults(
        self,
        db: AsyncSession,
        *,
        obj_in: schemas.UserCreate,
        wechat_info: dict | None = None,
    ) -> models.User:
        """创建新用户并处理所有默认的初始化操作

        这是系统中创建新用户的唯一权威方法。
        所有数据库操作都在一个事务中完成。

        Args:
            db: 数据库会话
            obj_in: 用户创建数据
            wechat_info: 微信用户信息（可选）

        Returns:
            models.User: 创建的用户实例
        """
        # 获取默认角色
        if not obj_in.role_id:
            result = await db.execute(select(UserRole).filter(UserRole.is_default))
            default_role = result.scalar_one_or_none()
            obj_in.role_id = default_role.id if default_role else 1

        # 智能处理昵称和头像
        if not obj_in.nickname and wechat_info:
            obj_in.nickname = wechat_info.get("nickname")
        if not obj_in.nickname:
            obj_in.nickname = await self._generate_random_nickname()

        # 如果提供了密码，则进行哈希处理
        if obj_in.password:
            obj_in.password = self.password_service.get_password_hash(obj_in.password)

        # 创建用户实例但暂不提交
        user = await crud.user.create(db, obj_in=obj_in, commit=False)

        if wechat_info and not user.avatar:
            user.avatar = wechat_info.get("headimgurl")
            db.add(user)

        # 刷新会话以获取 user.id，但不提交事务
        await db.flush()
        await db.refresh(user)

        # 在同一个事务中创建关联数据
        await VideoFolderService.create_default_folder(db, user_id=user.id, commit=False)
        await user_stats.get_or_create(db, user_id=user.id, commit=False)

        return user

    def _extract_wechat_info(self, user_data: schemas.UserCreate) -> dict | None:
        """从用户创建数据中提取微信信息

        Args:
            user_data: 用户创建数据

        Returns:
            Optional[dict]: 微信信息字典或None
        """
        wechat_fields = [
            "openid",
            "nickname",
            "sex",
            "province",
            "city",
            "country",
            "headimgurl",
            "privilege",
            "unionid",
        ]
        wechat_info = {}

        for field in wechat_fields:
            value = getattr(user_data, field, None)
            if value is not None:
                wechat_info[field] = value

        return wechat_info if wechat_info else None

    async def _generate_random_nickname(self) -> str:
        """生成随机昵称

        Returns:
            str: 随机生成的昵称
        """
        adjectives = [
            "快乐的",
            "勇敢的",
            "聪明的",
            "安静的",
            "活泼的",
            "温柔的",
            "机智的",
            "可爱的",
        ]
        nouns = ["小猫", "狮子", "土豆", "西瓜", "熊猫", "兔子", "小鸟", "花朵"]
        random_number = random.randint(1, 9999)
        return f"{random.choice(adjectives)}{random.choice(nouns)}{random_number}"


# 全局用户管理服务实例将在 service_factory 中创建
