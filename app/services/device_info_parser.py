"""设备信息解析服务模块"""

from fastapi import Request
from user_agents import parse

from app.services.device_fingerprint_service import DeviceFingerprintService


class DeviceInfoParser:
    """设备信息解析服务 - 独立模块"""

    @staticmethod
    def parse_device_info(request: Request) -> dict[str, str]:
        """解析设备信息

        Args:
            request: FastAPI请求对象

        Returns:
            Dict: 设备信息字典
        """
        user_agent = request.headers.get("user-agent", "")
        parsed_ua = parse(user_agent)

        # 确定设备类型
        device_type = "desktop"
        if parsed_ua.is_mobile:
            device_type = "mobile"
        elif parsed_ua.is_tablet:
            device_type = "tablet"

        # 生成设备名称
        device_name = f"{parsed_ua.os.family}"
        if parsed_ua.device.family and parsed_ua.device.family != "Other":
            device_name = f"{parsed_ua.device.family} - {parsed_ua.os.family}"

        return {
            "device_name": device_name,
            "device_type": device_type,
            "browser_name": parsed_ua.browser.family,
            "browser_version": parsed_ua.browser.version_string,
            "os_name": parsed_ua.os.family,
            "os_version": parsed_ua.os.version_string,
            "user_agent": user_agent,
        }

    @staticmethod
    def get_client_ip(request: Request) -> str:
        """获取客户端IP地址"""
        return DeviceFingerprintService._get_client_ip(request)
