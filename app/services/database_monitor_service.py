"""数据库连接池监控服务"""

import asyncio
import logging
from typing import Any

from sqlalchemy import text

from app.db.session import engine, get_db

logger = logging.getLogger(__name__)


class DatabaseMonitorService:
    """数据库连接池监控服务"""

    def __init__(self):
        self._monitoring_active = False
        self._last_stats = {}

    async def start_monitoring(self) -> None:
        """启动数据库连接监控"""
        if self._monitoring_active:
            logger.warning("数据库监控已经启动")
            return

        self._monitoring_active = True
        logger.info("数据库连接池监控已启动")

        # 创建后台监控任务
        asyncio.create_task(self._monitor_connection_pool())

    async def stop_monitoring(self) -> None:
        """停止数据库连接监控"""
        self._monitoring_active = False
        logger.info("数据库连接池监控已停止")

    async def _monitor_connection_pool(self) -> None:
        """后台监控连接池状态"""
        while self._monitoring_active:
            try:
                stats = await self._get_pool_stats()
                await self._check_pool_health(stats)
                await self._log_stats_if_needed(stats)

                # 每30秒检查一次
                await asyncio.sleep(30)
            except Exception as e:
                logger.error(f"连接池监控出错: {e}")
                await asyncio.sleep(60)  # 出错后等待更长时间重试

    async def _get_pool_stats(self) -> dict[str, Any]:
        """获取连接池统计信息"""
        try:
            # 获取当前连接池信息
            pool = engine.pool

            return {
                "pool_size": pool.size,
                "checked_out": pool.checkedout(),
                "overflow": pool.overflow(),
                "available": pool.size - pool.checkedout(),
                "usage_percent": (pool.checkedout() / pool.size) * 100 if pool.size > 0 else 0,
                "max_overflow": pool._max_overflow,
            }
        except Exception as e:
            logger.error(f"获取连接池统计信息失败: {e}")
            return {}

    async def _check_pool_health(self, stats: dict[str, Any]) -> None:
        """检查连接池健康状态"""
        if not stats:
            return

        # 检查连接使用率是否过高
        usage_percent = stats.get("usage_percent", 0)
        if usage_percent > 80:
            logger.warning(f"连接池使用率较高: {usage_percent:.1f}%")
        elif usage_percent > 90:
            logger.error(f"连接池使用率极高: {usage_percent:.1f}%")

        # 检查是否有连接泄漏
        overflow_count = stats.get("overflow", 0)
        max_overflow = stats.get("max_overflow", 30)
        if overflow_count == max_overflow:
            logger.error(f"连接池达到最大溢出限制: {overflow_count}/{max_overflow}")

    async def _log_stats_if_needed(self, stats: dict[str, Any]) -> None:
        """根据需要记录统计信息"""
        if not stats:
            return

        # 定期记录详细统计信息
        # 这里可以实现更复杂的日志记录逻辑，比如只在变化时记录

    async def get_health_check(self) -> dict[str, Any]:
        """获取数据库健康检查信息"""
        stats = await self._get_pool_stats()

        # 执行简单的健康检查查询
        is_connected = False
        try:
            async for db in get_db():
                await db.execute(text("SELECT 1"))
                is_connected = True
                break
        except Exception as e:
            logger.error(f"数据库健康检查失败: {e}")

        return {
            "connection_status": "healthy" if is_connected else "unhealthy",
            "pool_stats": stats,
        }

    async def reset_pool_if_needed(self) -> bool:
        """如果发现连接池问题，尝试重置连接池"""
        try:
            # 这里可以实现连接池重置逻辑
            # 注意：重置连接池可能影响正在进行的操作，应谨慎使用
            logger.info("尝试重置连接池...")
            await engine.dispose()
            logger.info("连接池重置完成")
            return True
        except Exception as e:
            logger.error(f"连接池重置失败: {e}")
            return False


# 创建全局实例
db_monitor = DatabaseMonitorService()


async def get_database_monitor() -> DatabaseMonitorService:
    """获取数据库监控服务实例"""
    return db_monitor
