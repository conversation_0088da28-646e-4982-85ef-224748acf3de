"""设备指纹服务模块"""

import hashlib
import json

from fastapi import Request
from user_agents import parse


class DeviceFingerprintService:
    """设备指纹生成服务 - 独立模块"""

    @staticmethod
    def generate_fingerprint(request: Request, user_id: int) -> str:
        """生成设备指纹

        Args:
            request: FastAPI请求对象
            user_id: 用户ID

        Returns:
            str: 设备指纹字符串
        """
        # 获取用户代理
        user_agent = request.headers.get("user-agent", "")

        # 获取IP地址
        ip_address = DeviceFingerprintService._get_client_ip(request)

        # 解析用户代理
        parsed_ua = parse(user_agent)

        # 构建指纹数据
        fingerprint_data = {
            "browser": f"{parsed_ua.browser.family}_{parsed_ua.browser.version_string}",
            "os": f"{parsed_ua.os.family}_{parsed_ua.os.version_string}",
            "device": parsed_ua.device.family,
            "ip_subnet": DeviceFingerprintService._get_ip_subnet(ip_address),
            "user_id": user_id,
        }

        # 生成哈希
        fingerprint_str = json.dumps(fingerprint_data, sort_keys=True)
        fingerprint_hash = hashlib.sha256(fingerprint_str.encode()).hexdigest()

        return fingerprint_hash[:32]  # 取前32位作为指纹

    @staticmethod
    def _get_client_ip(request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip

        # 返回直连IP
        return request.client.host if request.client else "unknown"

    @staticmethod
    def _get_ip_subnet(ip_address: str) -> str:
        """获取IP子网（用于指纹生成，提高稳定性）"""
        try:
            parts = ip_address.split(".")
            if len(parts) == 4:
                # IPv4，取前3段
                return ".".join(parts[:3]) + ".0"
            else:
                # IPv6或其他格式，直接返回
                return ip_address
        except:
            return ip_address
