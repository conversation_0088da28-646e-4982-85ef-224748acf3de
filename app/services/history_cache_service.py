"""历史记录缓存服务"""

from app.core.logging import logger
from app.db.redis import (
    delete_key,
    execute_pipeline,
    scan_keys,
    sorted_set_get_range,
)
from app.utils.time_utils import TimeUtils


class HistoryCacheService:
    """历史缓存服务类"""

    def __init__(self):
        self.cache_expire = 604800  # 缓存过期时间（秒）存储1周
        self.history_prefix = "history"  # 历史记录缓存前缀

    def _get_history_key(self, user_id: int, content_type: str) -> str:
        """获取历史记录缓存键"""
        return f"{self.history_prefix}:{user_id}:{content_type}"

    async def set_history(self, user_id: int, content_type: str, content_id: int) -> bool:
        """设置历史记录缓存 (写)"""
        try:
            key = self._get_history_key(user_id, content_type)
            timestamp = int(TimeUtils.datetime_to_timestamp(TimeUtils.get_utc_now()))

            # 使用pipeline确保原子性
            commands = [
                ("zadd", key, {str(content_id): timestamp}),
                ("zremrangebyrank", key, 0, -101),
                ("expire", key, self.cache_expire),
            ]
            await execute_pipeline(commands, write=True)
            return True
        except Exception as e:
            logger.error(f"设置历史记录缓存失败: {e}")
            return False

    async def get_history(
        self, user_id: int, content_type: str, skip: int = 0, limit: int = 100
    ) -> list[int]:
        """获取历史记录缓存 (读)"""
        try:
            key = self._get_history_key(user_id, content_type)
            content_ids = await sorted_set_get_range(key, skip, skip + limit - 1, desc=True)
            return [int(cid) for cid in content_ids]
        except Exception as e:
            logger.error(f"获取历史记录缓存失败: {e}")
            return []

    async def clear_user_content_type_cache(self, user_id: int, content_type: str) -> bool:
        """清除用户特定内容类型的历史记录缓存 (写)"""
        try:
            key = self._get_history_key(user_id, content_type)
            await delete_key(key)
            return True
        except Exception as e:
            logger.error(f"清除用户{content_type}历史记录缓存失败: {e}")
            return False

    async def clear_user_cache(self, user_id: int) -> bool:
        """清除用户所有历史记录缓存 (读+写 -> master)"""
        try:
            # 使用 scan 代替 keys 避免阻塞
            keys = await scan_keys(f"{self.history_prefix}:{user_id}:*")
            if keys:
                # delete_key 每次只能删一个，所以需要循环
                for key in keys:
                    await delete_key(key)
            return True
        except Exception as e:
            logger.error(f"清除用户所有历史记录缓存失败: {e}")
            return False

    async def set_history_batch(self, user_id: int, histories: list[dict]) -> bool:
        """批量设置历史记录缓存 (写)"""
        try:
            commands = []
            for history in histories:
                key = self._get_history_key(user_id, history["content_type"])
                timestamp = int(TimeUtils.datetime_to_timestamp(TimeUtils.get_utc_now()))
                commands.append(("zadd", key, {str(history["content_id"]): timestamp}))
                commands.append(("expire", key, self.cache_expire))

            await execute_pipeline(commands, write=True)
            return True
        except Exception as e:
            logger.error(f"批量设置历史记录缓存失败: {e}")
            return False

    async def get_history_with_score(
        self, user_id: int, content_type: str, skip: int = 0, limit: int = 100
    ) -> list[tuple[int, float]]:
        """获取历史记录及其分数（时间戳）(读)"""
        try:
            key = self._get_history_key(user_id, content_type)
            results = await sorted_set_get_range(
                key, skip, skip + limit - 1, withscores=True, desc=True
            )
            return [(int(cid), score) for cid, score in results]
        except Exception as e:
            logger.error(f"获取历史记录缓存失败: {e}")
            return []
