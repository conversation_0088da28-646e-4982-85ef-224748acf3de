from fastapi import HTT<PERSON>Exception, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app import models, schemas
from app.crud.base import CRUDBase
from app.models.video import Video
from app.models.video_folder import VideoFolder
from app.services.content_service import ContentService
from app.services.folder_stats_service import FolderStatsService
from app.services.video_cache_service import VideoCacheService
from app.utils.bloom_filters import video_bloom_filter
from app.utils.time_utils import TimeUtils


class VideoFolderService:
    """视频和文件夹领域的业务编排服务"""

    def __init__(
        self,
        user_crud: CRUDBase,
        video_crud: CRUDBase,
        folder_crud: CRUDBase,
        content_service: ContentService,
        stats_service: FolderStatsService,
        cache_service: VideoCacheService,
    ):
        self.user_crud = user_crud
        self.video_crud = video_crud
        self.folder_crud = folder_crud
        self.content_service = content_service
        self.stats_service = stats_service
        self.cache_service = cache_service

    async def create_default_folder(
        self, db: AsyncSession, user_id: int, commit: bool = True
    ) -> VideoFolder:
        """为用户创建默认文件夹

        Args:
            db: 数据库会话
            user_id: 用户ID
            commit: 是否提交事务

        Returns:
            VideoFolder: 创建的默认文件夹
        """
        default_folder = await self.folder_crud.get_default_folder(db, user_id=user_id)

        if default_folder:
            return default_folder

        # 获取用户信息，用于生成路径
        user = await self.user_crud.get(db, id=user_id)
        if not user:
            raise ValueError(f"用户ID {user_id} 不存在")

        # 创建默认文件夹
        default_folder = VideoFolder(
            name="默认文件夹",
            path=f"/user_{user_id}/default",
            user_id=user_id,
            is_default=True,
            access_level=0,  # 私有
            sort_order=0,
        )

        await self.folder_crud.create(db, obj_in=default_folder, commit=commit)

        return default_folder

    async def ensure_all_users_have_default_folders(self, db: AsyncSession) -> list[VideoFolder]:
        """确保所有用户都有默认文件夹

        Args:
            db: 数据库会话

        Returns:
            List[VideoFolder]: 创建的默认文件夹列表
        """
        # 获取所有用户ID
        user_ids = await self.folder_crud.get_all_user_ids(db)

        created_folders = []
        for user_id in user_ids:
            # 检查用户是否已有默认文件夹
            default_folder = await self.folder_crud.get_default_folder(db, user_id=user_id)

            if not default_folder:
                # 创建默认文件夹
                default_folder = await self.create_default_folder(db, user_id)
                created_folders.append(default_folder)

        return created_folders

    async def migrate_videos_to_default_folders(self, db: AsyncSession) -> int:
        """将无文件夹的视频迁移到默认文件夹

        Args:
            db: 数据库会话

        Returns:
            int: 迁移的视频数量
        """
        # 确保所有用户都有默认文件夹
        await self.ensure_all_users_have_default_folders(db)

        # 查找所有无文件夹的视频
        # This logic is slightly different, as it needs all videos without a folder
        # It's a one-off migration script logic, so keeping it here is acceptable for now.
        # For a cleaner approach, this could also be moved to a dedicated CRUD method.
        result = await db.execute(
            select(Video).where(Video.folder_id.is_(None), Video.is_deleted.is_(False))
        )
        videos_without_folder = result.scalars().all()

        migrated_count = 0
        for video in videos_without_folder:
            # 获取用户的默认文件夹
            default_folder = await self.folder_crud.get_default_folder(db, user_id=video.author_id)

            if default_folder:
                # 将视频移动到默认文件夹
                video.folder_id = default_folder.id
                migrated_count += 1

        await db.commit()
        return migrated_count

    async def get_folder_tree(
        self, db: AsyncSession, user_id: int, include_deleted: bool = False
    ) -> list[dict]:
        """获取用户的文件夹树结构

        Args:
            db: 数据库会话
            user_id: 用户ID
            include_deleted: 是否包含已删除的文件夹

        Returns:
            List[dict]: 文件夹树结构
        """
        # 构建查询条件
        folders = await self.folder_crud.get_by_user(db, user_id=user_id)
        if not include_deleted:
            folders = [f for f in folders if not f.is_deleted]

        # 构建文件夹树
        folder_map = {
            folder.id: {
                "id": folder.id,
                "name": folder.name,
                "path": folder.path,
                "is_default": folder.is_default,
                "access_level": folder.access_level,
                "is_public": folder.access_level == 1,  # 计算得出的兼容字段
                "cover_url": folder.cover_url,
                "video_count": folder.video_count,
                "created_at": folder.created_at.isoformat() if folder.created_at else None,
                "updated_at": folder.updated_at.isoformat() if folder.updated_at else None,
                "children": [],
            }
            for folder in folders
        }

        # 构建树结构
        root_folders = []
        for folder in folders:
            if folder.parent_id is None:
                root_folders.append(folder_map[folder.id])
            else:
                if folder.parent_id in folder_map:
                    folder_map[folder.parent_id]["children"].append(folder_map[folder.id])

        return root_folders

    async def soft_delete_folder(
        self, db: AsyncSession, folder_id: int, user_id: int | None = None
    ) -> bool:
        """软删除文件夹

        Args:
            db: 数据库会话
            folder_id: 文件夹ID
            user_id: 用户ID，用于权限验证

        Returns:
            bool: 是否删除成功
        """
        folder = await self.folder_crud.get(db, id=folder_id)
        if user_id is not None and folder.user_id != user_id:
            return False  # User does not own this folder
        if not folder:
            return False

        # 不允许删除默认文件夹
        if folder.is_default:
            return False

        # 标记为已删除
        folder.is_deleted = True
        folder.deleted_at = TimeUtils.get_utc_now()

        # 递归删除子文件夹
        children = await self.folder_crud.get_children(
            db, user_id=folder.user_id, parent_path=folder.path
        )
        for child in children:
            await self.soft_delete_folder(db, child.id, user_id=user_id)

        # 将文件夹中的视频移动到默认文件夹
        default_folder = await self.folder_crud.get_default_folder(db, user_id=folder.user_id)

        if default_folder:
            videos_in_folder = await self.folder_crud.get_videos_in_folder(db, folder_id=folder.id)
            for video in videos_in_folder:
                video.folder_id = default_folder.id
                db.add(video)

        await self.folder_crud.soft_delete(db, folder=folder)
        return True

    async def get_videos_in_same_folder(
        self,
        db: AsyncSession,
        *,
        video_id: int,
        folder_id: int,
        limit: int = 10,
        current_user_id: int | None = None,
    ) -> list[Video]:
        """获取指定文件夹下的其他视频（排除当前视频）

        Args:
            db: 数据库会话
            video_id: 当前视频ID
            folder_id: 文件夹ID
            limit: 返回的视频数量
            current_user_id: 当前用户ID，用于判断是否展示未发布的视频

        Returns:
            视频列表
        """
        # 构建基本查询条件
        query = select(Video).where(
            Video.folder_id == folder_id,
            Video.id != video_id,  # 排除当前视频
            Video.is_deleted.is_(False),
            Video.is_approved.is_(True),
        )

        # 根据用户身份判断是否展示未发布的视频
        if current_user_id is not None:
            # 如果是登录用户，展示自己的未发布视频和其他人的已发布视频
            query = query.where((Video.author_id == current_user_id) | (Video.is_published))
        else:
            # 如果是未登录用户，只展示已发布的视频
            query = query.where(Video.is_published)

        # 执行查询并返回结果
        result = await db.execute(query.limit(limit))
        return result.scalars().all()

    async def get_default_folder(self, db: AsyncSession, user_id: int) -> VideoFolder | None:
        """获取用户的默认文件夹

        Args:
            db: 数据库会话
            user_id: 用户ID

        Returns:
            VideoFolder | None: 用户的默认文件夹，如果不存在则返回None
        """
        # 查询用户的默认文件夹
        # 查询用户的默认文件夹，并按创建时间降序排序，获取最新的一个
        return await self.folder_crud.get_default_folder(db, user_id=user_id)

    # Video Lifecycle Management
    async def create_video(
        self, db: AsyncSession, *, obj_in: schemas.VideoCreate, author_id: int
    ) -> models.Video:
        """
        创建视频的完整业务流程。
        1. 创建数据库记录。
        2. 更新文件夹统计。
        3. 更新布隆过滤器。
        4. 创建领域事件。
        """
        video_create_schema = schemas.VideoCreate(**obj_in.model_dump(), author_id=author_id)

        # 在同一个事务中创建视频和事件
        async with db.begin_nested():
            db_obj = await self.video_crud.create(db, obj_in=video_create_schema, commit=False)
            self.video_crud._create_event(
                db, topic="video.created", payload={"video_id": db_obj.id}
            )

        await db.commit()
        await db.refresh(db_obj)

        # 事务外操作
        if db_obj.folder_id:
            await self.stats_service.update_folder_video_count(db, db_obj.folder_id)

        await video_bloom_filter.add(str(db_obj.id))

        return db_obj

    async def update_video(
        self,
        db: AsyncSession,
        *,
        video_id: int,
        obj_in: schemas.VideoUpdate,
        current_user: models.User,
    ) -> models.Video:
        """
        更新视频的完整业务流程。
        """
        db_obj = await self.video_crud.get(db, id=video_id)
        if not db_obj:
            raise HTTPException(status.HTTP_404_NOT_FOUND, "Video not found")

        # 权限检查
        await self.content_service.check_update_permission(db, video_id, current_user)

        old_folder_id = db_obj.folder_id
        update_data = obj_in.model_dump(exclude_unset=True)

        # 递增缓存版本
        db_obj.cache_version += 1

        async with db.begin_nested():
            updated_obj = await self.video_crud.update(
                db, db_obj=db_obj, obj_in=update_data, commit=False
            )
            self.video_crud._create_event(
                db, topic="video.updated", payload={"video_id": updated_obj.id}
            )

        await db.commit()
        await db.refresh(updated_obj)

        # 更新文件夹统计
        if "folder_id" in update_data and old_folder_id != updated_obj.folder_id:
            if old_folder_id:
                await self.stats_service.update_folder_video_count(db, old_folder_id)
            if updated_obj.folder_id:
                await self.stats_service.update_folder_video_count(db, updated_obj.folder_id)

        # 清理缓存
        await self.cache_service.invalidate_video(video_id)

        return updated_obj

    async def delete_video(
        self, db: AsyncSession, *, video_id: int, current_user: models.User
    ) -> models.Video:
        """
        软删除视频的完整业务流程。
        """
        db_obj = await self.video_crud.get(db, id=video_id)
        if not db_obj:
            raise HTTPException(status.HTTP_404_NOT_FOUND, "Video not found")

        # 权限检查
        await self.content_service.check_delete_permission(db, video_id, current_user)

        folder_id = db_obj.folder_id

        # 递增缓存版本
        db_obj.cache_version += 1

        async with db.begin_nested():
            deleted_obj = await self.video_crud.soft_remove(db, id=video_id, commit=False)
            self.video_crud._create_event(db, topic="video.deleted", payload={"video_id": video_id})

        await db.commit()

        if folder_id:
            await self.stats_service.update_folder_video_count(db, folder_id)

        await self.cache_service.invalidate_video(video_id)

        return deleted_obj


async def create_video_record_for_upload(
    self, db: AsyncSession, *, video_in: schemas.VideoCreate, author_id: int
) -> models.Video:
    """
    为批量上传创建一个视频记录，并更新统计，但不触发完整事件。
    这是一个简化的创建流程，用于文件上传的初始阶段。
    """
    video_create_schema = schemas.VideoCreate(**video_in.model_dump(), author_id=author_id)

    # 直接创建，因为后续还有Celery任务来更新状态
    db_obj = await self.video_crud.create(db, obj_in=video_create_schema, commit=True)

    # 更新用户统计
    await self.stats_service.update_folder_video_count(db, folder_id=video_in.folder_id)

    return db_obj
