"""用户行为追踪服务模块"""

import json
import logging
from typing import Any

from fastapi import Request
from sqlalchemy.ext.asyncio import AsyncSession
from user_agents import parse

from app import crud, schemas
from app.services.recommendation_cache_service import RecommendationCacheService
from app.services.video_cache_service import VideoCacheService

recommendation_cache_service = RecommendationCacheService()

logger = logging.getLogger(__name__)


class BehaviorTrackingService:
    """用户行为追踪服务类"""

    def __init__(self, video_cache_service: VideoCacheService):
        self.video_cache_service = video_cache_service
        # 交互权重配置
        self.interaction_weights = {
            "view": 1.0,
            "like": 3.0,
            "favorite": 5.0,
            "comment": 4.0,
            "share": 6.0,
            "click": 2.0,
            "search": 1.5,
            "download": 4.0,
        }

    async def track_content_view(
        self,
        db: AsyncSession,
        user_id: int,
        content_type: str,
        content_id: int,
        request: Request,
        duration: int | None = None,
        source: str | None = None,
    ) -> bool:
        """追踪内容浏览行为"""
        try:
            # 解析用户代理
            user_agent = request.headers.get("user-agent", "")
            parsed_ua = parse(user_agent)

            # 确定设备类型
            device_type = "desktop"
            if parsed_ua.is_mobile:
                device_type = "mobile"
            elif parsed_ua.is_tablet:
                device_type = "tablet"

            # 获取IP地址
            ip_address = self._get_client_ip(request)

            # 创建浏览历史记录
            browse_data = schemas.UserBrowseHistoryCreate(
                content_type=content_type,
                content_id=content_id,
                duration=duration,
                source=source,
                device_type=device_type,
                ip_address=ip_address,
                user_agent=user_agent,
            )

            await crud.user_browse_history.create_with_user(
                db=db, obj_in=browse_data, user_id=user_id
            )

            # 创建交互记录
            interaction_data = schemas.UserInteractionCreate(
                content_type=content_type,
                content_id=content_id,
                interaction_type="view",
                weight=self.interaction_weights.get("view", 1.0),
                extra_data=json.dumps(
                    {
                        "device_type": device_type,
                        "source": source,
                        "duration": duration,
                    },
                    ensure_ascii=False,
                ),
            )

            await crud.user_interaction.create_with_user(
                db=db, obj_in=interaction_data, user_id=user_id
            )

            # 异步更新用户画像
            await self._update_user_profile_async(db, user_id, content_type, content_id)

            # 清除用户推荐缓存
            await recommendation_cache_service.invalidate_user_cache(user_id)

            return True

        except Exception as e:
            logger.error(f"追踪内容浏览失败: {e}")
            raise

    async def track_interaction(
        self,
        db: AsyncSession,
        user_id: int,
        content_type: str,
        content_id: int,
        interaction_type: str,
        extra_data: dict[str, Any] | None = None,
    ) -> bool:
        """追踪用户交互行为"""
        try:
            # 获取交互权重
            weight = self.interaction_weights.get(interaction_type, 1.0)

            # 创建交互记录
            interaction_data = schemas.UserInteractionCreate(
                content_type=content_type,
                content_id=content_id,
                interaction_type=interaction_type,
                weight=weight,
                extra_data=json.dumps(extra_data or {}, ensure_ascii=False),
            )

            await crud.user_interaction.create_with_user(
                db=db, obj_in=interaction_data, user_id=user_id
            )

            # 对于重要交互，立即更新用户画像
            if interaction_type in ["like", "favorite", "comment", "share"]:
                await self._update_user_profile_async(db, user_id, content_type, content_id)
                # 清除用户推荐缓存
                await recommendation_cache_service.invalidate_user_cache(user_id)

            return True

        except Exception as e:
            logger.error(f"追踪用户交互失败: {e}")
            raise

    async def track_search_behavior(
        self,
        db: AsyncSession,
        user_id: int,
        search_query: str,
        search_results_count: int,
        clicked_content: dict[str, Any] | None = None,
    ) -> bool:
        """追踪搜索行为"""
        try:
            # 创建搜索交互记录
            search_data = {
                "search_query": search_query,
                "results_count": search_results_count,
                "clicked_content": clicked_content,
            }

            interaction_data = schemas.UserInteractionCreate(
                content_type="search",
                content_id=0,  # 搜索没有具体的内容ID
                interaction_type="search",
                weight=self.interaction_weights.get("search", 1.5),
                extra_data=json.dumps(search_data, ensure_ascii=False),
            )

            await crud.user_interaction.create_with_user(
                db=db, obj_in=interaction_data, user_id=user_id
            )

            # 如果用户点击了搜索结果，记录点击行为
            if clicked_content:
                await self.track_interaction(
                    db=db,
                    user_id=user_id,
                    content_type=clicked_content.get("content_type", ""),
                    content_id=clicked_content.get("content_id", 0),
                    interaction_type="click",
                    extra_data={"from_search": True, "search_query": search_query},
                )

            return True

        except Exception as e:
            logger.error(f"追踪搜索行为失败: {e}")
            raise

    async def get_user_behavior_summary(
        self, db: AsyncSession, user_id: int, days: int = 30
    ) -> dict[str, Any]:
        """获取用户行为摘要"""
        try:
            # 获取用户交互记录
            interactions = await crud.user_interaction.get_user_interactions(
                db=db, user_id=user_id, days=days, limit=1000
            )

            # 统计各种交互类型
            interaction_stats = {}
            content_type_stats = {"article": 0, "video": 0, "scratch": 0}
            total_weight = 0.0

            for interaction in interactions:
                # 统计交互类型
                if interaction.interaction_type not in interaction_stats:
                    interaction_stats[interaction.interaction_type] = 0
                interaction_stats[interaction.interaction_type] += 1

                # 统计内容类型偏好
                if interaction.content_type in content_type_stats:
                    content_type_stats[interaction.content_type] += interaction.weight

                total_weight += interaction.weight

            # 获取浏览历史统计
            browse_history = await crud.user_browse_history.get_user_history(
                db=db, user_id=user_id, days=days, limit=1000
            )

            # 统计设备使用情况
            device_stats = {}
            for history in browse_history:
                if history.device_type:
                    if history.device_type not in device_stats:
                        device_stats[history.device_type] = 0
                    device_stats[history.device_type] += 1

            # 计算活跃度分数
            activity_score = min(total_weight / 100.0, 1.0)

            return {
                "user_id": user_id,
                "days_analyzed": days,
                "total_interactions": len(interactions),
                "total_views": len(browse_history),
                "interaction_stats": interaction_stats,
                "content_type_preference": content_type_stats,
                "device_usage": device_stats,
                "activity_score": activity_score,
                "total_engagement_weight": total_weight,
            }

        except Exception as e:
            logger.error(f"获取用户行为摘要失败: {e}")
            raise

    async def _update_user_profile_async(
        self, db: AsyncSession, user_id: int, content_type: str, content_id: int
    ) -> None:
        """异步更新用户画像"""
        try:
            # 获取当前用户画像
            profile = await crud.user_profile.get_by_user_id(db, user_id=user_id)

            # 获取内容的标签和分类信息
            content_tags = []
            content_category = None

            if content_type == "article":
                content = await crud.article.get(db, id=content_id)
                if content:
                    content_tags = [tag.name for tag in content.tags]
                    content_category = content.category.name if content.category else None
            elif content_type == "video":
                content = await self.video_cache_service.get_entity(db, entity_id=content_id)
                if content:
                    content_tags = [tag.name for tag in content.tags]
                    content_category = content.category.name if content.category else None

            elif content_type == "scratch":
                content = await crud.scratch_product.get(db, id=content_id)
                if content:
                    content_tags = []  # Scratch项目通常没有标签
                    content_category = content.category  # Scratch项目使用category字符串字段

            # 更新兴趣标签
            interest_tags = {}
            if profile and profile.interest_tags:
                interest_tags = json.loads(profile.interest_tags)

            for tag in content_tags:
                if tag not in interest_tags:
                    interest_tags[tag] = 0.0
                interest_tags[tag] += 0.1  # 增加兴趣权重

            # 更新偏好分类
            preferred_categories = {}
            if profile and profile.preferred_categories:
                preferred_categories = json.loads(profile.preferred_categories)

            if content_category:
                if content_category not in preferred_categories:
                    preferred_categories[content_category] = 0.0
                preferred_categories[content_category] += 0.2  # 增加分类权重

            # 归一化权重（保持在合理范围内）
            max_tag_weight = max(interest_tags.values()) if interest_tags else 1.0
            if max_tag_weight > 10.0:
                for tag in interest_tags:
                    interest_tags[tag] /= max_tag_weight / 10.0

            max_category_weight = (
                max(preferred_categories.values()) if preferred_categories else 1.0
            )
            if max_category_weight > 10.0:
                for category in preferred_categories:
                    preferred_categories[category] /= max_category_weight / 10.0

            # 更新用户画像
            profile_update = schemas.UserProfileUpdate(
                interest_tags=json.dumps(interest_tags, ensure_ascii=False),
                preferred_categories=json.dumps(preferred_categories, ensure_ascii=False),
            )

            await crud.user_profile.update_or_create(db=db, user_id=user_id, obj_in=profile_update)

        except Exception as e:
            logger.error(f"更新用户画像失败: {e}")
            raise

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 尝试从各种头部获取真实IP
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        # 回退到客户端IP
        return request.client.host if request.client else "unknown"
