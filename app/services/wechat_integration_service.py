"""微信API集成服务 - 专门处理与微信API的交互"""

import hashlib
import time
import uuid

import httpx

from app import schemas
from app.config import get_settings
from app.core.logging import logger
from app.db.redis import get_key, set_key
from app.exceptions.auth import WeChatAuthError
from app.utils.wechat_httpx import get_wechat_access_token, get_wechat_qr_code, get_wechat_user_info

settings = get_settings()


class WeChatIntegrationService:
    """微信API集成服务 - 处理与微信API的所有交互"""

    def __init__(self):
        self.settings = settings
        self.logger = logger

    async def _get_access_token_key(self) -> str:
        """获取access token的Redis键"""
        return "wechat:access_token"

    async def get_access_token(self) -> str:
        """获取微信公众号access_token"""
        cached_token = await get_key(await self._get_access_token_key())
        if cached_token:
            return cached_token

        try:
            response = await get_wechat_access_token(
                self.settings.WECHAT_APP_ID, self.settings.WECHAT_APP_SECRET
            )
            data = response.json()

            if "access_token" in data:
                access_token = data["access_token"]
                expires_in = data.get("expires_in", 7200)
                # 提前5分钟过期，避免边界情况
                await set_key(
                    await self._get_access_token_key(), access_token, expire=expires_in - 300
                )
                self.logger.info("获取微信access_token成功")
                return access_token

            self.logger.error(f"获取微信access_token失败: {data}")
            raise WeChatAuthError("获取微信access_token失败", details=data)

        except httpx.HTTPError as e:
            self.logger.error(f"获取微信access_token失败: {e}")
            raise WeChatAuthError("获取微信access_token失败") from e

    async def create_qr_code_ticket(self, scene_str: str) -> dict[str, any]:
        """创建二维码ticket"""
        access_token = await self.get_access_token()

        try:
            response = await get_wechat_qr_code(access_token, scene_str)
            data = response.json()

            if "ticket" in data:
                qr_url = f"https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket={data['ticket']}"
                return {
                    "ticket": data["ticket"],
                    "expire_seconds": data["expire_seconds"],
                    "url": data["url"],
                    "qr_url": qr_url,
                    "scene_str": scene_str,
                }

            self.logger.error(f"创建登录二维码失败: {data}")
            raise WeChatAuthError("创建登录二维码失败", details=data)

        except httpx.HTTPError as e:
            self.logger.error(f"创建登录二维码失败: {e}")
            raise WeChatAuthError("创建登录二维码失败") from e

    async def get_user_info(self, openid: str) -> schemas.user.WeChatUserInfo:
        """获取微信用户基本信息"""
        access_token = await self.get_access_token()

        try:
            response = await get_wechat_user_info(access_token, openid)
            data = response.json()

            if "errcode" not in data:
                return schemas.user.WeChatUserInfo(**data)

            self.logger.error(f"获取微信用户基本信息失败: {data}")
            raise WeChatAuthError("获取微信用户基本信息失败", details=data)

        except httpx.HTTPError as e:
            self.logger.error(f"获取微信用户基本信息失败: {e}")
            raise WeChatAuthError("获取微信用户基本信息失败") from e

    def verify_signature(self, signature: str, timestamp: str, nonce: str) -> bool:
        """验证微信签名，用于微信服务器验证"""
        token = self.settings.WECHAT_MESSAGE_TOKEN

        # 将Token、timestamp、nonce三个参数进行字典序排序
        params = [token, timestamp, nonce]
        params.sort()

        # 将三个参数字符串拼接成一个字符串进行sha1加密
        temp_str = "".join(params)
        sha1_hash = hashlib.sha1(temp_str.encode("utf-8")).hexdigest()

        # 开发者获得加密后的字符串可与signature对比，标识该请求来源于微信
        return sha1_hash == signature

    def generate_scene_str(self) -> str:
        """生成场景字符串"""
        return f"login_{uuid.uuid4().hex}_{int(time.time())}"

    def create_text_response(self, to_user: str, from_user: str, content: str) -> str:
        """创建文本回复消息的XML格式"""
        create_time = int(time.time())
        xml_template = f"""<xml>
<ToUserName><![CDATA[{to_user}]]></ToUserName>
<FromUserName><![CDATA[{from_user}]]></FromUserName>
<CreateTime>{create_time}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[{content}]]></Content>
</xml>"""
        return xml_template
