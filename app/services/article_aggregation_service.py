"""
文章数据聚合服务
- 负责将文章的基础信息、作者、统计数据、审核信息等高效地聚合在一起。
"""

import asyncio
from typing import Any

from fastapi import HTTPException, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.core.pagination import CursorPaginationParams, CursorPaginationResponse
from app.services.article_cache_service import ArticleCacheService
from app.services.content_stats_service import ContentStatsService
from app.services.user_cache_service import UserCacheService


class ArticleAggregationService:
    def __init__(
        self,
        article_cache_service: ArticleCacheService,
        content_stats_service: ContentStatsService,
        user_cache_service: UserCacheService,
    ):
        self.article_cache_service = article_cache_service
        self.content_stats_service = content_stats_service
        self.user_cache_service = user_cache_service

    """
    文章数据聚合服务
    """

    async def _aggregate_articles_by_ids(
        self,
        db: AsyncSession,
        *,
        article_ids: list[int],
        current_user: models.User | None = None,
        include_review: bool = False,
        include_content: bool = False,
    ) -> list[schemas.ArticleOut]:
        """
        根据文章ID列表，聚合完整的文章信息
        这是一个核心的私有方法，用于代码复用
        """
        if not article_ids:
            return []

        print(f"DEBUG: 输入的article_ids: {article_ids}")

        # 1. 批量获取文章基础数据
        # 1. 批量获取文章基础数据 - 现在完全依赖 cache_service
        # 修复: ArticleCacheService 没有 get_articles_batch，使用 BaseCacheService 提供的 get_entities_batch
        articles_data = await self.article_cache_service.get_entities_batch(db, article_ids)
        print(
            f"DEBUG: 从缓存获取的articles_data keys: {list(articles_data.keys()) if articles_data else 'None'}"
        )
        print(f"DEBUG: articles_data数量: {len(articles_data) if articles_data else 0}")

        # 2. 收集所有需要关联查询的ID
        author_ids = {a.author_id for a in articles_data.values() if a and a.author_id}
        print(f"DEBUG: 收集到的author_ids: {author_ids}")

        # 3. 构建并行任务列表
        tasks = {
            "stats": self.content_stats_service.batch_get_stats(
                db,
                content_items=[("article", aid) for aid in article_ids],
                user_id=current_user.id if current_user else None,
            ),
            "authors": self.user_cache_service.get_entities_batch(db, entity_ids=list(author_ids)),
        }
        if include_review:
            tasks["reviews"] = crud.review.get_multi_by_content_ids(
                db, content_type="article", content_ids=article_ids
            )

        # 4. 并行执行所有任务
        results = await asyncio.gather(*tasks.values())
        print(f"DEBUG: 并行任务结果数量: {len(results)}")

        # 5. 将结果列表转换为ID为键的字典以便快速查找
        stats_map = results[0]
        authors_map = results[1]  # get_users_batch returns a dict
        print(f"DEBUG: stats_map keys: {list(stats_map.keys()) if stats_map else 'None'}")
        print(f"DEBUG: authors_map keys: {list(authors_map.keys()) if authors_map else 'None'}")

        reviews_map = {}
        if include_review:
            reviews_map = {review.content_id: review for review in results[2]}

        # 6. 在内存中高效组装成 ArticleOut 对象
        article_out_list = []
        processed_count = 0
        skipped_count = 0

        for article_id in article_ids:
            print(f"DEBUG: 处理article_id: {article_id}")
            article_base = articles_data.get(article_id)
            print(f"DEBUG: article_base: {article_base}")

            if not article_base:
                print(f"DEBUG: article_id {article_id} 没有找到对应的article_base，跳过")
                skipped_count += 1
                continue

            processed_count += 1
            stats = stats_map.get(("article", article_id), {})
            author = authors_map.get(article_base.author_id)
            review = reviews_map.get(article_id)

            print(
                f"DEBUG: article_id {article_id} - stats: {stats is not None}, author: {author is not None}"
            )

            # 使用 dict 解构方式构建 ArticleOut，避免 Pydantic v2 from_orm 额外参数错误
            base_data = (
                article_base.dict() if hasattr(article_base, "dict") else article_base.model_dump()
            )

            # 确保 base_data 中的 id 字段不为 None，这对 populate_meta 方法很重要
            if base_data.get("id") is None:
                base_data["id"] = article_id

            # 确保 category 字段被正确处理，避免异步加载
            if "category" in base_data and base_data["category"] is not None:
                if hasattr(base_data["category"], "dict"):
                    base_data["category"] = base_data["category"].dict()
                elif hasattr(base_data["category"], "model_dump"):
                    base_data["category"] = base_data["category"].model_dump()

            # 确保 tags 字段被正确处理
            if "tags" in base_data and base_data["tags"] is not None:
                tags_data = []
                for tag in base_data["tags"]:
                    if hasattr(tag, "dict"):
                        tags_data.append(tag.dict())
                    elif hasattr(tag, "model_dump"):
                        tags_data.append(tag.model_dump())
                    else:
                        tags_data.append(tag)
                base_data["tags"] = tags_data

            # 构建各个字段的原始数据，避免Pydantic v2验证冲突
            extra_data = {
                "stats": stats if stats else None,
                "author": author.model_dump() if author else None,
                "review": review.__dict__.copy() if review else None,
            }
            # 显式构建 meta，确保 content_id 不为 None
            original_article_id = getattr(article_base, "id", None)
            print(
                f"DEBUG: original_article_id: {original_article_id}, 循环article_id: {article_id}"
            )

            if original_article_id is None:
                print(f"DEBUG: article_id {article_id} 的ID为None，跳过")
                skipped_count += 1
                continue

            meta_data = {
                "content_type": "article",
                "content_id": original_article_id,
                "slug": getattr(article_base, "slug", None),
                "keywords": [
                    tag.name for tag in getattr(article_base, "tags", []) if hasattr(tag, "name")
                ],
            }

            try:
                article_out = schemas.ArticleOut(**{**base_data, **extra_data, "meta": meta_data})
                article_out_list.append(article_out)
                print(f"DEBUG: 成功创建article_out for article_id {article_id}")

                # 根据参数决定是否包含content字段
                if not include_content:
                    article_out.content = None

            except Exception as e:
                print(f"DEBUG: 创建article_out失败 article_id {article_id}: {e}")
                skipped_count += 1

        print(
            f"DEBUG: 处理完成 - 总数: {len(article_ids)}, 成功: {processed_count}, "
            f"跳过: {skipped_count}, 返回: {len(article_out_list)}"
        )
        return article_out_list

    async def get_aggregated_articles_by_ids(
        self,
        db: AsyncSession,
        *,
        article_ids: list[int],
        current_user: models.User | None = None,
        include_review: bool = False,
        include_content: bool = False,
    ) -> list[schemas.ArticleOut]:
        """
        获取按ID列表聚合的文章列表
        """
        if not article_ids:
            return []
        return await self._aggregate_articles_by_ids(
            db,
            article_ids=article_ids,
            current_user=current_user,
            include_review=include_review,
            include_content=include_content,
        )

    async def get_paginated_articles(
        self,
        db: AsyncSession,
        *,
        params: CursorPaginationParams,
        filters: dict[str, Any] | None = None,
        current_user: models.User | None = None,
        include_review: bool = False,
        include_content: bool = False,
    ) -> CursorPaginationResponse[schemas.ArticleOut]:
        """
        获取分页的、完整聚合的文章列表
        """
        filters = filters or {}

        author_id = filters.get("author_id")
        is_author = current_user and author_id == current_user.id
        is_admin = current_user and current_user.is_superuser
        can_view_all = is_author or is_admin

        paginated_result = await crud.article.get_paginated_articles(
            db=db,
            params=params,
            filters=filters,
            can_view_all=can_view_all,
            include_total=True,  # 总是获取总数
        )

        if not paginated_result.items:
            return CursorPaginationResponse(
                items=[], total_count=0, **params.dict(exclude_unset=True)
            )

        article_ids = [item.id for item in paginated_result.items]

        article_out_list = await self._aggregate_articles_by_ids(
            db,
            article_ids=article_ids,
            current_user=current_user,
            include_review=include_review,
            include_content=include_content,
        )

        # 保持原始分页的顺序
        articles_map = {article.id: article for article in article_out_list}
        sorted_articles = [articles_map[id] for id in article_ids if id in articles_map]

        return CursorPaginationResponse(
            items=sorted_articles,
            total_count=paginated_result.total_count,
            has_next=paginated_result.has_next,
            has_previous=paginated_result.has_previous,
            next_cursor=paginated_result.next_cursor,
            previous_cursor=paginated_result.previous_cursor,
        )

    async def get_aggregated_article(
        self,
        db: AsyncSession,
        *,
        article_id: int,
        current_user: models.User | None = None,
        include_content: bool = False,
    ) -> schemas.ArticleOut | None:
        """
        获取单个的、完整聚合的文章信息
        - 修改：通过批量降级通道获取基础实体，避免 Bloom 未初始化/误判导致返回空
        """
        # 通过带降级能力的批量通道获取单个基础实体
        batch_map = await self.article_cache_service.get_entities_batch_with_fallback(
            db, [article_id]
        )
        article_base = batch_map.get(article_id)
        if not article_base:
            return None

        # 并行获取关联数据
        stats_task = self.content_stats_service.get_stats(
            db,
            content_type="article",
            content_id=article_id,
            user_id=current_user.id if current_user else None,
        )
        author_task = self.user_cache_service.get_entity(db, entity_id=article_base.author_id)

        is_owner = current_user and current_user.id == article_base.author_id
        review_task = (
            crud.review.get_by_content(db, content_type="article", content_id=article_id)
            if is_owner
            else None
        )

        stats, author, review = await asyncio.gather(stats_task, author_task, review_task)

        # 组装成 ArticleOut 对象
        base_data = (
            article_base.dict() if hasattr(article_base, "dict") else article_base.model_dump()
        )

        # 确保 category 字段被正确处理，避免异步加载
        if "category" in base_data and base_data["category"] is not None:
            if hasattr(base_data["category"], "dict"):
                base_data["category"] = base_data["category"].dict()
            elif hasattr(base_data["category"], "model_dump"):
                base_data["category"] = base_data["category"].model_dump()

        # 确保 tags 字段被正确处理
        if "tags" in base_data and base_data["tags"] is not None:
            tags_data = []
            for tag in base_data["tags"]:
                if hasattr(tag, "dict"):
                    tags_data.append(tag.dict())
                elif hasattr(tag, "model_dump"):
                    tags_data.append(tag.model_dump())
                else:
                    tags_data.append(tag)
            base_data["tags"] = tags_data

        extra_data = {
            "stats": stats if stats else None,
            "author": schemas.UserAggregated.model_validate(author) if author is not None else None,
            "review": schemas.ReviewBase.model_validate(review) if review is not None else None,
        }
        # 显式构建 meta，确保 content_id 不为 None
        meta_data = {
            "content_type": "article",
            "content_id": article_base.id,
            "slug": getattr(article_base, "slug", None),
            "keywords": [
                tag.name for tag in getattr(article_base, "tags", []) if hasattr(tag, "name")
            ],
        }
        article_out = schemas.ArticleOut(**{**base_data, **extra_data, "meta": meta_data})

        # 当需要正文且当前内容为空时，回源数据库补充正文，避免基础缓存不含content导致的空值
        if include_content and (getattr(article_out, "content", None) in (None, "")):
            try:
                db_entity = await crud.article.get(db=db, id=article_id)
            except Exception:
                db_entity = None
            if db_entity and getattr(db_entity, "content", None) is not None:
                article_out.content = db_entity.content

        # 根据参数决定是否包含content字段
        if not include_content:
            article_out.content = None

        return article_out

    async def get_user_articles_with_cache(
        self,
        db: AsyncSession,
        user_id: int,
        params: CursorPaginationParams,
        filters: dict[str, Any] | None = None,
        current_user: models.User | None = None,
        include_review: bool = False,
        include_content: bool = False,
        include_fields: set[str] | None = None,
        is_own_articles: bool = False,
    ) -> CursorPaginationResponse[schemas.ArticleOut]:
        """获取用户文章列表（使用缓存服务进行数据聚合）

        专注于数据聚合，不直接操作缓存，由调用方处理缓存策略
        """
        from app.models.article import Article

        filters = filters or {}
        include_fields = include_fields or set()

        # 数据库查询获取文章ID列表
        article_query = select(Article.id).where(Article.author_id == user_id)

        # 应用状态过滤器
        if not is_own_articles:
            article_query = article_query.where(Article.status == "published_approved")
        elif "status" in filters and filters["status"] != "all":
            article_query = article_query.where(Article.status == filters["status"])

        # 应用游标分页
        if params.cursor:
            cursor_value = int(params.cursor)
            if params.order_direction == "desc":
                article_query = article_query.where(Article.id < cursor_value)
            else:
                article_query = article_query.where(Article.id > cursor_value)

        # 应用排序
        order_column = getattr(Article, params.order_by, Article.id)
        if params.order_direction == "desc":
            article_query = article_query.order_by(order_column.desc())
        else:
            article_query = article_query.order_by(order_column.asc())

        # 限制数量
        article_query = article_query.limit(params.size + 1)

        result = await db.execute(article_query)
        article_ids = [row[0] for row in result.fetchall()]

        if not article_ids:
            return CursorPaginationResponse(
                items=[],
                total_count=0,
                has_next=False,
                has_previous=False,
                next_cursor=None,
                previous_cursor=None,
            )

        # 检查是否有更多数据
        has_more = len(article_ids) > params.size
        if has_more:
            article_ids = article_ids[: params.size]

        next_cursor = str(article_ids[-1]) if has_more else None

        # 获取完整的聚合数据
        articles = await self._aggregate_articles_by_ids(
            db=db,
            article_ids=article_ids,
            current_user=current_user,
            include_review=include_review,
            include_content=include_content,
        )

        # 动态构建返回字段
        if include_fields:
            filtered_articles = []
            for article in articles:
                filtered_data = {k: v for k, v in article.dict().items() if k in include_fields}
                filtered_articles.append(schemas.ArticleOut(**filtered_data))
            articles = filtered_articles

        response = CursorPaginationResponse(
            items=articles,
            total_count=len(articles),
            has_next=has_more,
            has_previous=params.cursor is not None,
            next_cursor=next_cursor,
            previous_cursor=params.cursor,
        )

        return response

    async def get_user_behavior_articles(
        self,
        db: AsyncSession,
        user_id: int,
        behavior_type: str,
        params: CursorPaginationParams,
        current_user: models.User | None = None,
        include_content: bool = False,
        include_fields: set[str] | None = None,
    ) -> CursorPaginationResponse[schemas.ArticleOut]:
        """获取用户行为相关的文章列表（点赞、收藏、历史记录）

        专注于数据聚合，不直接操作缓存，由调用方处理缓存策略
        """
        from sqlalchemy import select

        from app.models import Favorite, History, Like

        include_fields = include_fields or set()

        # 验证权限 - 只能查看自己的行为数据
        if current_user and current_user.id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="只能查看自己的行为数据"
            )

        # 根据行为类型获取文章ID列表
        article_ids = []

        if behavior_type == "likes":
            # 获取点赞的文章ID
            like_query = select(Like.article_id).where(
                Like.user_id == user_id, Like.status == "active"
            )
            result = await db.execute(like_query)
            article_ids = [row[0] for row in result.fetchall()]

        elif behavior_type == "favorites":
            # 获取收藏的文章ID
            favorite_query = select(Favorite.article_id).where(
                Favorite.user_id == user_id, Favorite.status == "active"
            )
            result = await db.execute(favorite_query)
            article_ids = [row[0] for row in result.fetchall()]

        elif behavior_type == "history":
            # 获取浏览历史的文章ID
            history_query = (
                select(History.article_id)
                .where(History.user_id == user_id)
                .order_by(History.viewed_at.desc())
            )
            result = await db.execute(history_query)
            article_ids = [row[0] for row in result.fetchall()]

        if not article_ids:
            return CursorPaginationResponse(
                items=[],
                total_count=0,
                has_next=False,
                has_previous=False,
                next_cursor=None,
                previous_cursor=None,
            )

        # 应用游标分页
        if params.cursor:
            cursor_index = int(params.cursor)
            article_ids = article_ids[cursor_index:]

        # 限制数量
        article_ids = article_ids[: params.size + 1]

        # 检查是否有更多数据
        has_more = len(article_ids) > params.size
        if has_more:
            article_ids = article_ids[: params.size]

        next_cursor = str(len(article_ids)) if has_more else None

        # 获取完整的聚合数据
        articles = await self._aggregate_articles_by_ids(
            db=db,
            article_ids=article_ids,
            current_user=current_user,
            include_review=False,  # 行为数据不包含审核信息
            include_content=include_content,
        )

        # 动态构建返回字段
        if include_fields:
            filtered_articles = []
            for article in articles:
                filtered_data = {k: v for k, v in article.dict().items() if k in include_fields}
                filtered_articles.append(schemas.ArticleOut(**filtered_data))
            articles = filtered_articles

        return CursorPaginationResponse(
            items=articles,
            total_count=len(articles),
            has_next=has_more,
            has_previous=params.cursor is not None,
            next_cursor=next_cursor,
            previous_cursor=params.cursor,
        )


# 全局实例将在 service_factory 中创建和管理
