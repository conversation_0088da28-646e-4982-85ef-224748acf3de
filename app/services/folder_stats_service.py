"""文件夹统计服务"""

from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import logger
from app.models.video import Video
from app.models.video_folder import VideoFolder


class FolderStatsService:
    """文件夹统计服务，用于维护文件夹的统计数据，如视频数量"""

    @staticmethod
    async def update_folder_video_count(db: AsyncSession, folder_id: int) -> int:
        """更新指定文件夹的视频数量

        Args:
            db: 数据库会话
            folder_id: 文件夹ID

        Returns:
            int: 更新后的视频数量
        """
        try:
            # 查询文件夹中的视频数量
            result = await db.execute(
                select(func.count(Video.id))
                .where(Video.folder_id == folder_id)
                .where(not Video.is_deleted)
            )
            video_count = result.scalar_one() or 0

            # 更新文件夹的视频数量
            result = await db.execute(select(VideoFolder).where(VideoFolder.id == folder_id))
            folder = result.scalar_one_or_none()
            if folder:
                # 使用update_video_count方法更新视频数量，保持一致性
                folder.update_video_count(video_count)
                db.add(folder)
                await db.commit()
                logger.info(f"更新文件夹 {folder_id} 的视频数量为 {video_count}")
                return video_count
            else:
                logger.warning(f"文件夹 {folder_id} 不存在，无法更新视频数量")
                return 0
        except Exception as e:
            logger.error(f"更新文件夹视频数量失败: {e}")
            await db.rollback()
            return 0

    @staticmethod
    async def update_folder_video_count_batch(
        db: AsyncSession, folder_ids: list[int]
    ) -> dict[int, int]:
        """批量更新多个文件夹的视频数量

        Args:
            db: 数据库会话
            folder_ids: 文件夹ID列表

        Returns:
            dict[int, int]: 文件夹ID到视频数量的映射
        """
        result = {}
        for folder_id in folder_ids:
            count = await FolderStatsService.update_folder_video_count(db, folder_id)
            result[folder_id] = count
        return result

    @staticmethod
    async def update_all_folders_video_count(db: AsyncSession) -> int:
        """更新所有文件夹的视频数量

        Args:
            db: 数据库会话

        Returns:
            int: 更新的文件夹数量
        """
        try:
            # 获取所有文件夹ID
            result = await db.execute(select(VideoFolder.id))
            folder_ids = [folder_id for (folder_id,) in result.all()]

            # 批量更新视频数量
            await FolderStatsService.update_folder_video_count_batch(db, folder_ids)
            return len(folder_ids)
        except Exception as e:
            logger.error(f"更新所有文件夹视频数量失败: {e}")
            return 0
