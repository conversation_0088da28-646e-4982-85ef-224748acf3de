from typing import Any, Literal

from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas


class ContentService:
    """内容服务，处理文章和视频的通用逻辑"""

    def __init__(self, content_type: Literal["article", "video"]) -> None:
        """初始化内容服务

        Args:
            content_type: 内容类型，article或video
        """
        self.content_type = content_type
        self.crud = crud.article if content_type == "article" else crud.video
        self.model = models.Article if content_type == "article" else models.Video
        # self.schema = schemas.Article if content_type == "article" else schemas.Video

    async def handle_publish_status(
        self, db: AsyncSession, content: Any, is_published: bool | None = None
    ) -> Any:
        """处理内容的发布状态

        Args:
            db: 数据库会话
            content: 内容对象
            is_published: 是否发布

        Returns:
            更新后的内容对象
        """
        if is_published is None:
            return content

        # 如果内容从未发布变为发布，需要创建审核记录
        if not content.is_published and is_published:
            # 发布前检查标题和内容是否为空
            if not content.title or not content.content:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="文章标题和内容不能为空",
                )
            content.is_approved = False
            content.is_published = True
            db.add(content)
            await db.commit()
            await db.refresh(content)

            review_in = schemas.ReviewCreate(
                content_type=self.content_type,
                content_id=content.id,
            )
            await crud.review.create(db=db, obj_in=review_in)

        # 如果内容从发布变为未发布，删除审核记录
        elif content.is_published and not is_published:
            content.is_published = False
            content.is_approved = False
            db.add(content)
            await db.commit()
            await db.refresh(content)

            review = await crud.review.get_by_content(
                db, content_type=self.content_type, content_id=content.id
            )
            if review:
                await crud.review.remove(db=db, id=review.id)

        return content

    async def handle_content_update(
        self, db: AsyncSession, content: Any, has_content_update: bool
    ) -> Any:
        """处理内容更新后的审核状态

        Args:
            db: 数据库会话
            content: 内容对象
            has_content_update: 是否有内容更新

        Returns:
            更新后的内容对象
        """
        if not content.is_published or not has_content_update:
            return content

        # 已发布内容有更新，需要重新审核
        content.is_approved = False
        content.is_published = False  # 内容更新后变为未发布状态
        db.add(content)
        await db.commit()
        await db.refresh(content)

        # 更新审核记录
        review = await crud.review.get_by_content(
            db, content_type=self.content_type, content_id=content.id
        )
        if review:
            await crud.review.update(
                db=db,
                db_obj=review,
                obj_in={"status": "pending", "reviewer_id": None, "reviewed_at": None},
            )

        return content

    async def handle_tags_update(
        self, db: AsyncSession, content: Any, tags: list[str] | None = None
    ) -> Any:
        """处理内容标签的更新

        Args:
            db: 数据库会话
            content: 内容对象
            tags: 标签名列表

        Returns:
            更新后的内容对象
        """
        if tags is None:
            return content

        # 获取或创建标签
        tag_objects = await crud.tag.get_or_create_multi(db, names=tags, is_default=False)

        # 更新内容的标签关系
        content.tags = tag_objects
        db.add(content)
        await db.commit()
        await db.refresh(content, attribute_names=["tags"])

        return content

    async def delete_content(self, db: AsyncSession, content: Any) -> Any:
        """删除内容及其关联数据

        Args:
            db: 数据库会话
            content: 内容对象

        Returns:
            删除的内容对象
        """
        # 删除审核记录
        review = await crud.review.get_by_content(
            db, content_type=self.content_type, content_id=content.id
        )
        if review:
            await crud.review.remove(db=db, id=review.id)

        # 删除内容
        return await self.crud.remove(db=db, id=content.id)
