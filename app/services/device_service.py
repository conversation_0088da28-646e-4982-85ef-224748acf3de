"""设备管理服务 - 重构版本（集成TimeUtils）"""

from datetime import timedelta

from fastapi import Request
from jose import JWTError, jwt
from sqlalchemy.ext.asyncio import AsyncSession

from app import models
from app.config.auth_config import AuthConfig
from app.core.logging import logger
from app.crud.user_device import CRUDUserDevice
from app.exceptions.auth import (
    AuthErrorCodes,
    DeviceVerificationError,
)
from app.schemas.auth import DeviceTrustEvaluation, DeviceVerificationPayload
from app.services.device_fingerprint_service import DeviceFingerprintService
from app.services.device_info_parser import DeviceInfoParser
from app.services.interfaces.device_service_interface import IDeviceTrustService
from app.services.interfaces.token_service_interface import ITokenService
from app.utils.time_utils import TimeUtils, JWTTimeHelper


class DeviceTrustService(IDeviceTrustService):
    """设备信任管理服务 - 重构为单一职责"""

    def __init__(
        self,
        user_device_crud: CRUDUserDevice,
        token_service: ITokenService,
        auth_config: AuthConfig,
    ):
        self.fingerprint_service = DeviceFingerprintService()
        self.device_info_parser = DeviceInfoParser()
        self.token_service = token_service
        self.user_device_crud = user_device_crud
        self.auth_config = auth_config
        self.logger = logger

    # --- 核心公共接口 ---

    async def evaluate_device_trust(
        self, user: models.User, request: Request, db: AsyncSession
    ) -> DeviceTrustEvaluation:
        """评估设备信任状态"""
        # 获取或创建设备记录
        device, is_new = await self._get_or_create_device(db, user, request)

        # 评估信任状态
        is_trusted = self._is_device_trusted(device)
        requires_verification = self._requires_verification(device)

        # 分析风险因素
        risk_factors = self._analyze_risk_factors(device, is_new)

        return DeviceTrustEvaluation(
            device=device,
            is_trusted=is_trusted,
            requires_verification=requires_verification,
            trust_score=device.trust_score,
            risk_factors=risk_factors,
        )

    async def generate_device_verification_token(
        self, user_id: int, device_id: int, device_fingerprint: str, phone_number: str
    ) -> str:
        """生成设备验证令牌"""
        try:
            to_encode = {
                "sub": str(user_id),
                "user_id": user_id,
                "device_id": device_id,
                "device_fingerprint": device_fingerprint,
                "phone_number": phone_number,
                "type": "device_verification",
                "issued_at": TimeUtils.format_for_jwt(TimeUtils.get_utc_now()),
            }
            expire_minutes = self.auth_config.DEVICE_VERIFICATION_EXPIRE_MINUTES or 10
            expire = TimeUtils.calculate_expiry(expire_minutes)
            to_encode.update({"exp": TimeUtils.datetime_to_timestamp(expire)})

            encoded_jwt = jwt.encode(to_encode, self.auth_config.SECRET_KEY, algorithm="HS256")

            return encoded_jwt
        except Exception as e:
            self.logger.error(f"设备验证令牌生成失败 - 原始错误: {e}", exc_info=True)
            raise DeviceVerificationError(
                message="设备验证令牌生成失败",
                error_code=AuthErrorCodes.DEVICE_VERIFICATION_TOKEN_GENERATION_FAILED,
                status_code=500,
            ) from e

    async def verify_device_token(self, token: str) -> DeviceVerificationPayload:
        """验证设备验证令牌"""
        try:
            self.logger.info(f"开始验证设备验证令牌: {token[:20]}...{token[-10:] if len(token) > 30 else token}")
            payload = jwt.decode(token, self.auth_config.SECRET_KEY, algorithms=["HS256"])
            self.logger.info(f"设备验证令牌解码成功 - payload: {payload}")

            token_type = payload.get("type")
            self.logger.info(f"令牌类型: {token_type}")
            if token_type != "device_verification":
                self.logger.error(f"令牌类型不匹配 - 期望: device_verification, 实际: {token_type}")
                raise DeviceVerificationError(
                    message="令牌类型不匹配",
                    error_code=AuthErrorCodes.INVALID_DEVICE_TOKEN,
                    status_code=400,
                )

            user_id = payload.get("user_id")
            self.logger.info(f"用户ID: {user_id}")
            if not user_id:
                self.logger.error("令牌缺少用户ID信息")
                raise DeviceVerificationError(
                    message="令牌缺少用户ID信息",
                    error_code=AuthErrorCodes.INVALID_DEVICE_TOKEN,
                    status_code=400,
                )

            phone_number = payload.get("phone_number")
            self.logger.info(f"手机号: {phone_number}")
            if not phone_number:
                self.logger.error("令牌缺少电话号码信息")
                raise DeviceVerificationError(
                    message="令牌缺少电话号码信息",
                    error_code=AuthErrorCodes.INVALID_DEVICE_TOKEN,
                    status_code=400,
                )

            device_id = payload.get("device_id")
            issued_at = TimeUtils.parse_jwt_time(payload.get("issued_at"))
            expires_at = TimeUtils.timestamp_to_datetime(payload.get("exp"))
            
            self.logger.info(f"设备ID: {device_id}")
            self.logger.info(f"签发时间: {issued_at}")
            self.logger.info(f"过期时间: {expires_at}")
            self.logger.info(f"当前时间: {TimeUtils.get_utc_now()}")
            self.logger.info(f"令牌是否过期: {TimeUtils.is_expired(expires_at)}")

            return DeviceVerificationPayload(
                user_id=user_id,
                device_id=device_id,
                device_fingerprint=payload.get("device_fingerprint"),
                phone_number=phone_number,
                issued_at=issued_at,
                expires_at=expires_at,
            )

        except JWTError as e:
            self.logger.error(f"设备验证令牌解析失败 - 原始错误: {e}", exc_info=True)
            raise DeviceVerificationError(
                message="验证令牌已过期或无效",
                error_code=AuthErrorCodes.INVALID_DEVICE_TOKEN,
                status_code=400,
            ) from e

    async def trust_device(self, device_id: int, db: AsyncSession) -> models.UserDevice:
        """信任设备"""
        try:
            device = await self.user_device_crud.trust_device(db, device_id=device_id)
            return device
        except ValueError as e:
            raise DeviceVerificationError(
                message=f"设备信任失败: {str(e)}",
                error_code=AuthErrorCodes.DEVICE_NOT_FOUND,
                status_code=404,
            ) from e

    async def block_device(
        self, device_id: int, reason: str, db: AsyncSession
    ) -> models.UserDevice:
        """阻止设备"""
        try:
            device = await self.user_device_crud.block_device(
                db, device_id=device_id, reason=reason
            )
            return device
        except ValueError as e:
            raise DeviceVerificationError(
                message=f"设备阻止失败: {str(e)}",
                error_code=AuthErrorCodes.DEVICE_NOT_FOUND,
                status_code=404,
            ) from e

    async def unblock_device(self, device_id: int, db: AsyncSession) -> models.UserDevice:
        """解除设备阻止"""
        try:
            device = await self.user_device_crud.unblock_device(db, device_id=device_id)
            return device
        except ValueError as e:
            raise DeviceVerificationError(
                message=f"设备解除阻止失败: {str(e)}",
                error_code=AuthErrorCodes.DEVICE_NOT_FOUND,
                status_code=404,
            ) from e

    async def get_user_devices(
        self, user_id: int, db: AsyncSession, active_only: bool = True
    ) -> list[models.UserDevice]:
        """获取用户设备列表"""
        return await self.user_device_crud.get_user_devices(
            db, user_id=user_id, active_only=active_only
        )

    # --- 私有辅助方法 ---

    async def _get_or_create_device(
        self, db: AsyncSession, user: models.User, request: Request
    ) -> tuple[models.UserDevice, bool]:
        """获取或创建设备记录"""
        fingerprint = self.fingerprint_service.generate_fingerprint(request, user.id)

        device = await self.user_device_crud.get_by_fingerprint(
            db, user_id=user.id, fingerprint=fingerprint
        )

        if device:
            ip_address = self.device_info_parser.get_client_ip(request)
            await self.user_device_crud.update_login_info(
                db, device_id=device.id, ip_address=ip_address
            )
            return device, False
        else:
            device_info = self.device_info_parser.parse_device_info(request)
            ip_address = self.device_info_parser.get_client_ip(request)

            from app.schemas.user_device import UserDeviceCreate

            device_create = UserDeviceCreate(
                user_id=user.id,
                device_fingerprint=fingerprint,
                ip_address=ip_address,
                **device_info,
            )

            new_device = await self.user_device_crud.create(db, obj_in=device_create)
            return new_device, True

    def _is_device_trusted(self, device: models.UserDevice) -> bool:
        """判断设备是否可信"""
        if device.is_blocked:
            return False
        if device.is_trusted:
            return True
        if device.trust_score >= 60:
            return True
        return bool(device.is_frequent_device and device.trust_score >= 40)

    def _requires_verification(self, device: models.UserDevice) -> bool:
        """判断是否需要额外验证"""
        if self._is_device_trusted(device):
            return False
        if device.is_new_device:
            return True
        if device.trust_score < 40:
            return True
        return bool(
            device.last_login_at and TimeUtils.get_time_diff_minutes(TimeUtils.to_utc(device.last_login_at), TimeUtils.get_utc_now()) > 90 * 24 * 60
        )

    def _analyze_risk_factors(self, device: models.UserDevice, is_new: bool) -> list[str]:
        """分析设备风险因素"""
        risk_factors = []

        if is_new:
            risk_factors.append("新设备")

        if device.trust_score < 30:
            risk_factors.append("信任分数过低")

        if device.is_blocked:
            risk_factors.append("设备已被阻止")

        if device.last_login_at and TimeUtils.get_time_diff_minutes(TimeUtils.to_utc(device.last_login_at), TimeUtils.get_utc_now()) > 90 * 24 * 60:
            risk_factors.append("长期未使用")

        if device.login_count < 3:
            risk_factors.append("使用频率低")

        return risk_factors
