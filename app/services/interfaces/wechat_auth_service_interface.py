"""微信认证服务接口定义"""

from abc import ABC, abstractmethod

from sqlalchemy.ext.asyncio import AsyncSession

from app.schemas.auth import AuthenticationResult, QRCodeResponse
from app.schemas.wechat import WeChatScanStatus


class IWeChatAuthService(ABC):
    """微信认证服务接口"""

    @abstractmethod
    async def create_qr_code(self) -> QRCodeResponse:
        """创建用于微信扫码登录的二维码"""
        pass

    @abstractmethod
    async def check_scan_status(self, scene_str: str) -> WeChatScanStatus:
        """检查二维码扫描状态"""
        pass

    @abstractmethod
    async def bind_phone_number(
        self, openid: str, phone: str, verification_code: str, db: AsyncSession
    ) -> AuthenticationResult:
        """绑定手机号并完成认证"""
        pass
