"""用户管理服务接口定义"""

from abc import ABC, abstractmethod

from sqlalchemy.ext.asyncio import AsyncSession

from app import models, schemas


class IUserManagementService(ABC):
    """用户管理服务接口"""

    @abstractmethod
    async def get_user_by_id(self, user_id: int, db: AsyncSession) -> models.User | None:
        """根据ID获取用户"""
        pass

    @abstractmethod
    async def get_user_by_phone(self, phone: str, db: AsyncSession) -> models.User | None:
        """根据手机号获取用户"""
        pass

    @abstractmethod
    async def get_user_by_openid(self, openid: str, db: AsyncSession) -> models.User | None:
        """根据 openid 获取用户"""
        pass

    @abstractmethod
    async def get_or_create_user_by_phone(
        self, phone: str, db: AsyncSession
    ) -> tuple[models.User, bool]:
        """根据手机号获取或创建用户"""
        pass

    @abstractmethod
    async def create_user_with_wechat_info(
        self, user_create: schemas.UserCreate, db: AsyncSession
    ) -> models.User:
        """使用微信信息创建用户"""
        pass

    @abstractmethod
    async def update_user(
        self, user_id: int, user_update: schemas.UserUpdate, db: AsyncSession
    ) -> models.User | None:
        """更新用户信息"""
        pass

    @abstractmethod
    async def update_last_login(self, user_id: int, db: AsyncSession) -> None:
        """更新用户最后登录时间"""
        pass
