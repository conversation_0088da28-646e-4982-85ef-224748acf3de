"""设备信任服务接口定义"""

from abc import ABC, abstractmethod
from typing import Any

from fastapi import Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.models import User
from app.schemas.auth import DeviceTrustEvaluation


class IDeviceTrustService(ABC):
    """设备信任服务接口"""

    @abstractmethod
    async def evaluate_device_trust(
        self, user: User, request: Request, db: AsyncSession
    ) -> DeviceTrustEvaluation:
        """评估设备信任度"""
        pass

    @abstractmethod
    async def generate_device_verification_token(
        self, user_id: int, device_fingerprint: str, phone_number: str
    ) -> str:
        """生成设备验证令牌"""
        pass

    @abstractmethod
    async def verify_device_token(self, token: str) -> dict[str, Any] | None:
        """验证设备令牌"""
        pass
