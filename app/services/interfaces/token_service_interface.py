"""Token服务接口定义"""

from abc import ABC, abstractmethod
from datetime import timedelta


class ITokenService(ABC):
    """Token服务接口"""

    @staticmethod
    @abstractmethod
    async def create_token(
        username: str,
        device_id: int | None = None,
        expires_delta: timedelta | None = None,
        token_type: str = "access",
    ) -> str:
        """创建并存储 token"""
        pass

    @staticmethod
    @abstractmethod
    async def verify_token(token: str) -> dict | None:
        """验证 token 是否有效"""
        pass

    @staticmethod
    @abstractmethod
    async def revoke_token(token: str) -> bool:
        """撤销 token"""
        pass

    @staticmethod
    @abstractmethod
    async def revoke_user_tokens(username: str) -> int:
        """撤销用户的所有 token"""
        pass

    @staticmethod
    @abstractmethod
    async def revoke_device_token(username: str, device_id: int) -> bool:
        """撤销设备的 token"""
        pass

    @staticmethod
    @abstractmethod
    async def get_user_active_tokens(username: str) -> list[dict]:
        """获取用户的活跃 token 列表"""
        pass

    @abstractmethod
    async def create_access_token(
        self, data: dict, expires_delta: timedelta | None = None, device_id: int | None = None
    ) -> str:
        """创建访问令牌"""
        pass

    @abstractmethod
    async def refresh_token(self, refresh_token: str) -> str | None:
        """刷新令牌"""
        pass

    @abstractmethod
    async def validate_token(self, token: str) -> dict | None:
        """验证令牌"""
        pass
