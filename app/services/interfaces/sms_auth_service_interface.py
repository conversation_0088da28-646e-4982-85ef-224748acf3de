"""SMS认证服务接口定义"""

from abc import ABC, abstractmethod

from fastapi import Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.sms_template import SmsTemplate
from app.schemas.auth import AuthenticationResult, SMSVerificationResponse


class ISMSAuthService(ABC):
    """SMS认证服务接口"""

    @abstractmethod
    async def send_verification_code(
        self, phone_number: str, template_type: SmsTemplate
    ) -> SMSVerificationResponse:
        """发送短信验证码"""
        pass

    @abstractmethod
    async def verify_code_and_authenticate(
        self,
        phone_number: str,
        code: str,
        template_type: SmsTemplate,
        request: Request,
        db: AsyncSession,
    ) -> AuthenticationResult:
        """验证验证码并完成认证"""
        pass

    @abstractmethod
    async def verify_device_code_and_complete_auth(
        self, verification_token: str, verification_code: str, db: AsyncSession
    ) -> AuthenticationResult:
        """验证设备验证码并完成认证"""
        pass
