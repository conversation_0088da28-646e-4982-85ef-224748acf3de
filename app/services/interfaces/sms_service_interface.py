"""短信服务接口"""

from abc import ABC, abstractmethod

from app.core.sms_template import SmsTemplate


class ISmsService(ABC):
    """
    短信服务接口，定义了发送和验证短信验证码的核心功能。
    """

    @abstractmethod
    async def send_verification_code(self, phone: str, template_type: SmsTemplate) -> None:
        """
        发送验证码。

        Args:
            phone: 手机号。
            template_type: 短信模板类型。
        """
        pass

    @abstractmethod
    async def verify_code(self, phone: str, code: str, template_type: SmsTemplate) -> bool:
        """
        验证验证码。

        Args:
            phone: 手机号。
            code: 验证码。
            template_type: 短信模板类型。

        Returns:
            bool: 验证是否成功。
        """
        pass
