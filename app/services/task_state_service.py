from datetime import UTC, datetime

from app.db.redis import hash_get, hash_set


class TaskStateService:
    """
    一个用于管理和持久化异步任务状态的服务。
    主要用于记录增量任务的最后成功运行时间戳。
    """

    _redis_hash_key = "task_run_timestamps"

    async def get_last_run_timestamp(self, task_name: str) -> datetime | None:
        """
        从Redis中获取指定任务的最后成功运行时间戳。

        :param task_name: 任务的唯一名称。
        :return: 一个有时区的datetime对象，如果不存在则返回None。
        """
        timestamp_str = await hash_get(self._redis_hash_key, task_name)
        if timestamp_str:
            # 假设存储的是ISO 8601格式的字符串
            return datetime.fromisoformat(timestamp_str.decode("utf-8"))
        return None

    async def set_last_run_timestamp(self, task_name: str, timestamp: datetime):
        """
        在Redis中设置指定任务的最后成功运行时间戳。

        :param task_name: 任务的唯一名称。
        :param timestamp: 要设置的时间戳 (应为有时区的datetime对象)。
        """
        # 确保时间戳是UTC时区，并转换为ISO 8601格式字符串
        if timestamp.tzinfo is None:
            timestamp = timestamp.replace(tzinfo=UTC)

        await hash_set(self._redis_hash_key, task_name, timestamp.isoformat())
