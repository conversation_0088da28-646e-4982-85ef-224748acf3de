"""
Scratch项目统计服务

专门处理Scratch项目的点赞和收藏统计，支持缓存管理和批处理操作。
"""

import asyncio
from typing import Any, Literal

from sqlalchemy.ext.asyncio import AsyncSession

from app import crud
from app.config import settings
from app.core.logging import logger
from app.db.redis import (
    execute_pipeline,
    set_key,
)

ScratchContentType = Literal["scratch"]


class ScratchStatsService:
    """
    Scratch项目的统计服务类，提供高性能的统计数据管理和缓存机制。
    """

    def __init__(self):
        self.cache_expire_seconds = settings.CONTENT_STATS_CACHE_EXPIRE_SECONDS

    # --- Key Generation Methods ---

    def _get_key(self, content_id: int, metric: str) -> str:
        """通用键生成器，使用scratch前缀"""
        return f"scratch:{content_id}:{metric}"

    def _like_count_key(self, content_id: int) -> str:
        return self._get_key(content_id, "like_count")

    def _likers_set_key(self, content_id: int) -> str:
        return self._get_key(content_id, "likers")

    def _favorite_count_key(self, content_id: int) -> str:
        return self._get_key(content_id, "favorite_count")

    def _favoriters_set_key(self, content_id: int) -> str:
        return self._get_key(content_id, "favoriters")

    def _visit_count_key(self, content_id: int) -> str:
        return self._get_key(content_id, "visit_count")

    def _adapt_count_key(self, content_id: int) -> str:
        return self._get_key(content_id, "adapt_count")

    # --- Stats Retrieval Methods ---

    async def get_stats(
        self,
        db: AsyncSession,
        content_id: int,
        user_id: int | None = None,
    ) -> dict[str, Any]:
        """获取单个Scratch项目的完整统计信息"""
        stats_dict = await self.batch_get_stats(db, [content_id], user_id)
        return stats_dict.get(content_id, {})

    async def batch_get_stats(
        self,
        db: AsyncSession,
        content_ids: list[int],
        user_id: int | None = None,
    ) -> dict[int, dict[str, Any]]:
        """
        批量获取多个Scratch项目的统计信息，并处理缓存回填。
        """
        if not content_ids:
            return {}

        commands = []
        for content_id in content_ids:
            commands.extend(
                [
                    ("get", self._like_count_key(content_id)),
                    ("get", self._favorite_count_key(content_id)),
                    ("get", self._visit_count_key(content_id)),
                    ("get", self._adapt_count_key(content_id)),
                ]
            )
            if user_id:
                commands.extend(
                    [
                        ("sismember", self._likers_set_key(content_id), user_id),
                        ("sismember", self._favoriters_set_key(content_id), user_id),
                    ]
                )

        try:
            results = await execute_pipeline(commands, write=False)
        except Exception as e:
            logger.error(f"批量获取Scratch统计信息时 Redis 操作失败: {e}")
            results = []
            missing_items = content_ids
            stats = {item: {} for item in content_ids}

        if not results:
            pass
        else:
            stats = {}
            missing_items = []
        step = 6 if user_id else 4

        for i, content_id in enumerate(content_ids):
            chunk = results[i * step : (i + 1) * step]
            like_count, favorite_count, visit_count, adapt_count = (
                int(c) if c is not None else None for c in chunk[:4]
            )

            if (
                like_count is None
                or favorite_count is None
                or visit_count is None
                or adapt_count is None
            ):
                missing_items.append(content_id)
                stats[content_id] = {}
                continue

            is_liked = bool(chunk[4]) if user_id and len(chunk) > 4 else False
            is_favorited = bool(chunk[5]) if user_id and len(chunk) > 5 else False

            stats[content_id] = {
                "like_count": like_count,
                "favorite_count": favorite_count,
                "visit_count": visit_count,
                "adapt_count": adapt_count,
                "is_liked_by_user": is_liked,
                "is_favorited_by_user": is_favorited,
            }

        if missing_items:
            logger.info(f"缓存未命中，回填 {len(missing_items)} 个Scratch项目的统计信息")
            db_stats = await self._backfill_from_db(db, missing_items, user_id)
            stats.update(db_stats)

        return stats

    async def _backfill_from_db(
        self,
        db: AsyncSession,
        content_ids: list[int],
        user_id: int | None,
    ) -> dict[int, dict[str, Any]]:
        """从数据库获取统计信息并回填到缓存中"""
        backfilled_stats = {}

        for content_id in content_ids:
            lock_key = f"lock:backfill:scratch:{content_id}"

            if await set_key(lock_key, "1", expire=10):
                try:
                    logger.info(f"成功获取锁，开始为 scratch:{content_id} 回填缓存")
                    # 使用已有的CRUD方法获取统计信息
                    like_stats = await crud.like.get_content_likes_batch(
                        db=db, content_items=[("scratch", content_id)], user_id=user_id
                    )
                    favorite_stats = await crud.favorite.get_content_favorites_batch(
                        db=db, content_items=[("scratch", content_id)], user_id=user_id
                    )

                    l_stats_raw = like_stats.get(("scratch", content_id), {})
                    f_stats_raw = favorite_stats.get(("scratch", content_id), {})

                    like_count = l_stats_raw.get("like_count", 0) or 0
                    is_liked = l_stats_raw.get("is_liked", False)
                    favorite_count = f_stats_raw.get("favorite_count", 0) or 0
                    is_favorited = f_stats_raw.get("is_favorited", False)

                    # 获取访问计数，从scratch_products表直接获取
                    project = await crud.scratch_product.get(db=db, id=content_id)
                    visit_count = project.visit_count if project else 0
                    adapt_count = project.adapt_count if project else 0

                    final_stats = {
                        "like_count": like_count,
                        "favorite_count": favorite_count,
                        "visit_count": visit_count,
                        "adapt_count": adapt_count,
                        "is_liked_by_user": is_liked,
                        "is_favorited_by_user": is_favorited,
                    }
                    backfilled_stats[content_id] = final_stats

                    # 回填缓存
                    backfill_commands = [
                        ("set", self._like_count_key(content_id), like_count),
                        ("set", self._favorite_count_key(content_id), favorite_count),
                        ("set", self._visit_count_key(content_id), visit_count),
                        ("set", self._adapt_count_key(content_id), adapt_count),
                        ("expire", self._like_count_key(content_id), self.cache_expire_seconds),
                        ("expire", self._favorite_count_key(content_id), self.cache_expire_seconds),
                        ("expire", self._visit_count_key(content_id), self.cache_expire_seconds),
                        ("expire", self._adapt_count_key(content_id), self.cache_expire_seconds),
                    ]
                    await execute_pipeline(backfill_commands, write=True)

                    # 设置用户点赞/收藏状态进集合
                    if user_id:
                        user_status_commands = []
                        if is_liked:
                            user_status_commands.append(
                                ("sadd", self._likers_set_key(content_id), user_id)
                            )
                        if is_favorited:
                            user_status_commands.append(
                                ("sadd", self._favoriters_set_key(content_id), user_id)
                            )

                        if user_status_commands:
                            user_status_commands.extend(
                                [
                                    (
                                        "expire",
                                        self._likers_set_key(content_id),
                                        self.cache_expire_seconds,
                                    ),
                                    (
                                        "expire",
                                        self._favoriters_set_key(content_id),
                                        self.cache_expire_seconds,
                                    ),
                                ]
                            )
                            await execute_pipeline(user_status_commands, write=True)

                except Exception as e:
                    logger.error(f"回填 scratch:{content_id} 统计信息时发生错误: {e}")
                finally:
                    pass
            else:
                logger.info(f"未能获取锁 scratch:{content_id}，等待后重试")
                await asyncio.sleep(0.1)
                stats = await self.batch_get_stats(db, [content_id], user_id)
                backfilled_stats.update(stats)

        return backfilled_stats

    # --- Update Methods ---

    async def update_like_status(self, content_id: int, user_id: int, is_liked: bool) -> None:
        """原子化地更新Scratch项目的点赞状态"""
        likers_key = self._likers_set_key(content_id)
        count_key = self._like_count_key(content_id)

        if is_liked:
            commands = [("sadd", likers_key, user_id), ("incr", count_key)]
        else:
            commands = [("srem", likers_key, user_id), ("decr", count_key)]

        try:
            await execute_pipeline(commands, write=True)
            # 设置过期时间
            await execute_pipeline(
                [
                    ("expire", likers_key, self.cache_expire_seconds),
                    ("expire", count_key, self.cache_expire_seconds),
                ],
                write=True,
            )
        except Exception as e:
            logger.error(f"更新Scratch项目点赞状态时 Redis 操作失败: {e}")

    async def update_favorite_status(
        self, content_id: int, user_id: int, is_favorited: bool
    ) -> None:
        """原子化地更新Scratch项目的收藏状态"""
        favoriters_key = self._favoriters_set_key(content_id)
        count_key = self._favorite_count_key(content_id)

        if is_favorited:
            commands = [("sadd", favoriters_key, user_id), ("incr", count_key)]
        else:
            commands = [("srem", favoriters_key, user_id), ("decr", count_key)]

        try:
            await execute_pipeline(commands, write=True)
            # 设置过期时间
            await execute_pipeline(
                [
                    ("expire", favoriters_key, self.cache_expire_seconds),
                    ("expire", count_key, self.cache_expire_seconds),
                ],
                write=True,
            )
        except Exception as e:
            logger.error(f"更新Scratch项目收藏状态时 Redis 操作失败: {e}")

    async def increment_visit_count(self, content_id: int) -> None:
        """原子化地增加Scratch项目的访问次数"""
        key = self._visit_count_key(content_id)
        try:
            await execute_pipeline(
                [
                    ("incr", key),
                    ("expire", key, self.cache_expire_seconds),
                ],
                write=True,
            )
        except Exception as e:
            logger.error(f"增加Scratch项目访问次数时 Redis 操作失败: {e}")

    async def increment_adapt_count(self, content_id: int) -> None:
        """原子化地增加Scratch项目的改编次数"""
        key = self._adapt_count_key(content_id)
        try:
            await execute_pipeline(
                [
                    ("incr", key),
                    ("expire", key, self.cache_expire_seconds),
                ],
                write=True,
            )
        except Exception as e:
            logger.error(f"增加Scratch项目改编次数时 Redis 操作失败: {e}")

    # --- User Interaction Methods ---

    async def toggle_like(
        self,
        db: AsyncSession,
        user_id: int,
        content_id: int,
    ) -> tuple[bool, bool]:
        """
        切换Scratch项目的点赞状态

        Returns:
            tuple: (like_created_or_reactivated, is_liked_now)
        """
        # 检查项目是否存在
        project = await crud.scratch_product.get(db=db, id=content_id)
        if not project:
            raise Exception("Scratch项目不存在")

        # 检查用户是否已点赞
        existing_like = await crud.like.get_by_user_and_content(
            db=db, user_id=user_id, content_type="scratch", content_id=content_id
        )

        is_liked_now = False
        like_created_or_reactivated = False

        if existing_like:
            # 切换激活状态
            existing_like.is_active = not existing_like.is_active
            existing_like.updated_at = None  # 使用自动更新
            db.add(existing_like)
            is_liked_now = existing_like.is_active
            like_created_or_reactivated = is_liked_now
        else:
            # 创建新的点赞记录
            from app.models.like import Like

            new_like = Like(
                user_id=user_id,
                content_type="scratch",
                content_id=content_id,
                is_active=True,
            )
            db.add(new_like)
            is_liked_now = True
            like_created_or_reactivated = True

        # 创建统计更新事件（使用outbox模式）
        from app.models.outbox import OutboxMessage

        stats_event = OutboxMessage(
            topic="scratch.stats.update",
            payload={
                "entity_type": "scratch",
                "entity_id": content_id,
                "field_name": "like_count",
                "value_change": 1 if like_created_or_reactivated else -1,
            },
        )
        db.add(stats_event)

        return like_created_or_reactivated, is_liked_now

    async def toggle_favorite(
        self,
        db: AsyncSession,
        user_id: int,
        content_id: int,
        note: str | None = None,
    ) -> tuple[bool, bool]:
        """
        切换Scratch项目的收藏状态

        Returns:
            tuple: (favorite_created_or_reactivated, is_favorited_now)
        """
        # 检查项目是否存在
        project = await crud.scratch_product.get(db=db, id=content_id)
        if not project:
            raise Exception("Scratch项目不存在")

        # 检查用户是否已收藏
        existing_favorite = await crud.favorite.get_by_user_and_content(
            db=db, user_id=user_id, content_type="scratch", content_id=content_id
        )

        is_favorited_now = False
        favorite_created_or_reactivated = False

        if existing_favorite:
            # 切换激活状态
            existing_favorite.is_active = not existing_favorite.is_active
            existing_favorite.updated_at = None  # 使用自动更新
            if note is not None:
                existing_favorite.note = note
            db.add(existing_favorite)
            is_favorited_now = existing_favorite.is_active
            favorite_created_or_reactivated = is_favorited_now
        else:
            # 创建新的收藏记录
            from app.models.favorite import Favorite

            new_favorite = Favorite(
                user_id=user_id,
                content_type="scratch",
                content_id=content_id,
                note=note,
                is_active=True,
            )
            db.add(new_favorite)
            is_favorited_now = True
            favorite_created_or_reactivated = True

        # 创建统计更新事件
        from app.models.outbox import OutboxMessage

        stats_event = OutboxMessage(
            topic="scratch.stats.update",
            payload={
                "entity_type": "scratch",
                "entity_id": content_id,
                "field_name": "favorite_count",
                "value_change": 1 if favorite_created_or_reactivated else -1,
            },
        )
        db.add(stats_event)

        return favorite_created_or_reactivated, is_favorited_now

    # --- 新增的辅助方法用于主服务调用 --- #

    async def _get_like_stats_from_db(
        self,
        db: AsyncSession,
        content_ids: list[int],
        user_id: int | None,
    ) -> dict[tuple[str, int], dict[str, Any]]:
        """获取Scratch项目的点赞统计信息（供主服务调用）"""
        from app import crud

        # 按照原有的内容类型格式返回
        content_items = [("scratch", content_id) for content_id in content_ids]
        return await crud.like.get_content_likes_batch(
            db=db, content_items=content_items, user_id=user_id
        )

    async def _get_favorite_stats_from_db(
        self,
        db: AsyncSession,
        content_ids: list[int],
        user_id: int | None,
    ) -> dict[tuple[str, int], dict[str, Any]]:
        """获取Scratch项目的收藏统计信息（供主服务调用）"""
        from app import crud

        # 按照原有的内容类型格式返回
        content_items = [("scratch", content_id) for content_id in content_ids]
        return await crud.favorite.get_content_favorites_batch(
            db=db, content_items=content_items, user_id=user_id
        )
