"""通知服务层"""

from typing import Any

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import logger
from app.crud.notification import notification as crud_notification
from app.notifications.models import NotificationPriority, NotificationStatus, NotificationType
from app.notifications.schemas import (
    NotificationCreate,
    NotificationListResponse,
    NotificationResponse,
    UnreadCountResponse,
)


class NotificationService:
    """通知服务类"""

    def __init__(self):
        self.logger = logger

    async def create_notification(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        notification_type: NotificationType,
        title: str,
        message: str,
        priority: NotificationPriority = NotificationPriority.NORMAL,
        data: dict[str, Any] | None = None,
        action_url: str | None = None,
        expires_in_hours: int | None = None,
    ) -> NotificationResponse:
        """创建通知"""
        try:
            notification_in = NotificationCreate(
                user_id=user_id,
                type=notification_type,
                priority=priority,
                title=title,
                message=message,
                data=data,
                action_url=action_url,
                expires_in_hours=expires_in_hours,
            )

            notification = await crud_notification.create_notification(
                db=db, obj_in=notification_in
            )

            self.logger.info(
                f"通知创建成功 - 用户ID: {user_id}, 类型: {notification_type.value}, 标题: {title}"
            )

            return NotificationResponse.model_validate(notification)

        except Exception as e:
            self.logger.error(f"创建通知失败: {str(e)}")
            raise

    async def get_user_notifications(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        status: NotificationStatus | None = None,
        limit: int = 20,
        offset: int = 0,
    ) -> NotificationListResponse:
        """获取用户通知列表"""
        try:
            notifications, total = await crud_notification.get_user_notifications(
                db=db, user_id=user_id, status=status, limit=limit, offset=offset
            )

            unread_count = await crud_notification.get_unread_count(db=db, user_id=user_id)

            notification_responses = [
                NotificationResponse.model_validate(notification) for notification in notifications
            ]

            return NotificationListResponse(
                notifications=notification_responses, total=total, unread_count=unread_count
            )

        except Exception as e:
            self.logger.error(f"获取用户通知列表失败: {str(e)}")
            raise

    async def get_unread_count(self, db: AsyncSession, *, user_id: int) -> UnreadCountResponse:
        """获取未读通知数量"""
        try:
            count = await crud_notification.get_unread_count(db=db, user_id=user_id)
            return UnreadCountResponse(unread_count=count)

        except Exception as e:
            self.logger.error(f"获取未读通知数量失败: {str(e)}")
            raise

    async def mark_as_read(
        self, db: AsyncSession, *, notification_id: int, user_id: int
    ) -> NotificationResponse | None:
        """标记通知为已读"""
        try:
            notification = await crud_notification.mark_as_read(
                db=db, notification_id=notification_id, user_id=user_id
            )

            if notification:
                self.logger.info(f"通知标记为已读 - 通知ID: {notification_id}, 用户ID: {user_id}")
                return NotificationResponse.model_validate(notification)

            return None

        except Exception as e:
            self.logger.error(f"标记通知为已读失败: {str(e)}")
            raise

    async def mark_all_as_read(self, db: AsyncSession, *, user_id: int) -> int:
        """标记所有通知为已读"""
        try:
            count = await crud_notification.mark_all_as_read(db=db, user_id=user_id)

            self.logger.info(f"批量标记通知为已读 - 用户ID: {user_id}, 数量: {count}")

            return count

        except Exception as e:
            self.logger.error(f"批量标记通知为已读失败: {str(e)}")
            raise

    async def delete_notification(
        self, db: AsyncSession, *, notification_id: int, user_id: int
    ) -> bool:
        """删除通知"""
        try:
            success = await crud_notification.delete_notification(
                db=db, notification_id=notification_id, user_id=user_id
            )

            if success:
                self.logger.info(f"通知删除成功 - 通知ID: {notification_id}, 用户ID: {user_id}")

            return success

        except Exception as e:
            self.logger.error(f"删除通知失败: {str(e)}")
            raise

    # 便捷方法：创建特定类型的通知
    async def create_task_complete_notification(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        task_name: str,
        task_id: int | None = None,
        action_url: str | None = None,
    ) -> NotificationResponse:
        """创建任务完成通知"""
        return await self.create_notification(
            db=db,
            user_id=user_id,
            notification_type=NotificationType.TASK_COMPLETE,
            title="任务完成",
            message=f"您的任务「{task_name}」已完成",
            priority=NotificationPriority.NORMAL,
            data={"task_id": task_id} if task_id else None,
            action_url=action_url,
        )

    async def create_content_recommendation_notification(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        content_title: str,
        content_type: str,
        content_id: int,
        action_url: str | None = None,
    ) -> NotificationResponse:
        """创建内容推荐通知"""
        return await self.create_notification(
            db=db,
            user_id=user_id,
            notification_type=NotificationType.CONTENT_RECOMMENDATION,
            title="内容推荐",
            message=f"为您推荐了新的{content_type}：{content_title}",
            priority=NotificationPriority.LOW,
            data={"content_type": content_type, "content_id": content_id},
            action_url=action_url,
            expires_in_hours=72,  # 3天后过期
        )

    async def create_system_notification(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        title: str,
        message: str,
        priority: NotificationPriority = NotificationPriority.HIGH,
        action_url: str | None = None,
    ) -> NotificationResponse:
        """创建系统通知"""
        return await self.create_notification(
            db=db,
            user_id=user_id,
            notification_type=NotificationType.SYSTEM_UPDATE,
            title=title,
            message=message,
            priority=priority,
            action_url=action_url,
            expires_in_hours=168,  # 7天后过期
        )

    async def broadcast_notification_to_all_users(
        self,
        db: AsyncSession,
        *,
        title: str,
        message: str,
        notification_type: NotificationType = NotificationType.SYSTEM_UPDATE,
        priority: NotificationPriority = NotificationPriority.HIGH,
        data: dict[str, Any] | None = None,
        action_url: str | None = None,
        expires_in_hours: int | None = 168,
        target_user_type: str = "all",
    ):
        """
        广播通知给所有用户（混合方案）

        Args:
            db: 数据库会话
            title: 通知标题
            message: 通知内容
            notification_type: 通知类型
            priority: 优先级
            data: 附加数据
            action_url: 操作链接
            expires_in_hours: 过期时间（小时）
            target_user_type: 目标用户类型

        Returns:
            BroadcastResponse: 广播结果统计
        """
        try:
            # 1. 获取目标用户总数
            total_users = await self._get_target_users_count(db, target_user_type)

            if total_users == 0:
                self.logger.warning(f"没有找到符合条件的用户 - 类型: {target_user_type}")
                return {
                    "message": "没有找到符合条件的用户",
                    "total_users": 0,
                    "notifications_created": 0,
                    "online_users_notified": 0,
                }

            # 2. 创建广播任务（异步处理）
            from app.tasks.notification_tasks import broadcast_notification_task

            task_data = {
                "title": title,
                "message": message,
                "notification_type": notification_type.value,
                "priority": priority.value,
                "data": data,
                "action_url": action_url,
                "expires_in_hours": expires_in_hours,
                "target_user_type": target_user_type,
            }

            # 启动异步任务
            broadcast_notification_task.delay(task_data)

            # 3. 立即向在线用户推送WebSocket消息
            from app.services.websocket_manager import websocket_manager

            online_users = websocket_manager.get_online_users()
            online_count = len(online_users)

            # 向在线用户发送实时通知
            await websocket_manager.broadcast_system_message(
                title=title,
                message=message,
                data={
                    "type": notification_type.value,
                    "priority": priority.value,
                    "action_url": action_url,
                    **(data or {}),
                },
            )

            self.logger.info(
                f"广播通知已启动 - 标题: {title}, 目标用户: {total_users}, 在线用户: {online_count}"
            )

            return {
                "message": f"广播通知已启动，将为 {total_users} 个用户创建通知",
                "total_users": total_users,
                "notifications_created": 0,  # 异步创建，暂时为0
                "online_users_notified": online_count,
            }

        except Exception as e:
            self.logger.error(f"广播通知失败: {str(e)}")
            raise

    async def _get_target_users_count(self, db: AsyncSession, target_user_type: str) -> int:
        """获取目标用户数量"""
        try:
            from sqlalchemy import func, select

            from app.models.user import User

            if target_user_type == "all":
                # 所有活跃用户
                stmt = select(func.count(User.id)).where(
                    User.is_active == True, User.is_deleted == False
                )
            elif target_user_type == "active":
                # 最近活跃的用户（可以根据需要定义活跃标准）
                stmt = select(func.count(User.id)).where(
                    User.is_active == True,
                    User.is_deleted == False,
                    # 可以添加最后登录时间等条件
                )
            else:
                # 默认为所有活跃用户
                stmt = select(func.count(User.id)).where(
                    User.is_active == True, User.is_deleted == False
                )

            result = await db.execute(stmt)
            return result.scalar() or 0

        except Exception as e:
            self.logger.error(f"获取目标用户数量失败: {str(e)}")
            return 0
