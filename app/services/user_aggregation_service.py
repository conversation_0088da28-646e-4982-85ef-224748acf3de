from sqlalchemy.ext.asyncio import AsyncSession

from app import schemas
from app.services.user_cache_service import UserCacheService
from app.services.user_stats_service import UserStatsCacheService


class UserAggregationService:
    def __init__(
        self,
        user_cache_service: UserCacheService,
        user_stats_service: UserStatsCacheService,
    ):
        self.user_cache_service = user_cache_service
        self.user_stats_service = user_stats_service

    async def get_user_profile(
        self, db: AsyncSession, user_id: int
    ) -> schemas.UserAggregated | None:
        """
        获取完整的用户个人资料，聚合了核心信息和统计数据。
        """
        # 1. 获取用户核心信息
        user_base = await self.user_cache_service.get_entity(db, entity_id=user_id)
        if not user_base:
            return None

        # 2. 获取用户统计信息
        user_stats = await self.user_stats_service.get_user_stats(db, user_id=user_id)

        # 如果找不到统计信息，则创建一个空的默认实例
        if user_stats is None:
            user_stats = schemas.UserStats(user_id=user_id)

        # 3. 聚合数据
        user_aggregated_data = user_base.model_dump()
        user_aggregated_data["stats"] = user_stats

        return schemas.UserAggregated(**user_aggregated_data)


# 全局实例将在 service_factory 中创建和管理
