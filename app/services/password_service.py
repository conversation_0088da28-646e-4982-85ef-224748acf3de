"""密码哈希服务模块

提供密码哈希和验证功能，替换原有的AuthService中的密码处理功能
"""

from passlib.context import CryptContext

# 密码上下文配置
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class PasswordHashService:
    """密码哈希服务类

    提供密码哈希和验证的静态方法，用于替换原有的AuthService引用
    """

    @staticmethod
    def hash_password(password: str) -> str:
        """对密码进行哈希处理

        Args:
            password: 明文密码

        Returns:
            str: 哈希后的密码
        """
        return pwd_context.hash(password)

    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """验证密码是否正确

        Args:
            plain_password: 明文密码
            hashed_password: 哈希后的密码

        Returns:
            bool: 密码是否匹配
        """
        return pwd_context.verify(plain_password, hashed_password)

    @staticmethod
    def get_password_hash(password: str) -> str:
        """获取密码哈希值（兼容性方法）

        Args:
            password: 明文密码

        Returns:
            str: 哈希后的密码
        """
        return PasswordHashService.hash_password(password)
