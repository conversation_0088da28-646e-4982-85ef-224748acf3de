"""视频相关异常定义"""

from typing import Optional
from app.core.response_wrapper import ResponseCode
from app.exceptions.base import BusinessException


class VideoError(BusinessException):
    """视频基础异常类"""
    
    def __init__(
        self, 
        message: str, 
        response_code: ResponseCode,
        details: Optional[dict] = None
    ):
        super().__init__(message, response_code, details=details)


class VideoNotFoundError(VideoError):
    """视频不存在异常"""
    
    def __init__(self, video_id: Optional[int] = None):
        details = {"video_id": video_id} if video_id else {}
        super().__init__(
            message="视频不存在",
            response_code=ResponseCode.VIDEO_NOT_FOUND,
            details=details
        )


class VideoPermissionDeniedError(VideoError):
    """视频权限异常"""
    
    def __init__(self, video_id: Optional[int] = None, user_id: Optional[int] = None):
        details = {}
        if video_id:
            details["video_id"] = video_id
        if user_id:
            details["user_id"] = user_id
            
        super().__init__(
            message="无权操作此视频",
            response_code=ResponseCode.VIDEO_PERMISSION_DENIED,
            details=details
        )


class VideoFolderNotFoundError(VideoError):
    """视频文件夹不存在异常"""
    
    def __init__(self, folder_id: Optional[int] = None):
        details = {"folder_id": folder_id} if folder_id else {}
        super().__init__(
            message="视频文件夹不存在",
            response_code=ResponseCode.VIDEO_FOLDER_NOT_FOUND,
            details=details
        )