"""业务异常基类定义"""

from typing import Any, Optional
from app.core.response_wrapper import ResponseCode


class BusinessException(Exception):
    """业务异常基类"""
    
    def __init__(
        self, 
        message: str, 
        response_code: ResponseCode,
        data: Any = None,
        details: Optional[dict] = None
    ):
        self.message = message
        self.response_code = response_code
        self.data = data
        self.details = details or {}
        super().__init__(self.message)
    
    @property
    def status_code(self) -> int:
        """获取HTTP状态码"""
        return self.response_code.status_code
    
    @property
    def error_code(self) -> str:
        """获取错误码"""
        return self.response_code.error_code
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "message": self.message,
            "error_code": self.error_code,
            "status_code": self.status_code,
            "data": self.data,
            "details": self.details
        }