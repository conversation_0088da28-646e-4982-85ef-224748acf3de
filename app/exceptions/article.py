"""文章相关异常定义"""

from typing import Optional
from app.core.response_wrapper import ResponseCode
from app.exceptions.base import BusinessException


class ArticleError(BusinessException):
    """文章基础异常类"""
    
    def __init__(
        self, 
        message: str, 
        response_code: ResponseCode,
        details: Optional[dict] = None
    ):
        super().__init__(message, response_code, details=details)


class ArticleNotFoundError(ArticleError):
    """文章不存在异常"""
    
    def __init__(self, article_id: Optional[int] = None):
        details = {"article_id": article_id} if article_id else {}
        super().__init__(
            message="文章不存在",
            response_code=ResponseCode.ARTICLE_NOT_FOUND,
            details=details
        )


class ArticlePermissionDeniedError(ArticleError):
    """文章权限异常"""
    
    def __init__(self, article_id: Optional[int] = None, user_id: Optional[int] = None):
        details = {}
        if article_id:
            details["article_id"] = article_id
        if user_id:
            details["user_id"] = user_id
            
        super().__init__(
            message="无权操作此文章",
            response_code=ResponseCode.ARTICLE_PERMISSION_DENIED,
            details=details
        )


class ArticleAlreadyExistsError(ArticleError):
    """文章已存在异常"""
    
    def __init__(self, title: Optional[str] = None):
        details = {"title": title} if title else {}
        super().__init__(
            message="具有相同标题的文章已存在",
            response_code=ResponseCode.ARTICLE_ALREADY_EXISTS,
            details=details
        )