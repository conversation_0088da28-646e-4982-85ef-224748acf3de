"""认证相关异常定义"""

from app.core.response_wrapper import ResponseCode
from app.exceptions.base import BusinessException


# Legacy error codes for compatibility
class AuthErrorCodes:
    """认证错误码常量类 (兼容性版本)"""

    # 用户相关错误
    USER_NOT_FOUND = "AUTH_USER_001"
    USER_DISABLED = "AUTH_USER_002"
    INVALID_CREDENTIALS = "AUTH_USER_003"
    USER_ALREADY_EXISTS = "AUTH_USER_004"

    # 设备相关错误
    DEVICE_NOT_TRUSTED = "AUTH_DEVICE_001"
    DEVICE_VERIFICATION_REQUIRED = "AUTH_DEVICE_002"
    INVALID_DEVICE_TOKEN = "AUTH_DEVICE_003"
    DEVICE_NOT_FOUND = "AUTH_DEVICE_004"
    DEVICE_VERIFICATION_TOKEN_GENERATION_FAILED = "AUTH_DEVICE_005"
    DEVICE_VERIFICATION_FAILED = "AUTH_DEVICE_009"

    # 短信验证相关错误
    INVALID_PHONE_NUMBER = "AUTH_SMS_000"
    INVALID_VERIFICATION_CODE = "AUTH_SMS_001"
    VERIFICATION_CODE_EXPIRED = "AUTH_SMS_002"
    SMS_SERVICE_UNAVAILABLE = "AUTH_SMS_003"
    SMS_SEND_FAILED = "AUTH_SMS_004"
    TOO_MANY_ATTEMPTS = "AUTH_SMS_005"
    SMS_RATE_LIMIT_EXCEEDED = "AUTH_SMS_007"

    # 微信相关错误
    WECHAT_API_ERROR = "AUTH_WECHAT_001"
    INVALID_OPENID = "AUTH_WECHAT_002"
    QR_CODE_EXPIRED = "AUTH_WECHAT_003"

    # 令牌相关错误
    INVALID_TOKEN = "AUTH_TOKEN_001"
    TOKEN_EXPIRED = "AUTH_TOKEN_002"

    # 认证流程相关错误
    AUTHENTICATION_FAILED = "AUTH_FLOW_001"
    UNSUPPORTED_AUTH_TYPE = "AUTH_FLOW_007"
    AUTHENTICATION_INITIATION_FAILED = "AUTH_FLOW_008"
    AUTHENTICATION_COMPLETION_FAILED = "AUTH_FLOW_009"
    MISSING_REQUIRED_FIELDS = "AUTH_FLOW_010"
    UNSUPPORTED_OPERATION = "AUTH_FLOW_011"
    STATUS_CHECK_FAILED = "AUTH_FLOW_012"
    SMS_INITIATION_FAILED = "AUTH_FLOW_013"
    SMS_COMPLETION_FAILED = "AUTH_FLOW_014"
    WECHAT_INITIATION_FAILED = "AUTH_FLOW_015"
    WECHAT_COMPLETION_FAILED = "AUTH_FLOW_016"

    # 频率限制错误
    RATE_LIMIT_EXCEEDED = "AUTH_RATE_001"


class AuthErrorMessages:
    """认证错误消息常量类 (兼容性版本)"""

    @staticmethod
    def get_message(error_code: str, default: str = "未知错误") -> str:
        """获取错误码对应的消息"""
        messages = {
            AuthErrorCodes.USER_NOT_FOUND: "用户不存在",
            AuthErrorCodes.USER_DISABLED: "用户账户已被禁用",
            AuthErrorCodes.INVALID_CREDENTIALS: "用户名或密码错误",
            AuthErrorCodes.USER_ALREADY_EXISTS: "用户已存在",
            AuthErrorCodes.DEVICE_NOT_TRUSTED: "设备未受信任",
            AuthErrorCodes.DEVICE_VERIFICATION_REQUIRED: "需要设备验证",
            AuthErrorCodes.INVALID_DEVICE_TOKEN: "设备验证令牌无效",
            AuthErrorCodes.DEVICE_VERIFICATION_FAILED: "设备验证失败",
            AuthErrorCodes.INVALID_VERIFICATION_CODE: "验证码错误",
            AuthErrorCodes.VERIFICATION_CODE_EXPIRED: "验证码已过期",
            AuthErrorCodes.SMS_RATE_LIMIT_EXCEEDED: "短信发送频率过高",
            AuthErrorCodes.WECHAT_API_ERROR: "微信API调用失败",
            AuthErrorCodes.INVALID_OPENID: "微信OpenID无效",
            AuthErrorCodes.QR_CODE_EXPIRED: "二维码已过期",
            AuthErrorCodes.INVALID_TOKEN: "令牌无效",
            AuthErrorCodes.TOKEN_EXPIRED: "令牌已过期",
            AuthErrorCodes.AUTHENTICATION_FAILED: "认证失败",
            AuthErrorCodes.UNSUPPORTED_AUTH_TYPE: "不支持的认证类型",
            AuthErrorCodes.AUTHENTICATION_INITIATION_FAILED: "认证发起失败",
            AuthErrorCodes.AUTHENTICATION_COMPLETION_FAILED: "认证完成失败",
            AuthErrorCodes.MISSING_REQUIRED_FIELDS: "缺少必需字段",
            AuthErrorCodes.UNSUPPORTED_OPERATION: "不支持的操作",
            AuthErrorCodes.STATUS_CHECK_FAILED: "状态检查失败",
            AuthErrorCodes.SMS_INITIATION_FAILED: "短信认证发起失败",
            AuthErrorCodes.SMS_COMPLETION_FAILED: "短信认证完成失败",
            AuthErrorCodes.WECHAT_INITIATION_FAILED: "微信认证发起失败",
            AuthErrorCodes.WECHAT_COMPLETION_FAILED: "微信认证完成失败",
            AuthErrorCodes.RATE_LIMIT_EXCEEDED: "请求频率过高",
        }
        return messages.get(error_code, default)


class AuthenticationError(BusinessException):
    """认证基础异常类"""

    def __init__(self, message: str, response_code: ResponseCode, details: dict | None = None):
        super().__init__(message, response_code, details=details)


class UserNotFoundError(AuthenticationError):
    """用户不存在异常"""

    def __init__(self, user_id: int | None = None, username: str | None = None):
        details = {}
        if user_id:
            details["user_id"] = user_id
        if username:
            details["username"] = username

        super().__init__(
            message="用户不存在", response_code=ResponseCode.AUTH_USER_NOT_FOUND, details=details
        )


class InvalidCredentialsError(AuthenticationError):
    """无效凭据异常"""

    def __init__(self, details: dict | None = None):
        super().__init__(
            message="无效的凭据或密码错误",
            response_code=ResponseCode.AUTH_INVALID_CREDENTIALS,
            details=details,
        )


class UserDisabledError(AuthenticationError):
    """用户已禁用异常"""

    def __init__(self, user_id: int | None = None):
        details = {"user_id": user_id} if user_id else {}
        super().__init__(
            message="用户已被禁用", response_code=ResponseCode.AUTH_USER_DISABLED, details=details
        )


class UserAlreadyExistsError(AuthenticationError):
    """用户已存在异常"""

    def __init__(self, username: str | None = None, phone: str | None = None):
        details = {}
        if username:
            details["username"] = username
        if phone:
            details["phone"] = phone

        super().__init__(
            message="用户已存在",
            response_code=ResponseCode.AUTH_USER_ALREADY_EXISTS,
            details=details,
        )


class DeviceNotTrustedError(AuthenticationError):
    """设备不受信任异常"""

    def __init__(self, device_id: int | None = None):
        details = {"device_id": device_id} if device_id else {}
        super().__init__(
            message="设备不受信任",
            response_code=ResponseCode.AUTH_DEVICE_NOT_TRUSTED,
            details=details,
        )


class DeviceVerificationRequiredError(AuthenticationError):
    """需要设备验证异常"""

    def __init__(self, device_id: int | None = None):
        details = {"device_id": device_id} if device_id else {}
        super().__init__(
            message="需要设备验证",
            response_code=ResponseCode.AUTH_DEVICE_VERIFICATION_REQUIRED,
            details=details,
        )


class SMSInvalidCodeError(AuthenticationError):
    """无效验证码异常"""

    def __init__(self, phone: str | None = None):
        details = {"phone": phone} if phone else {}
        super().__init__(
            message="无效的验证码",
            response_code=ResponseCode.AUTH_SMS_INVALID_CODE,
            details=details,
        )


class SMSCodeExpiredError(AuthenticationError):
    """验证码已过期异常"""

    def __init__(self, phone: str | None = None):
        details = {"phone": phone} if phone else {}
        super().__init__(
            message="验证码已过期",
            response_code=ResponseCode.AUTH_SMS_CODE_EXPIRED,
            details=details,
        )


class SMSRateLimitError(AuthenticationError):
    """短信频率限制异常"""

    def __init__(
        self,
        message: str = "短信发送频率过高",
        phone: str | None = None,
        retry_after: int | None = None,
        details: dict | None = None,
    ):
        if details is None:
            details = {}
        if phone:
            details["phone"] = phone
        if retry_after:
            details["retry_after"] = retry_after

        super().__init__(
            message=message,
            response_code=ResponseCode.AUTH_SMS_RATE_LIMIT,
            details=details,
        )


class WeChatAPIError(AuthenticationError):
    """微信API调用异常"""

    def __init__(self, detail: str, openid: str | None = None):
        details = {"detail": detail}
        if openid:
            details["openid"] = openid

        super().__init__(
            message="微信服务调用失败",
            response_code=ResponseCode.AUTH_WECHAT_API_ERROR,
            details=details,
        )


class WeChatInvalidOpenIDError(AuthenticationError):
    """无效的OpenID异常"""

    def __init__(self, openid: str | None = None):
        details = {"openid": openid} if openid else {}
        super().__init__(
            message="无效的OpenID",
            response_code=ResponseCode.AUTH_WECHAT_INVALID_OPENID,
            details=details,
        )


# Additional exception classes for compatibility
class DeviceVerificationError(AuthenticationError):
    """设备验证异常"""

    def __init__(
        self,
        message: str | None = None,
        error_code: str | None = None,
        details: dict | None = None,
        **kwargs,
    ):
        if error_code and not details:
            details = {"error_code": error_code}
        elif error_code and details:
            details["error_code"] = error_code

        # Handle additional kwargs like status_code
        if kwargs:
            if not details:
                details = {}
            details.update(kwargs)

        super().__init__(
            message=message or ResponseCode.AUTH_DEVICE_VERIFICATION_FAILED.message,
            response_code=ResponseCode.AUTH_DEVICE_VERIFICATION_FAILED,
            details=details,
        )


class SMSVerificationError(AuthenticationError):
    """短信验证异常"""

    def __init__(
        self,
        message: str | None = None,
        error_code: str | None = None,
        details: dict | None = None,
        **kwargs,
    ):
        if error_code and not details:
            details = {"error_code": error_code}
        elif error_code and details:
            details["error_code"] = error_code

        # Handle additional kwargs like phone, retry_after, etc.
        if kwargs:
            if not details:
                details = {}
            details.update(kwargs)

        super().__init__(
            message=message or ResponseCode.AUTH_SMS_INVALID_CODE.message,
            response_code=ResponseCode.AUTH_SMS_INVALID_CODE,
            details=details,
        )


class WeChatAuthError(AuthenticationError):
    """微信认证异常"""

    def __init__(
        self,
        message: str | None = None,
        error_code: str | None = None,
        details: dict | None = None,
        **kwargs,
    ):
        if error_code and not details:
            details = {"error_code": error_code}
        elif error_code and details:
            details["error_code"] = error_code

        # Handle additional kwargs
        if kwargs:
            if not details:
                details = {}
            details.update(kwargs)

        super().__init__(
            message=message or ResponseCode.AUTH_WECHAT_API_ERROR.message,
            response_code=ResponseCode.AUTH_WECHAT_API_ERROR,
            details=details,
        )


class RateLimitError(AuthenticationError):
    """频率限制异常"""

    def __init__(
        self,
        message: str | None = None,
        error_code: str | None = None,
        details: dict | None = None,
        **kwargs,
    ):
        if error_code and not details:
            details = {"error_code": error_code}
        elif error_code and details:
            details["error_code"] = error_code

        # Handle additional kwargs like retry_after, phone, etc.
        if kwargs:
            if not details:
                details = {}
            details.update(kwargs)

        super().__init__(
            message=message or ResponseCode.AUTH_SMS_RATE_LIMIT.message,
            response_code=ResponseCode.AUTH_SMS_RATE_LIMIT,
            details=details,
        )
