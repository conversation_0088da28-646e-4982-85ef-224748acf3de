"""微信相关异常定义"""

from typing import Optional
from app.core.response_wrapper import ResponseCode
from app.exceptions.base import BusinessException


class WeChatError(BusinessException):
    """微信基础异常类"""
    
    def __init__(
        self, 
        message: str, 
        response_code: ResponseCode,
        details: Optional[dict] = None
    ):
        super().__init__(message, response_code, details=details)


class WeChatAPIError(WeChatError):
    """微信API调用异常"""
    
    def __init__(self, detail: str, openid: Optional[str] = None):
        details = {"detail": detail}
        if openid:
            details["openid"] = openid
            
        super().__init__(
            message="微信服务调用失败",
            response_code=ResponseCode.AUTH_WECHAT_API_ERROR,
            details=details
        )


class WeChatInvalidOpenIDError(WeChatError):
    """无效的OpenID异常"""
    
    def __init__(self, openid: Optional[str] = None):
        details = {"openid": openid} if openid else {}
        super().__init__(
            message="无效的OpenID",
            response_code=ResponseCode.AUTH_WECHAT_INVALID_OPENID,
            details=details
        )


class WeChatBindError(WeChatError):
    """微信绑定异常"""
    
    def __init__(self, detail: str, openid: Optional[str] = None, username: Optional[str] = None):
        details = {"detail": detail}
        if openid:
            details["openid"] = openid
        if username:
            details["username"] = username
            
        super().__init__(
            message="微信绑定失败",
            response_code=ResponseCode.FAILURE,
            details=details
        )


class WeChatQRCodeError(WeChatError):
    """微信二维码异常"""
    
    def __init__(self, detail: str, scene_str: Optional[str] = None):
        details = {"detail": detail}
        if scene_str:
            details["scene_str"] = scene_str
            
        super().__init__(
            message="二维码操作失败",
            response_code=ResponseCode.FAILURE,
            details=details
        )