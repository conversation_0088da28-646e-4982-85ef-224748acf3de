"""应用主入口"""

import uuid
from datetime import datetime

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi_cache import FastAPICache
from fastapi_cache.backends.redis import RedisBackend
from loguru import logger
from sqlalchemy import text

from app.api.api import api_router
from app.api.behavior_tracking_middleware import BehaviorTrackingMiddleware

# 微信公众号消息推送api
from app.api.middleware import ResponseFormatterMiddleware
from app.api.visit_stats_middleware import VisitStatsMiddleware
from app.config import settings
from app.core.init_data import init_data
from app.core.limiter import setup_limiter
from app.core.pagination import add_pagination
from app.db.redis import get_redis_master
from app.db.session import get_db
from app.exception_handlers.registry import register_exception_handlers


async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 初始化Redis连接
    redis = await get_redis_master()
    app.state.redis = redis
    FastAPICache.init(RedisBackend(redis), prefix="fastapi-cache")

    # 初始化应用数据
    logger.info("正在初始化应用数据...")
    await init_data()
    logger.info("应用数据初始化完成")

    # 这里可以后续添加数据库连接池监控
    logger.info("应用启动完成")

    yield

    # 应用关闭时的清理工作
    logger.info("应用关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title="Steam数据聚合API",
    description="提供Steam数据的聚合和分析服务",
    version="0.1.0",
    lifespan=lifespan,
    # 生产默认关闭默认文档
    # openapi_url=f"{settings.API_V1_STR}/openapi.json",
)

# 初始化分页功能
add_pagination(app)

# 初始化限流器
setup_limiter(app)

# 初始化日志
logger.info("应用程序启动中...")


# 添加请求ID中间件
@app.middleware("http")
async def request_id_middleware(request: Request, call_next):
    """为每个请求注入一个唯一的ID，以便追踪"""
    request_id = request.headers.get("X-Request-ID", str(uuid.uuid4()))
    with logger.contextualize(request_id=request_id):
        response = await call_next(request)
        response.headers["X-Request-ID"] = request_id
        return response


# 添加用户行为追踪中间件
app.add_middleware(BehaviorTrackingMiddleware)

# 添加访问统计中间件
app.add_middleware(VisitStatsMiddleware)

# 添加响应格式化中间件
app.add_middleware(ResponseFormatterMiddleware)

# 配置CORS（最后添加，最先执行）
app.add_middleware(
    CORSMiddleware,
    # 明确允许的常用前端来源
    allow_origins=[
        "http://**************:3000",
        "http://localhost:3000",
        "http://*************:3000",
    ],  # 生产环境应限制为实际域名
    # 允许 ************** 任意端口（开发调试更宽松，生产请收紧）
    allow_origin_regex=r"^https?://192\.168\.31\.(?:\d{1,3})(?::\d+)?$",
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册所有异常处理器
register_exception_handlers(app)


@app.get("/")
async def root():
    """API根路径，返回欢迎信息"""
    return {"message": "欢迎使用Steam数据聚合API"}


@app.get("/health")
async def health_check():
    """健康检查端点"""
    health_status = {"status": "healthy", "timestamp": str(datetime.now())}

    # 检查数据库连接健康状态
    try:
        # 执行简单的健康检查查询，复用现有的连接
        connection_healthy = False
        pool_stats = {}
        usage_percentage = 0

        try:
            # 创建一次连接来检查健康状态
            from app.db.session import engine

            pool = engine.pool
            pool_stats = {
                "pool_size": pool.size,
                "checked_out": pool.checkedout(),
                "overflow": pool.overflow(),
                "available": pool.size - pool.checkedout(),
            }

            if pool.size > 0:
                usage_percentage = (pool.checkedout() / pool.size) * 100

            # 简单的连接测试，通过检查连接回收信息
            async for db in get_db():
                await db.execute(text("SELECT 1"))
                connection_healthy = True
                break

        except Exception as db_error:
            logger.warning(f"数据库连接检查失败: {db_error}")
            connection_healthy = False

        health_status["database"] = {
            "connection_healthy": connection_healthy,
            "pool_stats": pool_stats,
            "usage_percentage": f"{usage_percentage:.1f}%",
        }

        # 如果数据库连接有问题，标记为不健康
        if not connection_healthy:
            health_status["status"] = "unhealthy"
        elif usage_percentage > 90:
            health_status["status"] = "warning"

    except Exception as e:
        logger.error(f"健康检查出错: {e}")
        health_status["status"] = "error"
        health_status["error"] = str(e)

    return health_status


# 注册API路由
app.include_router(api_router, prefix=settings.API_V1_STR)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")
