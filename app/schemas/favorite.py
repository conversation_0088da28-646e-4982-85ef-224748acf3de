from datetime import datetime
from typing import Literal

from pydantic import BaseModel


class FavoriteBase(BaseModel):
    """收藏基础模型"""

    content_type: Literal["article", "video", "scratch"]
    content_id: int
    note: str | None = None


class FavoriteCreate(FavoriteBase):
    """创建收藏的请求模型"""

    pass


class FavoriteUpdate(BaseModel):
    """更新收藏的请求模型"""

    note: str | None = None


class FavoriteToggle(BaseModel):
    """切换收藏状态的请求模型"""

    content_type: Literal["article", "video", "scratch"]
    content_id: int
    note: str | None = None


class FavoriteInDBBase(FavoriteBase):
    """数据库中收藏的基础模型"""

    id: int
    user_id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Favorite(FavoriteInDBBase):
    """API响应中的收藏模型"""

    pass


class FavoriteStatus(BaseModel):
    """收藏状态响应模型"""

    content_type: Literal["article", "video", "scratch"]
    content_id: int
    is_favorited: bool
    favorite_count: int


class FavoriteStats(BaseModel):
    """收藏统计模型"""

    total_favorites: int
    today_favorites: int
    this_week_favorites: int
    this_month_favorites: int


class ContentFavoriteInfo(BaseModel):
    """内容收藏信息模型"""

    content_type: Literal["article", "video", "scratch"]
    content_id: int
    favorite_count: int
    is_favorited_by_user: bool = False  # 当前用户是否已收藏


class FavoriteWithContent(Favorite):
    """包含内容信息的收藏模型"""

    content_title: str | None = None
    content_author: str | None = None
    content_created_at: datetime | None = None
