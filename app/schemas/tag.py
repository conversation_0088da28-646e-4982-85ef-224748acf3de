from pydantic import BaseModel


class TagBase(BaseModel):
    """标签基础模型"""

    name: str
    is_default: bool = False

    model_config = {"from_attributes": True}


class TagCreate(TagBase):
    """创建标签的请求模型"""

    pass


class TagUpdate(TagBase):
    """更新标签的请求模型"""

    name: str | None = None
    is_default: bool | None = None

    model_config = {"from_attributes": True}


class Tag(TagBase):
    """标签的响应模型"""

    id: int
    is_default: bool

    model_config = {"from_attributes": True}
