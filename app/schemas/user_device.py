"""用户设备相关的Pydantic模型"""

from datetime import datetime

from pydantic import BaseModel, Field


class UserDeviceBase(BaseModel):
    """用户设备基础模型"""
    
    device_fingerprint: str = Field(..., description="设备指纹")
    device_name: str | None = Field(None, description="设备名称")
    device_type: str | None = Field(None, description="设备类型：mobile, tablet, desktop")
    ip_address: str | None = Field(None, description="IP地址")
    location: str | None = Field(None, description="地理位置")
    user_agent: str | None = Field(None, description="用户代理字符串")
    browser_name: str | None = Field(None, description="浏览器名称")
    browser_version: str | None = Field(None, description="浏览器版本")
    os_name: str | None = Field(None, description="操作系统名称")
    os_version: str | None = Field(None, description="操作系统版本")


class UserDeviceCreate(UserDeviceBase):
    """创建用户设备的模型"""
    
    user_id: int = Field(..., description="用户ID")


class UserDeviceUpdate(BaseModel):
    """更新用户设备的模型"""
    
    device_name: str | None = None
    ip_address: str | None = None
    location: str | None = None
    is_trusted: bool | None = None
    trust_score: int | None = None
    is_active: bool | None = None
    is_blocked: bool | None = None
    blocked_reason: str | None = None


class UserDeviceInDB(UserDeviceBase):
    """数据库中的用户设备模型"""
    
    id: int
    user_id: int
    is_trusted: bool
    trust_score: int
    login_count: int
    last_login_at: datetime
    first_login_at: datetime
    is_active: bool
    is_blocked: bool
    blocked_reason: str | None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class UserDevice(UserDeviceInDB):
    """用户设备响应模型"""
    pass


class UserDeviceLoginInfo(BaseModel):
    """设备登录信息更新模型"""
    
    ip_address: str | None = None
    location: str | None = None