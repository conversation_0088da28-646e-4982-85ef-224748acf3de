from datetime import datetime

from pydantic import BaseModel, ConfigDict, model_serializer

from app.config import get_settings
from app.schemas.video import VideoBase

settings = get_settings()
CDN_DOMAIN = settings.OSS_CDN_DOMAIN


class BannerBase(BaseModel):
    """轮播图基础模型"""

    title: str
    description: str | None = None
    image_url: str
    link_url: str | None = None
    video_id: int | None = None
    sort_order: int = 0
    is_active: bool = True
    banner_type: str = "home"


class BannerCreate(BannerBase):
    """创建轮播图模型"""

    pass


class BannerUpdate(BaseModel):
    """更新轮播图模型"""

    title: str | None = None
    description: str | None = None
    image_url: str | None = None
    link_url: str | None = None
    video_id: int | None = None
    sort_order: int | None = None
    is_active: bool | None = None
    banner_type: str | None = None


class BannerInDBBase(BannerBase):
    """数据库中的轮播图模型"""

    id: int
    created_at: datetime | None = None
    updated_at: datetime | None = None

    class Config:
        from_attributes = True


class Banner(BannerInDBBase):
    """API响应中的轮播图模型"""

    pass


class BannerWithVideo(Banner):
    """包含视频信息的轮播图模型"""

    video: VideoBase | None = None

    model_config = ConfigDict(from_attributes=True)

    @model_serializer(mode="wrap")
    def ser_model(self, nxt):
        data = nxt(self)

        # 如果video存在，处理视频URL和封面URL添加CDN域名前缀
        if data.get("video") is not None:
            video_data = data["video"]
            if hasattr(video_data, "model_dump"):
                video_dict = video_data.model_dump()
            else:
                video_dict = dict(video_data)

            url_fields = ["url", "cover_url", "image_url", "link_url"]
            for field in url_fields:
                if field in video_dict and video_dict[field]:
                    url = video_dict[field]
                    if url and isinstance(url, str):
                        # 如果是相对路径（以/开头），添加CDN域名
                        if url.startswith("/"):
                            video_dict[field] = CDN_DOMAIN + url
                        # 如果是steam相关路径但没有域名，也添加CDN域名
                        elif url.startswith("steam/") and not url.startswith(
                            ("http://", "https://")
                        ):
                            video_dict[field] = CDN_DOMAIN + "/" + url

            # 更新data中的video
            data["video"] = video_dict

        return data
