from datetime import datetime
from typing import Literal

from pydantic import BaseModel


class HistoryBase(BaseModel):
    content_type: Literal["article", "video"]
    content_id: int


class HistoryCreate(HistoryBase):
    pass


class HistoryUpdate(HistoryBase):
    id: int


class HistoryInDB(HistoryBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True
    }


class History(HistoryInDB):
    last_visited_at: datetime
    visit_count: int
    
    model_config = {
        "from_attributes": True
    }


class HistoryWithContent(History):
    content_title: str | None = None
    content_author: str | None = None
    content_created_at: datetime | None = None
    
    model_config = {
        "from_attributes": True
    }


class HistoryList(BaseModel):
    total: int
    items: list[HistoryWithContent]
    
    model_config = {
        "from_attributes": True
    }
