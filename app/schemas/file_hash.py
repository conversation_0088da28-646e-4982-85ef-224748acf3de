
from pydantic import BaseModel


class FileHashBase(BaseModel):
    """文件哈希基础模型"""

    file_path: str
    file_hash: str
    file_metadata: dict | None = None

    class Config:
        from_attributes = True


class FileHash(FileHashBase):
    """API响应中的文件哈希模型"""

    pass


class FileHashCreate(FileHashBase):
    """创建文件哈希的请求模型"""

    pass


class FileHashUpdate(FileHashBase):
    """更新文件哈希的请求模型"""

    pass
