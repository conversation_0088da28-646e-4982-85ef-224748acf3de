"""
批量上传和状态查询相关的数据模型
"""

from enum import Enum

from pydantic import BaseModel


# --- Batch Upload ---
class TaskInfo(BaseModel):
    """单个任务的信息，在批量上传成功后返回"""

    fileName: str
    taskId: str


class BatchUploadResponseData(BaseModel):
    """批量上传响应的数据部分"""

    tasks: list[TaskInfo]


class BatchUploadResponse(BaseModel):
    """批量上传接口 /api/upload/batch 的最终响应模型"""

    data: BatchUploadResponseData


# --- Batch Status Query ---
class BatchStatusRequest(BaseModel):
    """批量状态查询接口 /api/upload/status/batch 的请求模型"""

    task_ids: list[str]


class UploadStatus(str, Enum):
    """任务上传状态枚举，与前端对齐"""

    IDLE = "idle"
    FILE_SELECTED = "file_selected"
    FILE_UPLOAD_STARTED = "file_upload_started"
    UPLOADING = "uploading"
    PROCESSING = "processing"
    UPLOAD_SUCCESS = "upload_success"
    UPLOAD_ERROR = "upload_error"
    CREATING = "creating"
    CREATE_ERROR = "create_error"
    COMPLETE = "complete"


class TaskStatus(BaseModel):
    """单个任务的详细状态信息"""

    taskId: str
    status: UploadStatus
    progress: float | None = None
    file_metadata: dict | None = None
    message: str | None = None


class BatchStatusResponse(BaseModel):
    """批量状态查询接口 /api/upload/status/batch 的响应模型"""

    tasks: list[TaskStatus]
