from datetime import datetime
from typing import Literal

from pydantic import BaseModel, ConfigDict, Field


class CategoryBase(BaseModel):
    """分类基础模型"""

    id: int = Field(..., description="分类ID")
    name: str = Field(..., min_length=1, max_length=50, description="分类名称")
    description: str | None = Field(None, max_length=255, description="分类描述")
    parent_id: int | None = Field(None, description="父分类ID")
    category_type: Literal["article", "video"] = Field(
        ..., description="类别类型: article 或 video"
    )

    model_config = ConfigDict(from_attributes=True)


class CategoryCreate(CategoryBase):
    """创建分类的请求模型"""

    pass


class CategoryUpdate(CategoryBase):
    """更新分类的请求模型"""

    pass


class CategoryInDBBase(CategoryBase):
    """数据库中分类的基础模型"""

    id: int
    created_at: datetime | None = None
    updated_at: datetime | None = None

    class Config:
        from_attributes = True


class Category(CategoryInDBBase):
    """API响应中的分类模型"""

    category_type: Literal["article", "video"]
    article_count: int = Field(0, description="文章数量")
    video_count: int = Field(0, description="视频数量")
    children: list["Category"] = []


# 更新前向引用
Category.model_rebuild()


class AuthorInfo(BaseModel):
    id: int
    nickname: str
    avatar: str | None = None

    class Config:
        from_attributes = True


class Tag(BaseModel):
    id: int
    name: str

    class Config:
        from_attributes = True


class CategoryRelatedInfo(BaseModel):
    category: Category
    related_authors: list[AuthorInfo]
    hot_tags: list[Tag]
