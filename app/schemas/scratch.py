import logging
from datetime import datetime
from enum import Enum
from typing import Any, Optional

from pydantic import BaseModel, ConfigDict, Field, model_validator

from app.config import get_settings
from app.schemas.user import UserAggregated

logger = logging.getLogger(__name__)

settings = get_settings()
CDN_DOMAIN = settings.OSS_CDN_DOMAIN


class AdaptationType(str, Enum):
    """改编类型枚举"""

    ORIGINAL = "original"  # 原创作品
    REMIX = "remix"  # 混编 - 基于原项目进行修改和扩展
    COPY = "copy"  # 复制 - 基于原项目创建独立版本


class ScratchProjectStats(BaseModel):
    """Scratch项目统计信息"""

    visit_count: int = Field(0, description="访问次数")
    adapt_count: int = Field(0, description="被改编次数")
    favorite_count: int = Field(0, description="收藏数")

    # 用户相关统计
    is_favorited_by_user: bool = Field(False, description="当前用户是否已收藏")
    is_followed_author: bool = Field(False, description="当前用户是否已关注作者")


class AdaptationInfo(BaseModel):
    """改编信息"""

    original_project_id: int | None = Field(None, description="直接改编源项目ID")
    root_project_id: int | None = Field(None, description="改编链根项目ID")
    adapt_level: int = Field(0, description="改编层级，0为原创")
    adaptation_type: AdaptationType = Field(AdaptationType.ORIGINAL, description="改编类型")


class ScratchProductBase(BaseModel):
    """Scratch项目基础模型"""

    project_id: int = Field(..., description="项目ID")
    title: str = Field(..., description="项目标题")
    description: str | None = Field(None, description="项目描述")
    cover_url: str | None = Field(None, description="封面图URL")
    author_id: int = Field(..., description="作者ID")
    is_published: bool = Field(False, description="是否发布")
    can_adapt: bool = Field(True, description="是否允许改编")

    # 改编相关字段
    original_project_id: int | None = Field(None, description="直接改编源项目ID")
    root_project_id: int | None = Field(None, description="改编链根项目ID")
    adapt_level: int = Field(0, description="改编层级，0为原创")
    adaptation_type: AdaptationType = Field(AdaptationType.ORIGINAL, description="改编类型")

    model_config = ConfigDict(from_attributes=True)


class ScratchProductCreate(BaseModel):
    """创建Scratch项目的请求模型"""

    title: str = Field(..., description="项目标题")
    cover_url: str | None = Field(None, description="封面图URL")
    can_adapt: bool = Field(True, description="是否允许改编")
    code: dict | None = None
    author_id: int = Field(..., description="作者ID")


class ScratchProductUpdate(BaseModel):
    """更新Scratch项目的请求模型"""

    title: str | None = None
    description: str | None = None
    cover_url: str | None = None
    is_published: bool | None = None
    can_adapt: bool | None = None
    code: dict | None = None


class ScratchProductAdapt(BaseModel):
    """改编Scratch项目的请求模型"""

    title: str = Field(..., description="新项目标题")
    description: str | None = Field(None, description="新项目描述")
    adaptation_type: AdaptationType = Field(AdaptationType.REMIX, description="改编类型")
    inherit_resources: bool = Field(True, description="是否继承原项目资源")
    inherit_code: bool = Field(True, description="是否继承原项目代码")
    custom_notes: str | None = Field(None, description="改编说明")


class ScratchProductSettings(BaseModel):
    """Scratch项目改编设置模型"""

    can_adapt: bool = Field(True, description="是否允许改编")
    adaptation_policy: str = Field("open", description="改编策略: open, restricted, closed")
    max_adapt_level: int | None = Field(None, description="最大允许改编层级")


class ScratchProductOut(ScratchProductBase):
    """Scratch项目输出模型"""

    created_at: datetime | None = Field(None, description="创建时间")
    updated_at: datetime | None = Field(None, description="更新时间")
    cache_version: int = Field(1, description="缓存版本号")

    # 统计信息
    visit_count: int = Field(0, description="访问次数")
    adapt_count: int = Field(0, description="被改编次数")
    favorite_count: int = Field(0, description="收藏数")
    stats: ScratchProjectStats | None = Field(None, description="统计信息")

    # 关联信息
    author: UserAggregated | None = Field(None, description="作者信息")
    adaptation_info: AdaptationInfo | None = Field(None, description="改编信息")

    # 改编关系
    original_project: Optional["ScratchProductSimple"] = Field(None, description="直接改编源项目")
    root_project: Optional["ScratchProductSimple"] = Field(None, description="改编链根项目")

    model_config = ConfigDict(from_attributes=True)

    @model_validator(mode="before")
    def populate_adaptation_info(cls, values: Any) -> Any:
        """在验证之前，根据项目数据动态填充adaptation_info字段"""
        # 将ORM对象转成dict
        if not isinstance(values, dict):
            if hasattr(values, "model_dump"):
                values = values.model_dump()
            elif hasattr(values, "__dict__"):
                values = dict(values.__dict__)
            else:
                values = {}

        # 填充adaptation_info字段
        if "adaptation_info" not in values or values["adaptation_info"] is None:
            values["adaptation_info"] = AdaptationInfo(
                original_project_id=values.get("original_project_id"),
                root_project_id=values.get("root_project_id"),
                adapt_level=values.get("adapt_level", 0),
                adaptation_type=values.get("adaptation_type", AdaptationType.ORIGINAL),
            )

        # 填充stats字段
        if "stats" not in values or values["stats"] is None:
            values["stats"] = ScratchProjectStats(
                visit_count=values.get("visit_count", 0),
                adapt_count=values.get("adapt_count", 0),
                favorite_count=values.get("favorite_count", 0),
            )

        return values


class ScratchProductCode(BaseModel):
    """Scratch项目代码模型"""

    code: dict | None = None


class ScratchProductSimple(BaseModel):
    """Scratch项目简化模型（用于避免循环引用）"""

    project_id: int = Field(..., description="项目ID")
    title: str = Field(..., description="项目标题")
    description: str | None = Field(None, description="项目描述")
    cover_url: str | None = Field(None, description="封面图URL")
    author_id: int = Field(..., description="作者ID")
    is_published: bool = Field(False, description="是否发布")
    adapt_level: int = Field(0, description="改编层级")
    adaptation_type: AdaptationType = Field(AdaptationType.ORIGINAL, description="改编类型")
    created_at: datetime | None = Field(None, description="创建时间")

    author: UserAggregated | None = Field(None, description="作者信息")

    model_config = ConfigDict(from_attributes=True)


class AdaptationChain(BaseModel):
    """改编链模型"""

    chain: list[ScratchProductSimple] = Field(..., description="改编链，从根项目到当前项目")
    root_project: ScratchProductSimple = Field(..., description="根项目")
    current_level: int = Field(..., description="当前项目在改编链中的层级")
    total_adaptations: int = Field(0, description="整个改编链的总改编数")


class AdaptationList(BaseModel):
    """改编列表模型"""

    items: list[ScratchProductOut] = Field(..., description="改编项目列表")
    total: int = Field(..., description="总数")
    page: int = Field(..., description="当前页码")
    limit: int = Field(..., description="每页数量")
    has_next: bool = Field(False, description="是否有下一页")
    has_prev: bool = Field(False, description="是否有上一页")


class ScratchProduct(ScratchProductBase):
    """完整的Scratch项目模型"""

    pass


# 更新前向引用
ScratchProductOut.model_rebuild()
