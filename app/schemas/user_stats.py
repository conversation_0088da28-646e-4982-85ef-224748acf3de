from pydantic import BaseModel


class UserStatsBase(BaseModel):
    """用户统计基础模型"""

    total_likes_count: int = 0
    total_favorites_count: int = 0
    following_count: int = 0
    follower_count: int = 0
    article_count: int = 0
    video_count: int = 0


class UserStatsCreate(UserStatsBase):
    user_id: int


class UserStatsUpdate(UserStatsBase):
    pass


class UserStatsInDB(UserStatsBase):
    user_id: int

    class Config:
        from_attributes = True


class UserStats(UserStatsInDB):
    pass
