from pydantic import Field
from pydantic_settings import BaseSettings


class ApiKeysConfig(BaseSettings):
    """第三方服务API Key配置"""

    # 微信API相关配置
    WECHAT_ACCESS_TOKEN_URL: str = Field(
        default="https://api.weixin.qq.com/cgi-bin/token", description="获取access_token的URL"
    )
    WECHAT_QR_CREATE_URL: str = Field(
        default="https://api.weixin.qq.com/cgi-bin/qrcode/create", description="创建二维码的URL"
    )
    WECHAT_QR_SHOW_URL: str = Field(
        default="https://mp.weixin.qq.com/cgi-bin/showqrcode", description="显示二维码的URL"
    )
    WECHAT_USER_INFO_URL: str = Field(
        default="https://api.weixin.qq.com/cgi-bin/user/info", description="获取用户信息的URL"
    )

    # Steam API设置
    STEAM_API_KEY: str = Field(default="your-steam-api-key", description="Steam API密钥")
