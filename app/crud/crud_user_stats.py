from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.user_stats import UserStats
from app.schemas.user_stats import UserStatsCreate, UserStatsUpdate


class CRUDUserStats(CRUDBase[UserStats, UserStatsCreate, UserStatsUpdate]):
    async def get(self, db: AsyncSession, user_id: int) -> UserStats | None:
        """
        根据用户ID获取统计数据记录

        Args:
            db: 数据库会话
            user_id: 用户ID

        Returns:
            查询到的统计数据记录，如果不存在则返回None
        """
        stmt = select(self.model).where(self.model.user_id == user_id)
        result = await db.execute(stmt)
        return result.scalar_one_or_none()

    async def get_or_create(
        self, db: AsyncSession, *, user_id: int, commit: bool = True
    ) -> UserStats:
        """
        获取或创建用户的统计数据记录
        """
        stmt = select(self.model).where(self.model.user_id == user_id)
        result = await db.execute(stmt)
        instance = result.scalar_one_or_none()
        if instance:
            return instance

        # 创建实例但不提交
        obj_in_data = {"user_id": user_id}
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        if commit:
            await db.commit()
            await db.refresh(db_obj)
        return db_obj

    async def get_multi_by_user_ids(
        self, db: AsyncSession, *, user_ids: list[int]
    ) -> list[UserStats]:
        """
        根据用户ID列表获取多个统计数据记录
        """
        if not user_ids:
            return []
        stmt = select(self.model).where(self.model.user_id.in_(user_ids))
        result = await db.execute(stmt)
        return result.scalars().all()

    async def increment(
        self, db: AsyncSession, *, user_id: int, field: str, value: int = 1
    ) -> UserStats:
        """
        原子性地增加一个统计字段的值
        """
        stats = await self.get_or_create(db, user_id=user_id)
        current_value = getattr(stats, field, 0)
        setattr(stats, field, current_value + value)

        db.add(stats)
        await db.commit()
        await db.refresh(stats)

        return stats


user_stats = CRUDUserStats(UserStats)
