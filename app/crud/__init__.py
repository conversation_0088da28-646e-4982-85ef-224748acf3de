# from app.crud.approval import approval
from app.crud.article import article
from app.crud.banner import banner
from app.crud.base import CRUDBase
from app.crud.category import category
from app.crud.comment import comment
from app.crud.crud_user_stats import user_stats
from app.crud.favorite import favorite
from app.crud.file_hash import file_hash
from app.crud.history import history
from app.crud.like import like
from app.crud.notification import notification
from app.crud.review import review
from app.crud.scratch import scratch_product
from app.crud.tag import tag
from app.crud.user import user
from app.crud.user_behavior import (
    content_similarity,
    recommendation_log,
    user_browse_history,
    user_interaction,
    user_profile,
)
from app.crud.user_device import user_device
from app.crud.video import video
from app.crud.video_folder import video_folder

__all__ = [
    "article",
    "banner",
    "category",
    "comment",
    "favorite",
    "file_hash",
    "like",
    "notification",
    "review",
    "scratch_product",
    "tag",
    "user",
    "user_device",
    "user_stats",
    "user_browse_history",
    "user_interaction",
    "user_profile",
    "video",
    "video_folder",
    "approval",
    "content_similarity",
    "recommendation_log",
    "CRUDBase",
    "history",
]
