"""
背包项目CRUD操作
"""

import logging
from uuid import UUID

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.backpack import BackpackItem, BackpackItemType
from app.schemas.backpack import BackpackItemCreate, BackpackItemUpdate

logger = logging.getLogger(__name__)


class BackpackCRUD:
    """背包项目CRUD操作类"""

    @staticmethod
    async def create_item(
        db: AsyncSession,
        user_id: int,
        item_data: BackpackItemCreate,
        body_path: str,
        thumbnail_path: str,
        size_bytes: int,
    ) -> BackpackItem:
        """创建背包项目"""
        db_item = BackpackItem(
            user_id=user_id,
            type=item_data.type,
            mime=item_data.mime,
            name=item_data.name,
            body_path=body_path,
            thumbnail_path=thumbnail_path,
            size_bytes=size_bytes,
            extra_data={},
        )

        db.add(db_item)
        await db.commit()
        await db.refresh(db_item)
        return db_item

    @staticmethod
    async def get_items_by_user_id(
        db: AsyncSession,
        user_id: int,
        limit: int = 20,
        offset: int = 0,
        item_type: BackpackItemType | None = None,
    ) -> list[BackpackItem]:
        """根据用户ID获取背包项目列表"""
        query = select(BackpackItem).filter(BackpackItem.user_id == user_id)

        if item_type:
            query = query.filter(BackpackItem.type == item_type)

        query = query.order_by(desc(BackpackItem.created_at))
        query = query.offset(offset).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def get_item_by_id(db: AsyncSession, item_id: UUID, user_id: int) -> BackpackItem | None:
        """根据ID和用户ID获取背包项目"""
        query = select(BackpackItem).filter(
            and_(BackpackItem.id == item_id, BackpackItem.user_id == user_id)
        )
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def delete_item(db: AsyncSession, item_id: UUID, user_id: int) -> bool:
        """删除背包项目"""
        item = await BackpackCRUD.get_item_by_id(db, item_id, user_id)
        if not item:
            return False

        await db.delete(item)
        await db.commit()
        return True

    @staticmethod
    async def update_item(
        db: AsyncSession, item_id: UUID, user_id: int, update_data: BackpackItemUpdate
    ) -> BackpackItem | None:
        """更新背包项目"""
        item = await BackpackCRUD.get_item_by_id(db, item_id, user_id)
        if not item:
            return None

        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(item, field, value)

        await db.commit()
        await db.refresh(item)
        return item

    @staticmethod
    async def count_items_by_user_id(
        db: AsyncSession, user_id: int, item_type: BackpackItemType | None = None
    ) -> int:
        """统计用户的背包项目数量"""
        query = select(func.count(BackpackItem.id)).filter(BackpackItem.user_id == user_id)

        if item_type:
            query = query.filter(BackpackItem.type == item_type)

        result = await db.execute(query)
        return result.scalar() or 0

    @staticmethod
    async def get_user_storage_stats(db: AsyncSession, user_id: int) -> dict:
        """获取用户存储统计信息"""
        # 总项目数和总大小
        total_query = select(
            func.count(BackpackItem.id).label("total_count"),
            func.coalesce(func.sum(BackpackItem.size_bytes), 0).label("total_size"),
        ).filter(BackpackItem.user_id == user_id)

        total_result = await db.execute(total_query)
        total_stats = total_result.first()

        # 按类型统计
        type_query = (
            select(
                BackpackItem.type,
                func.count(BackpackItem.id).label("count"),
                func.coalesce(func.sum(BackpackItem.size_bytes), 0).label("size"),
            )
            .filter(BackpackItem.user_id == user_id)
            .group_by(BackpackItem.type)
        )

        type_result = await db.execute(type_query)
        type_stats = type_result.all()

        stats = {
            "total_count": total_stats.total_count or 0,
            "total_size": total_stats.total_size or 0,
            "by_type": {},
        }

        for type_stat in type_stats:
            stats["by_type"][type_stat.type] = {"count": type_stat.count, "size": type_stat.size}

        return stats


# 创建CRUD实例
backpack_crud = BackpackCRUD()
