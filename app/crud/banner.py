from typing import Any

from sqlalchemy import desc, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.banner import Banner
from app.schemas.banner import BannerCreate, BannerUpdate


class CRUDBanner(CRUDBase[Banner, BannerCreate, BannerUpdate]):
    async def get_multi_with_filters(
        self,
        db: AsyncSession,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: dict[str, Any] | None = None,
        order_by: str = "id",
        order_direction: str = "asc",
    ) -> list[Banner]:
        """获取多个轮播图，支持过滤和排序"""
        query = select(self.model)
        
        # 应用过滤条件
        if filters:
            for field, value in filters.items():
                if hasattr(self.model, field):
                    query = query.where(getattr(self.model, field) == value)
        
        # 应用排序
        if hasattr(self.model, order_by):
            if order_direction.lower() == "desc":
                query = query.order_by(desc(getattr(self.model, order_by)))
            else:
                query = query.order_by(getattr(self.model, order_by))
        
        # 应用分页
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()


banner = CRUDBanner(Banner)