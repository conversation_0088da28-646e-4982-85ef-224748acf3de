from datetime import datetime

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.approval import Approval
from app.schemas.approval import ApprovalCreate, ApprovalUpdate


class CRUDApproval(CRUDBase[Approval, ApprovalCreate, ApprovalUpdate]):
    async def get_by_content(
        self, db: AsyncSession, *, content_type: str, content_id: int
    ) -> Approval | None:
        """获取指定内容的最新审核记录

        Args:
            db: 数据库会话
            content_type: 内容类型（article或video）
            content_id: 内容ID

        Returns:
            审核记录，如果不存在则返回None
        """
        result = await db.execute(
            select(self.model)
            .where(
                self.model.content_type == content_type,
                self.model.content_id == content_id,
            )
            .order_by(self.model.created_at.desc())
        )
        return result.scalar_one_or_none()

    async def get_pending_approvals(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> list[Approval]:
        """获取待审核的记录列表

        Args:
            db: 数据库会话
            skip: 跳过的记录数
            limit: 返回的最大记录数

        Returns:
            待审核记录列表
        """
        result = await db.execute(
            select(self.model)
            .where(self.model.status == "pending")
            .order_by(self.model.created_at.asc())
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def approve(self, db: AsyncSession, *, db_obj: Approval, reviewer_id: int) -> Approval:
        """批准审核记录

        Args:
            db: 数据库会话
            db_obj: 审核记录对象
            reviewer_id: 审核者ID

        Returns:
            更新后的审核记录
        """
        update_data = {
            "status": "approved",
            "reviewer_id": reviewer_id,
            "reviewed_at": datetime.utcnow(),
        }
        return await self.update(db, db_obj=db_obj, obj_in=update_data)

    async def reject(
        self,
        db: AsyncSession,
        *,
        db_obj: Approval,
        reviewer_id: int,
        reason: str,
    ) -> Approval:
        """拒绝审核记录

        Args:
            db: 数据库会话
            db_obj: 审核记录对象
            reviewer_id: 审核者ID
            reason: 拒绝原因

        Returns:
            更新后的审核记录
        """
        update_data = {
            "status": "rejected",
            "reviewer_id": reviewer_id,
            "reviewed_at": datetime.utcnow(),
            "reject_reason": reason,
        }
        return await self.update(db, db_obj=db_obj, obj_in=update_data)


approval = CRUDApproval(Approval)
