from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app import schemas
from app.crud.base import CRUDBase
from app.models.category import Category
from app.schemas.category import CategoryCreate, CategoryUpdate


class CRUDCategory(CRUDBase[Category, CategoryCreate, CategoryUpdate]):
    async def create(self, db: AsyncSession, *, obj_in: CategoryCreate) -> Category:
        """
        创建一个新分类，并正确处理 MPTT 的父子关系。
        """
        db_obj = Category(
            name=obj_in.name,
            description=obj_in.description,
            category_type=obj_in.category_type,
        )
        if obj_in.parent_id:
            parent = await db.get(Category, obj_in.parent_id)
            if parent:
                db_obj.parent = parent
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_or_create(self, db: AsyncSession, obj_in: CategoryCreate) -> Category:
        """
        获取或创建分类
        """
        existing_category = await self.get_by_name(db, name=obj_in.name)
        if existing_category:
            return existing_category
        return await self.create(db, obj_in=obj_in)

    async def get_by_name(self, db: AsyncSession, *, name: str) -> Category | None:
        """
        根据名称获取分类
        """
        result = await db.execute(select(Category).filter(Category.name == name))
        return result.scalar_one_or_none()

    async def get_category_tree(
        self, db: AsyncSession, *, category_type: str | None = None
    ) -> list[schemas.Category]:
        """
        获取完整的分类树，返回 Pydantic Category 模型
        """
        query = select(Category)
        if category_type:
            query = query.filter(Category.category_type == category_type)

        result = await db.execute(query)
        all_categories = result.scalars().all()

        if not all_categories:
            return []

        node_map = {node.id: node for node in all_categories}
        root_nodes = []
        for node in all_categories:
            if node.parent_id:
                parent = node_map.get(node.parent_id)
                if parent:
                    if not hasattr(parent, "children_list"):
                        parent.children_list = []
                    parent.children_list.append(node)
            else:
                root_nodes.append(node)

        def convert_to_pydantic(node: Category) -> schemas.Category:
            children = (
                [convert_to_pydantic(child) for child in node.children_list]
                if hasattr(node, "children_list")
                else []
            )
            return schemas.Category(
                id=node.id,
                name=node.name,
                description=node.description,
                category_type=node.category_type,
                article_count=node.article_count,
                video_count=node.video_count,
                created_at=node.created_at,
                updated_at=node.updated_at,
                parent_id=node.parent_id,
                children=children,
            )

        return [convert_to_pydantic(root) for root in root_nodes]

    async def get_descendant_ids(self, db: AsyncSession, *, category_id: int) -> list[int]:
        """
        获取一个分类及其所有子孙分类的ID列表
        """
        category = await self.get(db, id=category_id)
        if not category:
            return []

        descendant_ids = [category.id]
        nodes_to_visit = [category]
        while nodes_to_visit:
            current_node = nodes_to_visit.pop(0)
            # Eager load children
            await db.refresh(current_node, attribute_names=["children"])
            for child in current_node.children:
                descendant_ids.append(child.id)
                nodes_to_visit.append(child)
        return descendant_ids


category = CRUDCategory(Category)
