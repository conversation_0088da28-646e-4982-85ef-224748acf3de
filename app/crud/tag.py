from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.tag import Tag, video_tags
from app.models.video import Video
from app.schemas.tag import TagCreate, TagUpdate


class CRUDTag(CRUDBase[Tag, TagCreate, TagUpdate]):
    """标签的 CRUD 操作"""

    async def get_by_name(self, db: AsyncSession, *, name: str) -> Tag | None:
        """根据标签名获取标签

        Args:
            db: 数据库会话
            name: 标签名

        Returns:
            标签对象，如果不存在则返回 None
        """
        result = await db.execute(select(self.model).where(self.model.name == name))
        return result.scalar_one_or_none()

    async def get_or_create(self, db: AsyncSession, *, name: str, is_default: bool = False) -> Tag:
        """获取标签，如果不存在则创建

        Args:
            db: 数据库会话
            name: 标签名
            is_default: 是否为默认标签

        Returns:
            标签对象
        """
        tag = await self.get_by_name(db, name=name)
        if not tag:
            tag = await self.create(db, obj_in=TagCreate(name=name, is_default=is_default))
        return tag

    async def get_or_create_multi(
        self, db: AsyncSession, *, names: list[str], is_default: bool = False
    ) -> list[Tag]:
        """获取多个标签，不存在的标签会被创建

        Args:
            db: 数据库会话
            names: 标签名列表
            is_default: 是否为默认标签

        Returns:
            标签对象列表
        """
        tags = []
        for name in names:
            tag = await self.get_or_create(db, name=name, is_default=is_default)
            tags.append(tag)
        return tags

    async def get_top_tags_in_category(
        self, db: AsyncSession, *, category_id: int, limit: int = 10
    ):
        # 获取该分类下使用最频繁的标签ID
        top_tags_query = (
            select(
                video_tags.c.tag_id,
                func.count(video_tags.c.tag_id).label("tag_count"),
            )
            .join(Video, Video.id == video_tags.c.video_id)
            .filter(Video.category_id == category_id)
            .group_by(video_tags.c.tag_id)
            .order_by(func.count(video_tags.c.tag_id).desc())
            .limit(limit)
        )
        result = await db.execute(top_tags_query)
        top_tag_ids = result.all()

        tag_ids = [item.tag_id for item in top_tag_ids]
        if not tag_ids:
            return []

        # 根据标签ID获取标签信息
        tags_query = select(Tag).filter(Tag.id.in_(tag_ids))
        result = await db.execute(tags_query)
        return result.scalars().all()

    async def get_default_tags(self, db: AsyncSession) -> list[Tag]:
        """
        获取所有 is_default 为 True 的标签
        """
        result = await db.execute(select(self.model).where(self.model.is_default))
        return result.scalars().all()


tag = CRUDTag(Tag)
