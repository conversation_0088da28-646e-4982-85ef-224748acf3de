from datetime import datetime, timed<PERSON>ta
from typing import Any

from sqlalchemy import and_, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.like import Like
from app.models.outbox import OutboxMessage
from app.schemas.like import LikeCreate, LikeToggle


class CRUDLike(CRUDBase[Like, LikeCreate, LikeToggle]):
    """点赞CRUD操作"""

    async def get_by_user_and_content(
        self, db: AsyncSession, *, user_id: int, content_type: str, content_id: int
    ) -> Like | None:
        """根据用户和内容获取点赞记录"""
        result = await db.execute(
            select(self.model).where(
                and_(
                    self.model.user_id == user_id,
                    self.model.content_type == content_type,
                    self.model.content_id == content_id,
                )
            )
        )
        return result.scalar_one_or_none()

    async def toggle_like(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        content_type: str,
        content_id: int,
        commit: bool = False,
    ) -> tuple[Like, bool]:
        """
        切换点赞状态，并创建统计更新事件。
        注意：此方法现在默认不提交事务。
        """
        existing_like = await self.get_by_user_and_content(
            db, user_id=user_id, content_type=content_type, content_id=content_id
        )

        is_new_like_or_reactivated = False
        value_change = 0

        if existing_like:
            # 切换激活状态
            is_currently_active = existing_like.is_active
            existing_like.is_active = not is_currently_active
            existing_like.updated_at = datetime.utcnow()
            db.add(existing_like)
            is_new_like_or_reactivated = existing_like.is_active
            value_change = 1 if is_new_like_or_reactivated else -1
            like_object_to_return = existing_like
        else:
            # 创建新的点赞记录
            like_data = {
                "user_id": user_id,
                "content_type": content_type,
                "content_id": content_id,
                "is_active": True,
            }
            new_like = self.model(**like_data)
            db.add(new_like)
            is_new_like_or_reactivated = True
            value_change = 1
            like_object_to_return = new_like

        # 创建统计更新事件
        stats_event = OutboxMessage(
            topic="stats.update",
            payload={
                "entity_type": content_type,
                "entity_id": content_id,
                "field_name": "likes_count",
                "value_change": value_change,
            },
        )
        db.add(stats_event)

        # 如果是新点赞或重新激活，创建通知事件
        if is_new_like_or_reactivated:
            notification_event = OutboxMessage(
                topic="like.created",
                payload={
                    "liker_user_id": user_id,
                    "content_type": content_type,
                    "content_id": content_id,
                },
            )
            db.add(notification_event)

        if commit:
            await db.commit()
            await db.refresh(like_object_to_return)

        return like_object_to_return, is_new_like_or_reactivated

    async def get_content_like_count(
        self, db: AsyncSession, *, content_type: str, content_id: int
    ) -> int:
        """获取内容的点赞数量"""
        result = await db.execute(
            select(func.count(self.model.id)).where(
                and_(
                    self.model.content_type == content_type,
                    self.model.content_id == content_id,
                    self.model.is_active,
                )
            )
        )
        return result.scalar() or 0

    async def is_liked_by_user(
        self, db: AsyncSession, *, user_id: int, content_type: str, content_id: int
    ) -> bool:
        """检查用户是否已点赞"""
        like = await self.get_by_user_and_content(
            db, user_id=user_id, content_type=content_type, content_id=content_id
        )
        return like is not None and like.is_active

    async def get_user_liked_content_ids(
        self, db: AsyncSession, *, user_id: int, content_type: str
    ) -> list[int]:
        """获取用户点赞的特定类型内容的ID列表"""
        result = await db.execute(
            select(self.model.content_id).where(
                and_(
                    self.model.user_id == user_id,
                    self.model.content_type == content_type,
                    self.model.is_active,
                )
            )
        )
        return result.scalars().all()

    async def get_like_stats(
        self, db: AsyncSession, *, content_type: str | None = None
    ) -> dict[str, int]:
        """获取点赞统计信息"""
        now = datetime.utcnow()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        week_start = today_start - timedelta(days=now.weekday())
        month_start = today_start.replace(day=1)

        base_query = select(func.count(self.model.id)).where(self.model.is_active)

        if content_type:
            base_query = base_query.where(self.model.content_type == content_type)

        # 获取总数
        total_result = await db.execute(base_query)
        total = total_result.scalar() or 0

        # 获取今日数量
        today_result = await db.execute(base_query.where(self.model.created_at >= today_start))
        today = today_result.scalar() or 0

        # 获取本周数量
        week_result = await db.execute(base_query.where(self.model.created_at >= week_start))
        this_week = week_result.scalar() or 0

        # 获取本月数量
        month_result = await db.execute(base_query.where(self.model.created_at >= month_start))
        this_month = month_result.scalar() or 0

        return {
            "total_likes": total,
            "today_likes": today,
            "this_week_likes": this_week,
            "this_month_likes": this_month,
        }

    async def get_content_likes_batch(
        self, db: AsyncSession, *, content_items: list[tuple[str, int]], user_id: int | None = None
    ) -> dict[tuple[str, int], dict[str, Any]]:
        """批量获取内容的点赞信息

        Args:
            content_items: [(content_type, content_id), ...] 列表
            user_id: 可选的用户ID，用于检查用户是否已点赞

        Returns:
            {(content_type, content_id): {"like_count": int, "is_liked": bool}}
        """
        if not content_items:
            return {}

        # 构建查询条件
        conditions = []
        for content_type, content_id in content_items:
            conditions.append(
                and_(
                    self.model.content_type == content_type,
                    self.model.content_id == content_id,
                )
            )

        # 获取点赞统计
        like_counts_result = await db.execute(
            select(
                self.model.content_type,
                self.model.content_id,
                func.count(self.model.id).label("like_count"),
            )
            .where(
                and_(
                    self.model.is_active,
                    or_(*conditions),
                )
            )
            .group_by(self.model.content_type, self.model.content_id)
        )
        like_counts = like_counts_result.all()

        # 构建结果字典
        result = {}
        for content_type, content_id in content_items:
            result[(content_type, content_id)] = {
                "like_count": 0,
                "is_liked": False,
            }

        # 填充点赞数量
        for content_type, content_id, like_count in like_counts:
            result[(content_type, content_id)]["like_count"] = like_count

        # 如果提供了用户ID，检查用户点赞状态
        if user_id:
            user_likes_result = await db.execute(
                select(self.model.content_type, self.model.content_id).where(
                    and_(
                        self.model.user_id == user_id,
                        self.model.is_active,
                        or_(*conditions),
                    )
                )
            )
            user_likes = user_likes_result.all()

            for content_type, content_id in user_likes:
                if (content_type, content_id) in result:
                    result[(content_type, content_id)]["is_liked"] = True

        return result


like = CRUDLike(Like)
