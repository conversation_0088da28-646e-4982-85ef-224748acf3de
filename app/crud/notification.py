"""通知CRUD操作"""

from datetime import datetime, timedelta

from sqlalchemy import and_, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.notifications.models import (
    Notification,
    NotificationStatus,
)
from app.notifications.schemas import NotificationCreate, NotificationUpdate


class CRUDNotification(CRUDBase[Notification, NotificationCreate, NotificationUpdate]):
    async def create_notification(
        self, db: AsyncSession, *, obj_in: NotificationCreate
    ) -> Notification:
        """创建通知，并原子性地创建发件箱消息"""
        # 计算过期时间
        expires_at = None
        if obj_in.expires_in_hours:
            expires_at = datetime.utcnow() + timedelta(hours=obj_in.expires_in_hours)

        # 创建通知对象
        notification_data = obj_in.model_dump(exclude={"expires_in_hours"})
        notification_data["expires_at"] = expires_at

        db_obj = self.model(**notification_data)
        db.add(db_obj)

        # 创建发件箱消息用于实时推送
        self._create_event(
            db,
            topic="notification.created",
            payload={
                "notification_id": db_obj.id,
                "user_id": db_obj.user_id,
                "type": db_obj.type.value,
                "priority": db_obj.priority.value,
            },
        )

        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_user_notifications(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        status: NotificationStatus | None = None,
        limit: int = 20,
        offset: int = 0,
        include_expired: bool = False,
    ) -> tuple[list[Notification], int]:
        """获取用户通知列表"""
        # 构建查询条件
        conditions = [Notification.user_id == user_id]

        if status:
            conditions.append(Notification.status == status)

        if not include_expired:
            conditions.append(
                or_(Notification.expires_at.is_(None), Notification.expires_at > datetime.utcnow())
            )

        # 查询通知列表
        stmt = (
            select(Notification)
            .where(and_(*conditions))
            .order_by(Notification.created_at.desc())
            .limit(limit)
            .offset(offset)
        )

        result = await db.execute(stmt)
        notifications = result.scalars().all()

        # 查询总数
        count_stmt = select(func.count(Notification.id)).where(and_(*conditions))
        count_result = await db.execute(count_stmt)
        total = count_result.scalar()

        return notifications, total

    async def get_unread_count(self, db: AsyncSession, *, user_id: int) -> int:
        """获取用户未读通知数量"""
        stmt = select(func.count(Notification.id)).where(
            and_(
                Notification.user_id == user_id,
                Notification.status == "unread",
                or_(Notification.expires_at.is_(None), Notification.expires_at > datetime.utcnow()),
            )
        )
        result = await db.execute(stmt)
        return result.scalar() or 0

    async def mark_as_read(
        self, db: AsyncSession, *, notification_id: int, user_id: int
    ) -> Notification | None:
        """标记通知为已读"""
        stmt = select(Notification).where(
            and_(Notification.id == notification_id, Notification.user_id == user_id)
        )
        result = await db.execute(stmt)
        notification = result.scalar_one_or_none()

        if notification and notification.status == "unread":
            notification.status = "read"
            notification.read_at = datetime.utcnow()
            db.add(notification)

            # 创建发件箱消息用于更新未读数量
            self._create_event(
                db,
                topic="notification.read",
                payload={"notification_id": notification.id, "user_id": user_id},
            )

            await db.commit()
            await db.refresh(notification)

        return notification

    async def mark_all_as_read(self, db: AsyncSession, *, user_id: int) -> int:
        """标记用户所有未读通知为已读"""
        from sqlalchemy import update

        stmt = (
            update(Notification)
            .where(
                and_(
                    Notification.user_id == user_id,
                    Notification.status == "unread",
                    or_(
                        Notification.expires_at.is_(None),
                        Notification.expires_at > datetime.utcnow(),
                    ),
                )
            )
            .values(status="read", read_at=datetime.utcnow())
        )

        result = await db.execute(stmt)
        affected_rows = result.rowcount

        if affected_rows > 0:
            # 创建发件箱消息用于更新未读数量
            self._create_event(
                db,
                topic="notification.all_read",
                payload={"user_id": user_id, "count": affected_rows},
            )

        await db.commit()
        return affected_rows

    async def delete_notification(
        self, db: AsyncSession, *, notification_id: int, user_id: int
    ) -> bool:
        """删除通知（软删除）"""
        stmt = select(Notification).where(
            and_(Notification.id == notification_id, Notification.user_id == user_id)
        )
        result = await db.execute(stmt)
        notification = result.scalar_one_or_none()

        if notification:
            notification.status = "deleted"
            db.add(notification)
            await db.commit()
            return True

        return False

    async def cleanup_expired_notifications(self, db: AsyncSession) -> int:
        """清理过期通知"""
        from sqlalchemy import delete

        stmt = delete(Notification).where(
            and_(Notification.expires_at.is_not(None), Notification.expires_at < datetime.utcnow())
        )

        result = await db.execute(stmt)
        await db.commit()
        return result.rowcount


notification = CRUDNotification(Notification)
