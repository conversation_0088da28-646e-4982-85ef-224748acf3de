from typing import Any

from slugify import slugify
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from app.core.pagination import CursorPaginationParams, CursorPaginationResponse
from app.crud.base import CRUDBase
from app.models.video import Video
from app.schemas.video import VideoBase, VideoCreate, VideoUpdate


class CRUDVideo(CRUDBase[Video, VideoCreate, VideoUpdate]):
    async def get(self, db: AsyncSession, id: Any) -> Video | None:
        """
        获取单个视频，并预加载 tags 和 category
        """
        query = select(self.model).where(self.model.id == id)
        query = query.options(joinedload(self.model.tags), joinedload(self.model.category))
        result = await db.execute(query)
        return result.unique().scalar_one_or_none()

    async def get_by_title(self, db: AsyncSession, *, title: str) -> Video | None:
        """根据标题获取视频"""
        result = await db.execute(select(self.model).where(self.model.title == title))
        return result.scalar_one_or_none()

    async def get_multi_by_author(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取指定作者的视频列表"""
        result = await db.execute(
            select(self.model).where(self.model.author_id == author_id).offset(skip).limit(limit)
        )
        return result.scalars().all()

    async def get_published(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取已发布的视频列表"""
        result = await db.execute(
            select(self.model).where(self.model.is_published).offset(skip).limit(limit)
        )
        return result.scalars().all()

    async def get_by_folder(
        self, db: AsyncSession, *, folder_id: int, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取指定文件夹下的视频列表"""
        result = await db.execute(
            select(self.model).where(self.model.folder_id == folder_id).offset(skip).limit(limit)
        )
        return result.scalars().all()

    async def get_multi_by_ids(self, db: AsyncSession, *, ids: list[int]) -> list[Video]:
        """根据ID列表获取多个视频 - 包含业务逻辑过滤"""
        if not ids:
            return []
        query = (
            select(self.model)
            .where(
                self.model.id.in_(ids),
                self.model.is_published,
                self.model.is_approved,
                not self.model.is_deleted,
            )
            .options(joinedload(self.model.tags), joinedload(self.model.category))
        )
        result = await db.execute(query)
        return result.unique().scalars().all()

    async def get_multi_by_ids_raw(self, db: AsyncSession, *, ids: list[int]) -> list[Video]:
        """根据ID列表获取多个视频 - 无业务逻辑过滤，供缓存服务使用"""
        if not ids:
            return []
        query = (
            select(self.model)
            .where(
                self.model.id.in_(ids),
                self.model.is_deleted.is_(False),  # 只过滤软删除的记录
            )
            .options(joinedload(self.model.tags), joinedload(self.model.category))
        )
        result = await db.execute(query)
        return result.unique().scalars().all()

    async def get_multi_by_ids_with_status(
        self, db: AsyncSession, *, ids: list[int], status: str
    ) -> list[Video]:
        """根据ID列表和状态获取多个视频 - 支持状态过滤"""
        if not ids:
            return []

        query = select(self.model).where(
            self.model.id.in_(ids),
            not self.model.is_deleted,  # 只过滤软删除的记录
        )

        # 根据状态添加过滤条件
        if status == "draft":
            query = query.where(self.model.is_published.is_(False))
        elif status == "published_pending" or status == "published_rejected":
            query = query.where(
                self.model.is_published.is_(True), self.model.is_approved.is_(False)
            )
        elif status == "published_approved":
            query = query.where(self.model.is_published.is_(True), self.model.is_approved.is_(True))

        query = query.options(joinedload(self.model.tags), joinedload(self.model.category))
        result = await db.execute(query)
        return result.unique().scalars().all()

    async def update_publish_status(
        self, db: AsyncSession, *, db_obj: Video, is_published: bool
    ) -> Video:
        """更新视频发布状态"""
        db_obj.is_published = is_published
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_pending_review(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取指定作者的待审核视频列表

        Args:
            db: 数据库会话
            author_id: 作者ID
            skip: 跳过的记录数
            limit: 返回的最大记录数

        Returns:
            待审核视频列表
        """
        result = await db.execute(
            select(self.model)
            .where(self.model.author_id == author_id)
            .where(self.model.is_published & ~self.model.is_approved)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_drafts(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取指定作者的草稿列表

        Args:
            db: 数据库会话
            author_id: 作者ID
            skip: 跳过的记录数
            limit: 返回的最大记录数

        Returns:
            草稿列表
        """
        result = await db.execute(
            select(self.model)
            .where(self.model.author_id == author_id)
            .where(~self.model.is_published)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def create(self, db: AsyncSession, *, obj_in: VideoCreate, commit: bool = True) -> Video:
        """创建视频"""
        create_data = obj_in.model_dump()
        if "title" in create_data:
            create_data["slug"] = slugify(create_data["title"])

        db_obj = self.model(**create_data)
        db.add(db_obj)
        if commit:
            await db.commit()
            await db.refresh(db_obj)
        return db_obj

    async def update(
        self,
        db: AsyncSession,
        *,
        db_obj: Video,
        obj_in: VideoUpdate | dict[str, Any],
        commit: bool = True,
    ) -> Video:
        """更新视频"""
        # 如果标题被更新，则重新生成slug
        update_data = obj_in if isinstance(obj_in, dict) else obj_in.model_dump(exclude_unset=True)
        if "title" in update_data:
            update_data["slug"] = slugify(update_data["title"])

        # 调用父类的更新方法
        return await super().update(db, db_obj=db_obj, obj_in=update_data, commit=commit)

    async def soft_remove(self, db: AsyncSession, *, id: int, commit: bool = True) -> Video | None:
        """软删除视频"""
        db_obj = await self.get(db, id=id)
        if not db_obj:
            return None

        db_obj.is_deleted = True
        db.add(db_obj)
        if commit:
            await db.commit()
            await db.refresh(db_obj)
        return db_obj

    async def get_visit_counts_batch(
        self, db: AsyncSession, content_items: list[tuple[str, int]]
    ) -> dict[tuple[str, int], int]:
        """批量获取内容的访问次数。"""
        video_ids = [
            content_id for content_type, content_id in content_items if content_type == "video"
        ]
        if not video_ids:
            return {}

        result = await db.execute(
            select(self.model.id, self.model.visit_count).where(self.model.id.in_(video_ids))
        )

        # 构建一个以 (content_type, content_id) 为键的字典
        return {("video", video_id): visit_count for video_id, visit_count in result.all()}

    async def bulk_update_stats(
        self, db: AsyncSession, stats_data: dict[int, dict[str, int]]
    ) -> None:
        """批量更新视频的统计数据。"""
        if not stats_data:
            return

        # SQLAlchemy 2.0 风格的批量更新
        update_statements = []
        for video_id, metrics in stats_data.items():
            # 过滤掉非法的 metric 名称，以防万一
            allowed_metrics = {
                "like_count": metrics.get("like_count"),
                "favorite_count": metrics.get("favorite_count"),
                "visit_count": metrics.get("visit_count"),
            }
            # 移除值为 None 的项
            update_values = {k: v for k, v in allowed_metrics.items() if v is not None}

            if update_values:
                stmt = update(self.model).where(self.model.id == video_id).values(**update_values)
                update_statements.append(stmt)

        # 一次性执行所有更新
        for stmt in update_statements:
            await db.execute(stmt)

        await db.commit()

    async def get_paginated_video_ids(
        self,
        db: AsyncSession,
        *,
        params: "CursorPaginationParams",
        filters: dict[str, Any] | None = None,
        access_level: str = "public",
    ) -> CursorPaginationResponse[int]:
        """
        高效获取分页后的视频ID列表和分页元数据。

        :param db: 数据库会话
        :param params: 游标分页参数
        :param filters: 筛选条件字典
        :param access_level: 访问级别 ('public', 'owner', 'admin')
        :return: 包含视频ID列表和分页信息的响应对象
        """
        from app.core.pagination import CursorPaginator

        query = select(self.model.id)

        if filters:
            if "category_id" in filters:
                query = query.where(self.model.category_id == filters["category_id"])
            if "author_id" in filters:
                query = query.where(self.model.author_id == filters["author_id"])
            if "folder_id" in filters:
                query = query.where(self.model.folder_id == filters["folder_id"])
            # 可以根据需要添加更多筛选条件

        # 根据访问级别应用默认过滤
        if access_level == "public":
            query = query.where(self.model.is_published.is_(True), self.model.is_approved.is_(True))

        query = query.where(self.model.is_deleted.is_(False))

        # 使用游标分页器获取ID
        paginated_result = await CursorPaginator.paginate_query(
            db=db,
            query=query,
            params=params,
            cursor_field=params.order_by,
            include_total=True,  # 我们需要总数来进行分页计算
        )

        # paginate_query 返回的 items 已经是 ID 列表，直接返回即可
        return paginated_result

    async def get_paginated_videos(
        self,
        db: AsyncSession,
        *,
        params: "CursorPaginationParams",
        filters: dict[str, Any] | None = None,
        can_view_all: bool = False,
    ) -> CursorPaginationResponse[VideoBase]:
        """
        获取分页后的视频对象列表，支持灵活的筛选条件。

        :param db: 数据库会话
        :param params: 游标分页参数
        :param filters: 筛选条件字典 (e.g., {"folder_id": 1, "status": "published_approved"})
        :param can_view_all: 是否可以查看所有状态的视频（如作者或管理员）
        :return: 包含视频对象列表和分页信息的响应对象
        """
        from app.core.pagination import CursorPaginator
        from app.schemas.video import VideoStatus

        query = select(self.model)

        if filters:
            if "folder_id" in filters:
                query = query.where(self.model.folder_id == filters["folder_id"])
            if "author_id" in filters:
                query = query.where(self.model.author_id == filters["author_id"])

            if "status" in filters and filters["status"] is not None:
                status = filters["status"]
                if status == VideoStatus.DRAFT:
                    query = query.where(self.model.is_published.is_(False))
                elif status == VideoStatus.PUBLISHED_PENDING:
                    query = query.where(
                        self.model.is_published.is_(True), self.model.is_approved.is_(False)
                    )
                elif status == VideoStatus.PUBLISHED_REJECTED:
                    query = query.where(
                        self.model.is_published.is_(True), self.model.is_approved.is_(False)
                    )  # Assuming rejected is a form of not approved
                elif status == VideoStatus.PUBLISHED_APPROVED:
                    query = query.where(
                        self.model.is_published.is_(True), self.model.is_approved.is_(True)
                    )

        if not can_view_all:
            query = query.where(self.model.is_published.is_(True), self.model.is_approved.is_(True))

        query = query.where(self.model.is_deleted.is_(False))

        paginated_result = await CursorPaginator.paginate_query(
            db=db,
            query=query,
            params=params,
            cursor_field=params.order_by,
            include_total=True,
        )
        return paginated_result

    async def get_paginated_video_ids_by_user_interaction(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        interaction_type: str,
        params: "CursorPaginationParams",
    ) -> tuple[list[int], int | None]:
        """
        根据用户交互（点赞、收藏、历史）获取分页的视频ID列表。
        """
        from sqlalchemy import and_

        from app.core.pagination import CursorPaginator
        from app.models import Favorite, History, Like

        interaction_model_map = {
            "like": Like,
            "favorite": Favorite,
            "history": History,
        }

        interaction_model = interaction_model_map.get(interaction_type)
        if not interaction_model:
            return [], 0

        # 构建基础的JOIN查询
        query = select(self.model.id).join(
            interaction_model,
            and_(
                self.model.id == interaction_model.content_id,
                interaction_model.content_type == "video",
                interaction_model.user_id == user_id,
            ),
        )

        # 为点赞和收藏添加 is_active 过滤
        if interaction_type in ["like", "favorite"]:
            query = query.where(interaction_model.is_active.is_(True))

        # 应用通用过滤条件
        query = query.where(
            self.model.is_published.is_(True),
            self.model.is_approved.is_(True),
            self.model.is_deleted.is_(False),
        )

        # 确定排序字段
        # 对于历史记录，按交互时间排序；对于点赞/收藏，按视频创建时间排序
        if interaction_type == "history":
            # FIXME: 当前分页器不支持对JOIN的字段进行排序，这里会失败
            # 这是一个预先存在的bug，暂时保持原样以修复当前问题
            cursor_field_name = "last_visited_at"
        else:
            getattr(self.model, params.order_by)
            cursor_field_name = params.order_by

        # 使用游标分页器获取ID
        # 注意：当前实现中，当 interaction_type == "history" 时，下面的调用会失败
        # 因为 paginate_query 期望 cursor_field_name 存在于 self.model 上
        paginated_result = await CursorPaginator.paginate_query(
            db=db,
            query=query,
            params=params,
            cursor_field=cursor_field_name,
            include_total=True,
        )

        video_ids = [item[0] for item in paginated_result.items]

        return video_ids, paginated_result.total_count

    async def get_paginated_liked_videos(
        self, db: AsyncSession, *, user_id: int, params: "CursorPaginationParams"
    ) -> CursorPaginationResponse[VideoBase]:
        """获取用户点赞的视频分页列表"""
        from app.core.pagination import CursorPaginator
        from app.models import Like

        query = (
            select(self.model)
            .join(Like, self.model.id == Like.content_id)
            .where(
                Like.user_id == user_id,
                Like.content_type == "video",
                Like.is_active.is_(True),
                self.model.is_deleted.is_(False),
                self.model.is_published.is_(True),
                self.model.is_approved.is_(True),
            )
        )
        return await CursorPaginator.paginate_query(
            db, query, params, cursor_field="like_time", join_model=Like, include_total=True
        )

    async def get_paginated_favorite_videos(
        self, db: AsyncSession, *, user_id: int, params: "CursorPaginationParams"
    ) -> CursorPaginationResponse[VideoBase]:
        """获取用户收藏的视频分页列表"""
        from app.core.pagination import CursorPaginator
        from app.models import Favorite

        query = (
            select(self.model)
            .join(Favorite, self.model.id == Favorite.content_id)
            .where(
                Favorite.user_id == user_id,
                Favorite.content_type == "video",
                Favorite.is_active.is_(True),
                self.model.is_deleted.is_(False),
                self.model.is_published.is_(True),
                self.model.is_approved.is_(True),
            )
        )
        return await CursorPaginator.paginate_query(
            db, query, params, cursor_field="favorite_time", join_model=Favorite, include_total=True
        )

    async def get_paginated_history_videos(
        self, db: AsyncSession, *, user_id: int, params: "CursorPaginationParams"
    ) -> CursorPaginationResponse[VideoBase]:
        """获取用户历史记录的视频分页列表"""
        from app.core.pagination import CursorPaginator
        from app.models import History

        query = (
            select(self.model)
            .join(History, self.model.id == History.content_id)
            .where(
                History.user_id == user_id,
                History.content_type == "video",
                self.model.is_deleted.is_(False),
                self.model.is_published.is_(True),
                self.model.is_approved.is_(True),
            )
        )
        return await CursorPaginator.paginate_query(
            db, query, params, cursor_field="history_time", join_model=History, include_total=True
        )


video = CRUDVideo(Video)
