from typing import Any

from sqlalchemy import and_, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app import models, schemas
from app.core.pagination import (
    CursorPaginationParams,
    CursorPaginationResponse,
    CursorPaginator,
)
from app.crud.base import CRUDBase
from app.models.article import Article
from app.models.review import ContentType
from app.schemas.article import ArticleBase, ArticleCreate, ArticleUpdate


class CRUDArticle(CRUDBase[Article, ArticleCreate, ArticleUpdate]):
    async def create(self, db: AsyncSession, *, obj_in: ArticleCreate) -> Article:
        """创建文章，并原子性地创建发件箱消息"""
        # 调用基类的创建方法，但不提交
        db_obj = await super().create(db, obj_in=obj_in, commit=False)

        # 创建发件箱消息
        self._create_event(db, topic="article.created", payload={"article_id": db_obj.id})

        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_by_title(self, db: AsyncSession, *, title: str) -> Article | None:
        """根据标题获取文章"""
        result = await db.execute(select(self.model).where(self.model.title == title))
        return result.scalar_one_or_none()

    async def update(
        self,
        db: AsyncSession,
        *,
        db_obj: Article,
        obj_in: ArticleUpdate | dict[str, Any],
        commit: bool = True,
    ) -> Article:
        """更新文章，并增加缓存版本号"""
        db_obj.cache_version += 1
        return await super().update(db, db_obj=db_obj, obj_in=obj_in, commit=commit)

    async def get_multi_by_author(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Article]:
        """获取指定作者的文章列表"""
        result = await db.execute(
            select(self.model)
            .options(
                selectinload(self.model.tags),
                selectinload(self.model.author),
            )
            .where(self.model.author_id == author_id)
            .offset(skip)
            .limit(limit)
        )

        return result.scalars().all()

    async def get_multi_by_ids(self, db: AsyncSession, *, ids: list[int]) -> list[Article]:
        """根据ID列表批量获取文章

        Args:
            db: 数据库会话
            ids: 文章ID列表

        Returns:
            文章列表，按照传入的ID顺序排序
        """
        if not ids:
            return []

        result = await db.execute(
            select(self.model)
            .options(
                selectinload(self.model.tags),
                selectinload(self.model.author),
                selectinload(self.model.category),
                selectinload(self.model.reviews),  # 预加载审核信息
            )
            .where(self.model.id.in_(ids))
        )

        articles = result.scalars().all()

        # 按照传入的ID顺序重新排序
        articles_dict = {article.id: article for article in articles}
        ordered_articles = [articles_dict[id] for id in ids if id in articles_dict]

        return ordered_articles

    async def get_published(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> list[Article]:
        """获取已发布的文章列表"""
        result = await db.execute(
            select(self.model)
            .options(
                selectinload(self.model.tags),
            )
            .where(self.model.is_published)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def update_publish_status(
        self, db: AsyncSession, *, db_obj: Article, is_published: bool
    ) -> Article:
        """更新文章发布状态"""
        db_obj.is_published = is_published
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_multi_with_filters(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100, **filters
    ) -> list[Article]:
        """根据过滤条件获取文章列表

        Args:
            db: 数据库会话
            skip: 跳过的记录数
            limit: 返回的最大记录数
            **filters: 过滤条件，支持的字段：
                - author_id: 作者ID
                - category_id: 分类ID
                - is_published: 是否发布
                - is_approved: 是否审核通过
                - title: 标题（模糊匹配）

        Returns:
            文章列表
        """
        query = select(self.model).options(
            selectinload(self.model.tags),
            selectinload(self.model.author),
            selectinload(self.model.category),
        )

        # 应用过滤条件
        for field, value in filters.items():
            if hasattr(self.model, field) and value is not None:
                if field == "title":
                    # 标题模糊匹配
                    query = query.where(self.model.title.contains(value))
                else:
                    # 精确匹配
                    query = query.where(getattr(self.model, field) == value)

        # 排序、分页
        query = query.order_by(self.model.updated_at.desc()).offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    async def count_with_filters(self, db: AsyncSession, **filters) -> int:
        """根据过滤条件统计文章数量

        Args:
            db: 数据库会话
            **filters: 过滤条件，与get_multi_with_filters相同

        Returns:
            符合条件的文章数量
        """
        query = select(func.count()).select_from(self.model)

        # 应用过滤条件
        for field, value in filters.items():
            if hasattr(self.model, field) and value is not None:
                if field == "title":
                    # 标题模糊匹配
                    query = query.where(self.model.title.contains(value))
                else:
                    # 精确匹配
                    query = query.where(getattr(self.model, field) == value)

        result = await db.execute(query)
        return result.scalar()

    async def get_paginated_category_articles(
        self,
        db: AsyncSession,
        *,
        category_id: int,
        params: CursorPaginationParams,
        include_total: bool = True,
    ) -> CursorPaginationResponse[ArticleBase]:
        """
        获取特定分类下的文章列表（高效分页版）
        - 仅返回已发布、已审核且未删除的文章
        """
        # 构建基础查询
        query = select(self.model).where(
            self.model.category_id == category_id,
            self.model.is_published.is_(True),
            self.model.is_approved.is_(True),
            self.model.is_deleted.is_(False),
        )

        # 排序逻辑
        cursor_field = getattr(self.model, params.order_by, self.model.id)

        # 预加载关联数据以避免 N+1 问题
        query = query.options(
            selectinload(self.model.category),
        )

        # 使用游标分页器
        paginated_result = await CursorPaginator.paginate_query(
            db=db,
            query=query,
            params=params,
            cursor_field=cursor_field,
            include_total=include_total,
        )

        return paginated_result

    async def get_paginated_published_articles(
        self,
        db: AsyncSession,
        *,
        params: CursorPaginationParams,
        include_total: bool = True,
    ) -> CursorPaginationResponse[ArticleBase]:
        """
        获取所有已发布且已审核的文章列表（不区分分类）
        - 仅返回已发布、已审核且未删除的文章
        - 支持分页
        """
        # 构建基础查询
        query = select(self.model).where(
            self.model.is_published.is_(True),
            self.model.is_approved.is_(True),
            self.model.is_deleted.is_(False),
        )

        # 排序逻辑
        cursor_field = getattr(self.model, params.order_by, self.model.id)

        # 预加载关联数据以避免 N+1 问题
        query = query.options(
            selectinload(self.model.category),
            selectinload(self.model.author),
            selectinload(self.model.tags),
        )

        # 使用游标分页器
        paginated_result = await CursorPaginator.paginate_query(
            db=db,
            query=query,
            params=params,
            cursor_field=cursor_field,
            include_total=include_total,
        )

        return paginated_result

    async def get_paginated_liked_articles(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        params: CursorPaginationParams,
    ) -> CursorPaginationResponse[ArticleBase]:
        """
        获取用户点赞的文章列表（高效分页版）
        - 使用 JOIN 查询直接在数据库层面进行分页
        - 支持按点赞时间排序
        """
        # 构建基础查询，通过 JOIN 关联 Like 表
        query = (
            select(self.model)
            .join(
                models.Like,
                and_(
                    models.Like.content_id == self.model.id,
                    models.Like.content_type == ContentType.ARTICLE,
                ),
            )
            .where(
                models.Like.user_id == user_id,
                models.Like.is_active.is_(True),
                self.model.is_published.is_(True),
                self.model.is_approved.is_(True),
                self.model.is_deleted.is_(False),
            )
        )

        # --- 排序逻辑 ---
        # 默认按文章ID排序，如果指定了 like_time，则按点赞时间排序
        if params.order_by == "like_time":
            cursor_field = models.Like.created_at
        else:
            cursor_field = getattr(self.model, params.order_by, self.model.id)

        # 预加载关联数据以避免 N+1 问题
        query = query.options(
            selectinload(self.model.author),
            selectinload(self.model.tags),
            selectinload(self.model.category),
        )

        # 使用升级后的游标分页器
        paginated_result = await CursorPaginator.paginate_query(
            db=db,
            query=query,
            params=params,
            cursor_field=cursor_field,
            include_total=True,
        )

        return paginated_result

    async def get_paginated_favorite_articles(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        params: CursorPaginationParams,
    ) -> CursorPaginationResponse[ArticleBase]:
        """
        获取用户收藏的文章列表（高效分页版）
        - 使用 JOIN 查询直接在数据库层面进行分页
        - 支持按收藏时间排序
        """
        # 构建基础查询，通过 JOIN 关联 Favorite 表
        query = (
            select(self.model)
            .join(
                models.Favorite,
                and_(
                    models.Favorite.content_id == self.model.id,
                    models.Favorite.content_type == ContentType.ARTICLE,
                ),
            )
            .where(
                models.Favorite.user_id == user_id,
                models.Favorite.is_active.is_(True),
                self.model.is_published.is_(True),
                self.model.is_approved.is_(True),
                self.model.is_deleted.is_(False),
            )
        )

        # --- 排序逻辑 ---
        # 默认按文章ID排序，如果指定了 favorite_time，则按收藏时间排序
        if params.order_by == "favorite_time":
            cursor_field = models.Favorite.created_at
        else:
            cursor_field = getattr(self.model, params.order_by, self.model.id)

        # 预加载关联数据以避免 N+1 问题
        query = query.options(
            selectinload(self.model.author),
            selectinload(self.model.tags),
            selectinload(self.model.category),
        )

        # 使用升级后的游标分页器
        paginated_result = await CursorPaginator.paginate_query(
            db=db,
            query=query,
            params=params,
            cursor_field=cursor_field,
            include_total=True,
        )

        return paginated_result

    async def get_paginated_history_articles(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        params: CursorPaginationParams,
    ) -> CursorPaginationResponse[ArticleBase]:
        """
        获取用户浏览历史的文章列表（高效分页版）
        - 使用 JOIN 查询直接在数据库层面进行分页
        - 支持按浏览时间排序
        """
        # 构建基础查询，通过 JOIN 关联 History 表
        query = (
            select(self.model)
            .join(
                models.History,
                and_(
                    models.History.content_id == self.model.id,
                    models.History.content_type == ContentType.ARTICLE,
                ),
            )
            .where(
                models.History.user_id == user_id,
                self.model.is_published.is_(True),
                self.model.is_approved.is_(True),
                self.model.is_deleted.is_(False),
            )
        )

        # --- 排序逻辑 ---
        # 默认按文章ID排序，如果指定了 history_time，则按浏览时间排序
        if params.order_by == "history_time":
            cursor_field = models.History.updated_at
        else:
            cursor_field = getattr(self.model, params.order_by, self.model.id)

        # 预加载关联数据以避免 N+1 问题
        query = query.options(
            selectinload(self.model.author),
            selectinload(self.model.tags),
            selectinload(self.model.category),
        )

        # 使用升级后的游标分页器
        paginated_result = await CursorPaginator.paginate_query(
            db=db,
            query=query,
            params=params,
            cursor_field=cursor_field,
            include_total=True,
        )

        return paginated_result

    async def get_paginated_articles(
        self,
        db: AsyncSession,
        *,
        params: CursorPaginationParams,
        filters: dict[str, Any],
        can_view_all: bool,
        include_total: bool,
    ) -> CursorPaginationResponse[ArticleBase]:
        """获取分页的文章列表，支持多种过滤条件"""
        query = select(self.model).options(
            selectinload(self.model.author),
            selectinload(self.model.tags),
            selectinload(self.model.category),
        )

        # 应用过滤条件
        if author_id := filters.get("author_id"):
            query = query.where(self.model.author_id == author_id)

        status = filters["status"]  # 直接获取，因为API层已保证其存在

        if status == schemas.article.ArticleStatus.ALL:
            if not can_view_all:
                # 非作者或管理员只能看已发布和审核通过的
                query = query.where(
                    self.model.is_published.is_(True),
                    self.model.is_approved.is_(True),
                    self.model.is_deleted.is_(False),
                )
        elif status == schemas.article.ArticleStatus.DRAFT:
            query = query.where(
                self.model.is_published.is_(False),
                self.model.is_deleted.is_(False),
            )
        elif status == schemas.article.ArticleStatus.PUBLISHED_APPROVED:
            query = query.where(
                self.model.is_published.is_(True),
                self.model.is_approved.is_(True),
                self.model.is_deleted.is_(False),
            )
        elif status == schemas.article.ArticleStatus.PUBLISHED_PENDING:
            query = query.where(
                self.model.is_published.is_(True),
                self.model.is_approved.is_(False),
                self.model.is_deleted.is_(False),
            )

        elif status == schemas.article.ArticleStatus.PUBLISHED_REJECTED:
            # 假设审核拒绝的文章也是 is_published=True, is_approved=False
            # 如果有单独的拒绝状态字段，应使用该字段
            query = query.where(
                self.model.is_published.is_(True),
                self.model.is_approved.is_(False),
                self.model.is_deleted.is_(False),
            )

        # 排序
        order_by_field = getattr(self.model, params.order_by, self.model.id)

        # 使用升级后的游标分页器
        return await CursorPaginator.paginate_query(
            db=db,
            query=query,
            params=params,
            cursor_field=order_by_field,
            include_total=include_total,
        )

    async def bulk_update_stats(
        self, db: AsyncSession, stats_data: dict[int, dict[str, int]]
    ) -> None:
        """批量更新文章的统计数据。"""
        if not stats_data:
            return

        # SQLAlchemy 2.0 风格的批量更新
        from sqlalchemy import update

        update_statements = []
        for article_id, metrics in stats_data.items():
            allowed_metrics = {
                "like_count": metrics.get("like_count"),
                "favorite_count": metrics.get("favorite_count"),
                "visit_count": metrics.get("visit_count"),
            }
            update_values = {k: v for k, v in allowed_metrics.items() if v is not None}

            if update_values:
                stmt = update(self.model).where(self.model.id == article_id).values(**update_values)
                update_statements.append(stmt)

        for stmt in update_statements:
            await db.execute(stmt)

        await db.commit()

    async def remove(self, db: AsyncSession, *, id: int) -> Article | None:
        """删除文章（软删除），并原子性地创建发件箱消息"""
        db_obj = await self.get(db, id=id)
        if not db_obj:
            return None

        db_obj.is_deleted = True
        db_obj.cache_version += 1
        db.add(db_obj)

        await db.commit()
        return db_obj


article = CRUDArticle(Article)
