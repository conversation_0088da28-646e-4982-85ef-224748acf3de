from typing import Any, TypeVar

from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import Base
from app.models.outbox import OutboxMessage

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDBase[ModelType: Base, CreateSchemaType: BaseModel, UpdateSchemaType: BaseModel]:
    def __init__(self, model: type[ModelType]):
        """CRUD对象初始化

        Args:
            model: SQLAlchemy模型类
        """
        self.model = model

    def _create_event(self, db: AsyncSession, *, topic: str, payload: dict) -> None:
        """封装创建并添加 OutboxMessage 到会话中的逻辑。"""
        outbox_msg = OutboxMessage(topic=topic, payload=payload)
        db.add(outbox_msg)

    async def get(
        self, db: AsyncSession, id: Any, *, options: list[Any] | None = None
    ) -> ModelType | None:
        """根据ID获取对象

        Args:
            db: 数据库会话
            id: 对象ID
            options: SQLAlchemy查询选项

        Returns:
            查询到的对象，如果不存在则返回None
        """
        stmt = select(self.model).where(self.model.id == id)
        if options:
            stmt = stmt.options(*options)  # 应用加载选项
        result = await db.execute(stmt)
        return result.unique().scalar_one_or_none()

    async def get_multi(
        self,
        db: AsyncSession,
        *,
        skip: int = 0,
        limit: int | None = 100,
        where_conditions: list | None = None,
        order_by: str | None = None,
    ) -> list[ModelType]:
        """获取多个对象

        Args:
            db: 数据库会话
            skip: 跳过的记录数
            limit: 返回的最大记录数, None表示不限制
            where_conditions: SQLAlchemy查询条件列表
            order_by: 排序字段，例如 "id" 或 "-created_at"

        Returns:
            对象列表
        """
        stmt = select(self.model)
        if where_conditions:
            stmt = stmt.where(*where_conditions)

        if order_by:
            if order_by.startswith("-"):
                stmt = stmt.order_by(getattr(self.model, order_by[1:]).desc())
            else:
                stmt = stmt.order_by(getattr(self.model, order_by).asc())

        stmt = stmt.offset(skip)
        if limit is not None:
            stmt = stmt.limit(limit)

        result = await db.execute(stmt)
        return result.scalars().all()

    async def create(
        self, db: AsyncSession, *, obj_in: CreateSchemaType, commit: bool = True
    ) -> ModelType:
        """创建对象

        Args:
            db: 数据库会话
            obj_in: 创建对象的数据
            commit: 是否提交事务

        Returns:
            创建的对象
        """
        obj_in_data = jsonable_encoder(obj_in)
        db_obj = self.model(**obj_in_data)  # type: ignore
        db.add(db_obj)
        if commit:
            await db.commit()
            await db.refresh(db_obj)
        return db_obj

    async def update(
        self,
        db: AsyncSession,
        *,
        db_obj: ModelType,
        obj_in: UpdateSchemaType | dict[str, Any],
        commit: bool = True,
    ) -> ModelType:
        """更新对象

        Args:
            db: 数据库会话
            db_obj: 数据库中的对象
            obj_in: 更新的数据
            commit: 是否提交事务

        Returns:
            更新后的对象
        """
        obj_data = jsonable_encoder(db_obj)
        update_data = obj_in if isinstance(obj_in, dict) else obj_in.model_dump(exclude_unset=True)
        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        db.add(db_obj)
        if commit:
            await db.commit()
            await db.refresh(db_obj)
        return db_obj

    async def remove(self, db: AsyncSession, *, id: int) -> ModelType:
        """删除对象

        Args:
            db: 数据库会话
            id: 对象ID

        Returns:
            删除的对象
        """
        result = await db.execute(select(self.model).where(self.model.id == id))
        obj = result.scalar_one()
        await db.delete(obj)
        await db.commit()
        return obj
