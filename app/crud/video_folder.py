from datetime import datetime

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.user import User
from app.models.video import Video
from app.models.video_folder import VideoFolder
from app.schemas.video_folder import VideoFolderCreate, VideoFolderUpdate


class CRUDVideoFolder(CRUDBase[VideoFolder, VideoFolderCreate, VideoFolderUpdate]):
    async def get_multi_by_ids(self, db: AsyncSession, *, ids: list[int]) -> list[VideoFolder]:
        """根据ID列表获取多个文件夹"""
        if not ids:
            return []
        result = await db.execute(select(self.model).where(self.model.id.in_(ids)))
        return result.scalars().all()

    async def get_by_path(self, db: AsyncSession, *, user_id: int, path: str) -> VideoFolder | None:
        """根据路径获取文件夹"""
        result = await db.execute(
            select(self.model).where(self.model.user_id == user_id, self.model.path == path)
        )
        return result.scalar_one_or_none()

    async def get_by_user(self, db: AsyncSession, *, user_id: int) -> list[VideoFolder]:
        """获取用户的所有文件夹"""
        result = await db.execute(select(self.model).where(self.model.user_id == user_id))
        return result.scalars().all()

    async def get_children(
        self, db: AsyncSession, *, user_id: int, parent_path: str
    ) -> list[VideoFolder]:
        """获取指定路径下的所有子文件夹"""
        if parent_path == "/":
            # 根目录特殊处理
            result = await db.execute(
                select(self.model)
                .where(
                    self.model.user_id == user_id,
                    self.model.path.like("/%"),
                    ~self.model.path.like("/%/%"),  # 排除子文件夹
                )
                .where(self.model.is_deleted.is_(False))
            )
            return result.scalars().all()
        else:
            # 确保parent_path以/结尾
            if not parent_path.endswith("/"):
                parent_path += "/"
            result = await db.execute(
                select(self.model)
                .where(
                    self.model.user_id == user_id,
                    self.model.path.like(f"{parent_path}%"),
                    ~self.model.path.like(f"{parent_path}%/%"),  # 排除子文件夹
                )
                .where(self.model.is_deleted.is_(False))
            )
            return result.scalars().all()

    async def get_default_folder(self, db: AsyncSession, *, user_id: int) -> VideoFolder | None:
        """获取用户的默认文件夹，按创建时间降序取最新一个。"""
        result = await db.execute(
            select(self.model)
            .where(
                self.model.user_id == user_id,
                self.model.is_default.is_(True),
                self.model.is_deleted.is_(False),
            )
            .order_by(self.model.created_at.desc())
        )
        return result.scalars().first()

    async def get_all_user_ids(self, db: AsyncSession) -> list[int]:
        """获取所有用户的ID列表。"""
        result = await db.execute(select(User.id))
        return result.scalars().all()

    async def get_videos_in_folder(self, db: AsyncSession, *, folder_id: int) -> list[Video]:
        """获取文件夹下的所有未删除视频。"""
        result = await db.execute(
            select(Video).where(Video.folder_id == folder_id, Video.is_deleted.is_(False))
        )
        return result.scalars().all()

    async def soft_delete(self, db: AsyncSession, *, folder: VideoFolder) -> VideoFolder:
        """软删除文件夹。"""
        folder.is_deleted = True
        folder.deleted_at = datetime.utcnow()
        db.add(folder)
        await db.commit()
        await db.refresh(folder)
        return folder


video_folder = CRUDVideoFolder(VideoFolder)
