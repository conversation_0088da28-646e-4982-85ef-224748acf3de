"""用户设备CRUD操作"""

from datetime import datetime, timedelta

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.user_device import UserDevice
from app.schemas.user_device import UserDeviceCreate, UserDeviceUpdate


class CRUDUserDevice(CRUDBase[UserDevice, UserDeviceCreate, UserDeviceUpdate]):
    """用户设备CRUD操作类"""

    async def get_by_fingerprint(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        fingerprint: str
    ) -> UserDevice | None:
        """根据用户ID和设备指纹获取设备记录
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            fingerprint: 设备指纹
            
        Returns:
            设备记录，如果不存在则返回None
        """
        result = await db.execute(
            select(self.model).filter(
                self.model.user_id == user_id,
                self.model.device_fingerprint == fingerprint
            )
        )
        return result.scalar_one_or_none()

    async def get_user_devices(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        active_only: bool = True
    ) -> list[UserDevice]:
        """获取用户的所有设备记录
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            active_only: 是否只返回活跃设备
            
        Returns:
            设备记录列表
        """
        query = select(self.model).filter(self.model.user_id == user_id)
        
        if active_only:
            query = query.filter(
                self.model.is_active,
                not self.model.is_blocked
            )
        
        query = query.order_by(self.model.last_login_at.desc())
        result = await db.execute(query)
        return result.scalars().all()

    async def get_trusted_devices(
        self,
        db: AsyncSession,
        *,
        user_id: int
    ) -> list[UserDevice]:
        """获取用户的所有信任设备
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            
        Returns:
            信任设备列表
        """
        result = await db.execute(
            select(self.model).filter(
                self.model.user_id == user_id,
                self.model.is_trusted,
                self.model.is_active,
                not self.model.is_blocked
            ).order_by(self.model.last_login_at.desc())
        )
        return result.scalars().all()

    async def update_login_info(
        self,
        db: AsyncSession,
        *,
        device_id: int,
        ip_address: str | None = None,
        location: str | None = None
    ) -> UserDevice:
        """更新设备登录信息
        
        Args:
            db: 数据库会话
            device_id: 设备ID
            ip_address: IP地址
            location: 地理位置
            
        Returns:
            更新后的设备记录
        """
        device = await self.get(db, device_id)
        if not device:
            raise ValueError(f"Device with id {device_id} not found")
        
        # 更新登录信息
        device.login_count += 1
        device.last_login_at = datetime.utcnow()
        
        if ip_address:
            device.ip_address = ip_address
        if location:
            device.location = location
            
        # 重新计算信任分数
        device.calculate_trust_score()
        
        db.add(device)
        await db.commit()
        await db.refresh(device)
        return device

    async def trust_device(
        self,
        db: AsyncSession,
        *,
        device_id: int
    ) -> UserDevice:
        """信任设备
        
        Args:
            db: 数据库会话
            device_id: 设备ID
            
        Returns:
            更新后的设备记录
        """
        device = await self.get(db, device_id)
        if not device:
            raise ValueError(f"Device with id {device_id} not found")
        
        device.is_trusted = True
        device.calculate_trust_score()
        
        db.add(device)
        await db.commit()
        await db.refresh(device)
        return device

    async def block_device(
        self,
        db: AsyncSession,
        *,
        device_id: int,
        reason: str = "管理员手动阻止"
    ) -> UserDevice:
        """阻止设备
        
        Args:
            db: 数据库会话
            device_id: 设备ID
            reason: 阻止原因
            
        Returns:
            更新后的设备记录
        """
        device = await self.get(db, device_id)
        if not device:
            raise ValueError(f"Device with id {device_id} not found")
        
        device.is_blocked = True
        device.is_trusted = False
        device.blocked_reason = reason
        device.is_active = False
        
        db.add(device)
        await db.commit()
        await db.refresh(device)
        return device

    async def unblock_device(
        self,
        db: AsyncSession,
        *,
        device_id: int
    ) -> UserDevice:
        """解除设备阻止
        
        Args:
            db: 数据库会话
            device_id: 设备ID
            
        Returns:
            更新后的设备记录
        """
        device = await self.get(db, device_id)
        if not device:
            raise ValueError(f"Device with id {device_id} not found")
        
        device.is_blocked = False
        device.blocked_reason = None
        device.is_active = True
        device.calculate_trust_score()
        
        db.add(device)
        await db.commit()
        await db.refresh(device)
        return device

    async def get_devices_by_trust_score(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        min_score: int = 0,
        max_score: int = 100
    ) -> list[UserDevice]:
        """根据信任分数范围获取设备
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            min_score: 最小信任分数
            max_score: 最大信任分数
            
        Returns:
            设备记录列表
        """
        result = await db.execute(
            select(self.model).filter(
                self.model.user_id == user_id,
                self.model.trust_score >= min_score,
                self.model.trust_score <= max_score,
                self.model.is_active
            ).order_by(self.model.trust_score.desc())
        )
        return result.scalars().all()

    async def cleanup_old_devices(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        days_inactive: int = 90
    ) -> int:
        """清理长期未使用的设备记录
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            days_inactive: 非活跃天数阈值
            
        Returns:
            清理的设备数量
        """
        cutoff_date = datetime.utcnow() - timedelta(days=days_inactive)
        
        result = await db.execute(
            select(self.model).filter(
                self.model.user_id == user_id,
                self.model.last_login_at < cutoff_date,
                not self.model.is_trusted
            )
        )
        old_devices = result.scalars().all()
        
        count = 0
        for device in old_devices:
            device.is_active = False
            db.add(device)
            count += 1
        
        if count > 0:
            await db.commit()
        
        return count


# 创建全局实例
user_device = CRUDUserDevice(UserDevice)