from datetime import datetime

from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.history import History
from app.schemas.history import HistoryCreate, HistoryUpdate


class CRUDHistory(CRUDBase[History, HistoryCreate, HistoryUpdate]):
    async def get_by_user_and_content(
        self, db: AsyncSession, *, user_id: int, content_type: str, content_id: int
    ) -> History | None:
        result = await db.execute(
            select(self.model).where(
                and_(
                    self.model.user_id == user_id,
                    self.model.content_type == content_type,
                    self.model.content_id == content_id,
                )
            )
        )
        return result.scalar_one_or_none()

    async def update_visit(self, db: AsyncSession, *, db_obj: History) -> History:
        """更新访问信息"""
        db_obj.last_visited_at = datetime.utcnow()
        # 如果访问次数为空，则初始化为0
        if db_obj.visit_count is None:
            db_obj.visit_count = 0
        db_obj.visit_count += 1
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_multi_by_ids(self, db: AsyncSession, *, ids: list[int]) -> list[History]:
        """根据ID列表获取多个历史记录"""
        result = await db.execute(select(self.model).where(self.model.id.in_(ids)))
        return result.scalars().all()

    async def get_multi_by_user(
        self, db: AsyncSession, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> list[History]:
        """获取用户的所有历史记录"""
        result = await db.execute(
            select(self.model)
            .where(self.model.user_id == user_id)
            .order_by(self.model.last_visited_at.desc())
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_multi_by_user_and_content_type(
        self, db: AsyncSession, *, user_id: int, content_type: str, skip: int = 0, limit: int = 100
    ) -> list[History]:
        """获取用户特定内容类型的历史记录"""
        result = await db.execute(
            select(self.model)
            .where(and_(self.model.user_id == user_id, self.model.content_type == content_type))
            .order_by(self.model.last_visited_at.desc())
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def count_by_user(
        self, db: AsyncSession, *, user_id: int, content_type: str = None
    ) -> int:
        """计算用户历史记录总数"""
        query = select(self.model).where(self.model.user_id == user_id)
        if content_type:
            query = query.where(self.model.content_type == content_type)
        result = await db.execute(query)
        return len(result.scalars().all())

    async def remove_by_user(
        self, db: AsyncSession, *, user_id: int, content_type: str = None
    ) -> None:
        """删除用户的历史记录"""
        query = select(self.model).where(self.model.user_id == user_id)
        if content_type:
            query = query.where(self.model.content_type == content_type)
        result = await db.execute(query)
        for item in result.scalars().all():
            await db.delete(item)
        await db.commit()


history = CRUDHistory(History)
