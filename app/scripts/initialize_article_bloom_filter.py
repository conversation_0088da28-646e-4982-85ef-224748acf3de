"""
一次性脚本：初始化文章ID布隆过滤器
从数据库中读取所有文章ID，并将其添加到RedisBloom过滤器中。
"""

import asyncio
import logging

from sqlalchemy import select

from app.db.session import SessionLocal
from app.models.article import Article
from app.utils.bloom_filters import article_bloom_filter

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def initialize_article_bloom_filter():
    """
    从数据库获取所有文章ID并填充到布隆过滤器
    """
    logger.info("开始初始化文章ID布隆过滤器...")

    # 首先，确保过滤器在Redis中已创建
    await article_bloom_filter._reserve_filter_if_not_exists()

    db = SessionLocal()
    try:
        page = 1
        page_size = 1000
        total_added = 0

        while True:
            offset = (page - 1) * page_size
            logger.info(f"正在处理第 {page} 页数据 (offset: {offset})...")

            result = await db.execute(select(Article.id).offset(offset).limit(page_size))
            article_ids = result.scalars().all()

            if not article_ids:
                logger.info("没有更多文章ID需要处理。")
                break

            # 批量添加到布隆过滤器
            str_article_ids = [str(aid) for aid in article_ids]
            await article_bloom_filter.add_multi(str_article_ids)

            total_added += len(article_ids)
            logger.info(
                f"已将 {len(article_ids)} 个文章ID添加到布隆过滤器。累计总数: {total_added}"
            )

            page += 1

        logger.info(f"文章ID布隆过滤器初始化完成！总共添加了 {total_added} 个ID。")

    except Exception as e:
        logger.error(f"初始化文章布隆过滤器时发生错误: {e}")
    finally:
        await db.close()


async def main():
    await initialize_article_bloom_filter()


if __name__ == "__main__":
    asyncio.run(main())
