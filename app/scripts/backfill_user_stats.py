import asyncio
import logging

from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.db.session import SessionLocal

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def backfill_user_stats(db: AsyncSession):
    """
    回填所有用户的统计数据到 user_stats 表
    """
    logger.info("开始回填用户统计数据...")

    # 1. 获取所有用户ID
    user_ids_result = await db.execute(
        select(models.User.id).where(models.User.is_deleted.is_(False))
    )
    user_ids = user_ids_result.scalars().all()
    logger.info(f"共找到 {len(user_ids)} 个有效用户。")

    # 2. 分批处理以避免内存问题
    batch_size = 100
    for i in range(0, len(user_ids), batch_size):
        batch_user_ids = user_ids[i : i + batch_size]
        logger.info(
            f"正在处理批次 {i // batch_size + 1}，用户ID从 {batch_user_ids[0]} 到 {batch_user_ids[-1]}..."
        )

        # 3. 批量计算各项统计数据
        # 文章数
        article_counts_result = await db.execute(
            select(models.Article.author_id, func.count(models.Article.id))
            .where(models.Article.author_id.in_(batch_user_ids))
            .group_by(models.Article.author_id)
        )
        article_counts = dict(article_counts_result.all())

        # 视频数
        video_counts_result = await db.execute(
            select(models.Video.author_id, func.count(models.Video.id))
            .where(models.Video.author_id.in_(batch_user_ids))
            .group_by(models.Video.author_id)
        )
        video_counts = dict(video_counts_result.all())

        # 粉丝数
        follower_counts_result = await db.execute(
            select(models.user_follow.c.followed_id, func.count())
            .where(models.user_follow.c.followed_id.in_(batch_user_ids))
            .group_by(models.user_follow.c.followed_id)
        )
        follower_counts = dict(follower_counts_result.all())

        # 关注数
        following_counts_result = await db.execute(
            select(models.user_follow.c.follower_id, func.count())
            .where(models.user_follow.c.follower_id.in_(batch_user_ids))
            .group_by(models.user_follow.c.follower_id)
        )
        following_counts = dict(following_counts_result.all())

        # 获赞数 (文章 + 视频)
        likes_result = await db.execute(
            select(
                models.Like.content_type,
                models.Article.author_id.label("article_author"),
                models.Video.author_id.label("video_author"),
            )
            .outerjoin(
                models.Article,
                (models.Like.content_id == models.Article.id)
                & (models.Like.content_type == "article"),
            )
            .outerjoin(
                models.Video,
                (models.Like.content_id == models.Video.id) & (models.Like.content_type == "video"),
            )
            .where(
                (models.Article.author_id.in_(batch_user_ids))
                | (models.Video.author_id.in_(batch_user_ids))
            )
        )

        total_likes_counts = {}
        for row in likes_result.all():
            author_id = row.article_author or row.video_author
            if author_id:
                total_likes_counts[author_id] = total_likes_counts.get(author_id, 0) + 1

        # 收藏数 (文章 + 视频)
        favorites_result = await db.execute(
            select(
                models.Favorite.content_type,
                models.Article.author_id.label("article_author"),
                models.Video.author_id.label("video_author"),
            )
            .outerjoin(
                models.Article,
                (models.Favorite.content_id == models.Article.id)
                & (models.Favorite.content_type == "article"),
            )
            .outerjoin(
                models.Video,
                (models.Favorite.content_id == models.Video.id)
                & (models.Favorite.content_type == "video"),
            )
            .where(
                (models.Article.author_id.in_(batch_user_ids))
                | (models.Video.author_id.in_(batch_user_ids))
            )
        )

        total_favorites_counts = {}
        for row in favorites_result.all():
            author_id = row.article_author or row.video_author
            if author_id:
                total_favorites_counts[author_id] = total_favorites_counts.get(author_id, 0) + 1

        # 4. 逐一更新或创建统计记录
        for user_id in batch_user_ids:
            stats_data = {
                "article_count": article_counts.get(user_id, 0),
                "video_count": video_counts.get(user_id, 0),
                "follower_count": follower_counts.get(user_id, 0),
                "following_count": following_counts.get(user_id, 0),
                "total_likes_count": total_likes_counts.get(user_id, 0),
                "total_favorites_count": total_favorites_counts.get(user_id, 0),
            }
            stats_obj = await crud.user_stats.get(db, user_id=user_id)
            if stats_obj:
                await crud.user_stats.update(db, db_obj=stats_obj, obj_in=stats_data)
            else:
                create_schema = schemas.UserStatsCreate(user_id=user_id, **stats_data)
                await crud.user_stats.create(db, obj_in=create_schema)

        await db.commit()
        logger.info(f"批次 {i // batch_size + 1} 处理完成。")

    logger.info("所有用户统计数据回填完成！")


async def main():
    async with SessionLocal() as db:
        await backfill_user_stats(db)


if __name__ == "__main__":
    asyncio.run(main())
