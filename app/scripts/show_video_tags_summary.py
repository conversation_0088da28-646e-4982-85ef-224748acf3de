"""
显示视频标签分配摘要的脚本
"""

import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text

from app.db.session import SessionLocal


async def show_video_tags_summary():
    """显示视频标签分配摘要"""

    async with SessionLocal() as session:
        print("📊 视频标签分配摘要报告")
        print("=" * 60)

        # 1. 基本统计
        print("\n📈 基本统计:")

        # 总视频数
        result = await session.execute(
            text("SELECT COUNT(*) as total FROM videos WHERE is_deleted = false")
        )
        total_videos = result.scalar()

        # 总标签数
        result = await session.execute(text("SELECT COUNT(*) as total FROM tags"))
        total_tags = result.scalar()

        # 总关联数
        result = await session.execute(text("SELECT COUNT(*) as total FROM video_tags"))
        total_relations = result.scalar()

        print(f"  • 活跃视频总数: {total_videos}")
        print(f"  • 标签总数: {total_tags}")
        print(f"  • 视频-标签关联总数: {total_relations}")
        print(f"  • 平均每个视频的标签数: {total_relations / total_videos:.1f}")

        # 2. 标签使用排行
        print("\n🏷️ 标签使用排行 (Top 10):")
        query = text("""
            SELECT t.name, COUNT(vt.video_id) as video_count 
            FROM tags t 
            LEFT JOIN video_tags vt ON t.id = vt.tag_id 
            GROUP BY t.id, t.name 
            HAVING video_count > 0
            ORDER BY video_count DESC, t.name 
            LIMIT 10
        """)
        result = await session.execute(query)
        tag_stats = result.fetchall()

        for i, (tag_name, count) in enumerate(tag_stats, 1):
            print(f"  {i:2d}. {tag_name:<15} ({count} 个视频)")

        # 3. 最新添加的视频标签详情
        print("\n🆕 最新添加的视频标签详情 (ID > 22):")
        query = text("""
            SELECT 
                v.id,
                v.title,
                c.name as category_name,
                GROUP_CONCAT(t.name) as tags
            FROM videos v
            LEFT JOIN categories c ON v.category_id = c.id
            LEFT JOIN video_tags vt ON v.id = vt.video_id
            LEFT JOIN tags t ON vt.tag_id = t.id
            WHERE v.id > 22 AND v.is_deleted = false
            GROUP BY v.id, v.title, c.name
            ORDER BY v.id
        """)
        result = await session.execute(query)
        new_videos = result.fetchall()

        for video_id, title, category_name, tags in new_videos:
            tags_list = tags.split(",") if tags else ["无标签"]
            print(f"  • ID {video_id:2d}: {title[:35]:<35} [{category_name}]")
            print(f"    标签: {', '.join(tags_list)}")

        print("\n" + "=" * 60)
        print("📋 报告生成完成！")


async def main():
    """主函数"""
    await show_video_tags_summary()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  报告生成被用户中断")
    except Exception as e:
        print(f"❌ 报告生成失败: {e}")
        sys.exit(1)
