#!/usr/bin/env python3
"""
验证测试数据是否正确创建
"""

import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text

from app.db.session import SessionLocal


async def verify_data():
    """验证数据库中的测试数据"""
    async with SessionLocal() as db:
        print("=== 数据验证报告 ===")

        # 验证分类数据
        result = await db.execute(text("SELECT name, category_type FROM categories LIMIT 5"))
        print("\n分类样例:")
        for row in result.fetchall():
            print(f"  {row[0]} ({row[1]})")

        # 验证文章数据
        result = await db.execute(
            text("SELECT title, is_published, is_approved FROM articles LIMIT 5")
        )
        print("\n文章样例:")
        for row in result.fetchall():
            print(f"  {row[0]} - 发布:{row[1]} 审核:{row[2]}")

        # 验证审核数据
        result = await db.execute(text("SELECT content_type, status, comment FROM reviews LIMIT 5"))
        print("\n审核样例:")
        for row in result.fetchall():
            print(f"  {row[0]} - {row[1]}: {row[2]}")

        # 统计总数
        result = await db.execute(text("SELECT COUNT(*) FROM categories"))
        categories_count = result.scalar()

        result = await db.execute(text("SELECT COUNT(*) FROM articles"))
        articles_count = result.scalar()

        result = await db.execute(text("SELECT COUNT(*) FROM reviews"))
        reviews_count = result.scalar()

        print("\n=== 数据统计 ===")
        print(f"分类总数: {categories_count}")
        print(f"文章总数: {articles_count}")
        print(f"审核记录总数: {reviews_count}")

        print("\n✅ 测试数据验证完成！")


if __name__ == "__main__":
    asyncio.run(verify_data())
