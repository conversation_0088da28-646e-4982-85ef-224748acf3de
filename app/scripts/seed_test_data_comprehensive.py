#!/usr/bin/env python3
"""
为articles、reviews和category表添加测试数据
包含各种不同的状态
"""

import asyncio
import os
import random
import sys
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import SessionLocal
from app.models.article import Article
from app.models.category import Category
from app.models.review import ContentType, Review, ReviewStatus
from app.models.user import User, UserRole


async def create_test_categories(db: AsyncSession):
    """创建测试分类数据"""
    # 检查是否已存在分类
    result = await db.execute(select(Category))
    existing_categories = result.scalars().all()
    
    if len(existing_categories) >= 10:
        print(f"已存在 {len(existing_categories)} 个分类，跳过创建")
        return existing_categories
    
    # 简化的分类数据，避免MPTT复杂性
    categories_data = [
        {"name": "技术教程", "description": "编程技术相关教程", "category_type": "article"},
        {"name": "产品设计", "description": "产品设计与用户体验", "category_type": "article"},
        {"name": "行业资讯", "description": "最新行业动态和资讯", "category_type": "article"},
        {"name": "创业故事", "description": "创业经验分享", "category_type": "article"},
        {"name": "职场发展", "description": "职业规划与发展", "category_type": "article"},
        {"name": "编程教学", "description": "编程技术视频教程", "category_type": "video"},
        {"name": "设计分享", "description": "设计作品展示", "category_type": "video"},
        {"name": "生活记录", "description": "日常生活视频", "category_type": "video"},
        {"name": "科技评测", "description": "数码产品评测", "category_type": "video"},
        {"name": "知识科普", "description": "科普知识分享", "category_type": "video"},
    ]
    
    created_categories = []
    for cat_data in categories_data:
        # 检查是否已存在
        result = await db.execute(select(Category).filter(Category.name == cat_data["name"]))
        existing = result.scalar_one_or_none()
        if not existing:
            try:
                # 简单创建分类，让MPTT自动处理树结构
                category = Category(**cat_data)
                db.add(category)
                await db.flush()  # 立即刷新以检查错误
                created_categories.append(category)
            except Exception as e:
                print(f"创建分类 {cat_data['name']} 时出错: {e}")
                await db.rollback()
                continue
    
    try:
        await db.commit()
        
        # 刷新以获取ID
        for cat in created_categories:
            await db.refresh(cat)
        
        print(f"创建了 {len(created_categories)} 个分类")
    except Exception as e:
        print(f"提交分类数据时出错: {e}")
        await db.rollback()
        created_categories = []
    
    # 返回所有分类（包括已存在的）
    result = await db.execute(select(Category))
    all_categories = result.scalars().all()
    return all_categories


async def create_test_articles(db: AsyncSession):
    """创建测试文章数据"""
    # 获取用户和分类
    result = await db.execute(select(User).limit(10))
    users = result.scalars().all()
    if not users:
        print("警告: 没有找到用户，请先创建用户数据")
        return []
    
    result = await db.execute(select(Category).filter(Category.category_type == "article"))
    article_categories = result.scalars().all()
    if not article_categories:
        print("警告: 没有找到文章分类")
        return []
    
    articles_data = [
        {
            "title": "Python Web开发完整指南",
            "content": "本文将详细介绍如何使用Python进行Web开发，包括Flask和Django框架的使用...",
            "description": "从零开始学习Python Web开发",
            "cover_url": "https://example.com/covers/python-web.jpg",
            "is_published": True,
            "is_approved": True,
            "visit_count": random.randint(100, 1000)
        },
        {
            "title": "React Hooks深度解析",
            "content": "React Hooks是React 16.8引入的新特性，让我们可以在函数组件中使用状态...",
            "description": "深入理解React Hooks的原理和使用",
            "cover_url": "https://example.com/covers/react-hooks.jpg",
            "is_published": True,
            "is_approved": False,
            "visit_count": random.randint(50, 500)
        },
        {
            "title": "数据库设计最佳实践",
            "content": "良好的数据库设计是应用程序成功的关键，本文将分享一些设计原则...",
            "description": "数据库设计的核心原则和技巧",
            "cover_url": "https://example.com/covers/database-design.jpg",
            "is_published": False,
            "is_approved": False,
            "visit_count": 0
        },
        {
            "title": "微服务架构实战",
            "content": "微服务架构已成为现代应用开发的主流模式，本文将介绍实际项目中的应用...",
            "description": "微服务架构的实际应用案例",
            "cover_url": "https://example.com/covers/microservices.jpg",
            "is_published": True,
            "is_approved": True,
            "visit_count": random.randint(200, 800)
        },
        {
            "title": "前端性能优化技巧",
            "content": "前端性能直接影响用户体验，本文总结了常用的优化方法...",
            "description": "提升前端应用性能的实用技巧",
            "cover_url": "https://example.com/covers/frontend-optimization.jpg",
            "is_published": True,
            "is_approved": False,
            "visit_count": random.randint(80, 400)
        },
        {
            "title": "Docker容器化部署指南",
            "content": "Docker已成为现代应用部署的标准工具，本文将介绍如何使用Docker...",
            "description": "Docker容器化技术详解",
            "cover_url": "https://example.com/covers/docker.jpg",
            "is_published": False,
            "is_approved": False,
            "visit_count": 0
        },
        {
            "title": "AI人工智能入门",
            "content": "人工智能正在改变世界，本文将为初学者介绍AI的基本概念...",
            "description": "AI技术的基础知识介绍",
            "cover_url": "https://example.com/covers/ai-intro.jpg",
            "is_published": True,
            "is_approved": True,
            "visit_count": random.randint(300, 1200)
        },
        {
            "title": "区块链技术解析",
            "content": "区块链作为新兴技术，具有去中心化、不可篡改等特点...",
            "description": "深入了解区块链技术原理",
            "cover_url": "https://example.com/covers/blockchain.jpg",
            "is_published": True,
            "is_approved": False,
            "visit_count": random.randint(60, 300)
        },
        {
            "title": "云计算服务对比",
            "content": "AWS、Azure、阿里云等主流云服务提供商的功能对比...",
            "description": "主流云计算平台功能比较",
            "cover_url": "https://example.com/covers/cloud-computing.jpg",
            "is_published": False,
            "is_approved": False,
            "visit_count": 0
        },
        {
            "title": "移动应用开发趋势",
            "content": "移动应用开发技术不断演进，本文分析当前的发展趋势...",
            "description": "移动开发技术的未来方向",
            "cover_url": "https://example.com/covers/mobile-dev.jpg",
            "is_published": True,
            "is_approved": True,
            "visit_count": random.randint(150, 600)
        },
        {
            "title": "网络安全防护策略",
            "content": "网络安全威胁日益严重，企业需要建立完善的防护体系...",
            "description": "企业网络安全防护方案",
            "cover_url": "https://example.com/covers/cybersecurity.jpg",
            "is_published": True,
            "is_approved": False,
            "visit_count": random.randint(90, 450)
        },
        {
            "title": "大数据分析实践",
            "content": "大数据技术在各行业的应用越来越广泛，本文介绍实际案例...",
            "description": "大数据技术的实际应用",
            "cover_url": "https://example.com/covers/big-data.jpg",
            "is_published": False,
            "is_approved": False,
            "visit_count": 0
        },
        {
            "title": "DevOps实施指南",
            "content": "DevOps文化和实践可以显著提升开发效率，本文分享实施经验...",
            "description": "DevOps文化的实践方法",
            "cover_url": "https://example.com/covers/devops.jpg",
            "is_published": True,
            "is_approved": True,
            "visit_count": random.randint(120, 550)
        },
        {
            "title": "敏捷开发方法论",
            "content": "敏捷开发已成为软件开发的主流方法，本文介绍核心原则...",
            "description": "敏捷开发的理论与实践",
            "cover_url": "https://example.com/covers/agile.jpg",
            "is_published": True,
            "is_approved": False,
            "visit_count": random.randint(70, 350)
        },
        {
            "title": "开源软件贡献指南",
            "content": "参与开源项目是提升技能的好方法，本文介绍如何开始贡献...",
            "description": "如何参与开源项目开发",
            "cover_url": "https://example.com/covers/opensource.jpg",
            "is_published": False,
            "is_approved": False,
            "visit_count": 0
        },
        {
            "title": "API设计最佳实践",
            "content": "良好的API设计对系统集成至关重要，本文总结设计原则...",
            "description": "RESTful API设计规范",
            "cover_url": "https://example.com/covers/api-design.jpg",
            "is_published": True,
            "is_approved": True,
            "visit_count": random.randint(180, 700)
        },
        {
            "title": "代码重构技巧",
            "content": "代码重构是保持代码质量的重要手段，本文分享实用技巧...",
            "description": "提升代码质量的重构方法",
            "cover_url": "https://example.com/covers/refactoring.jpg",
            "is_published": True,
            "is_approved": False,
            "visit_count": random.randint(100, 480)
        },
        {
            "title": "测试驱动开发",
            "content": "TDD是一种有效的开发方法，可以提高代码质量和开发效率...",
            "description": "测试驱动开发的实践方法",
            "cover_url": "https://example.com/covers/tdd.jpg",
            "is_published": False,
            "is_approved": False,
            "visit_count": 0
        },
        {
            "title": "技术团队管理",
            "content": "技术团队的管理需要平衡技术和人员因素，本文分享管理经验...",
            "description": "技术团队的有效管理方法",
            "cover_url": "https://example.com/covers/team-management.jpg",
            "is_published": True,
            "is_approved": True,
            "visit_count": random.randint(200, 800)
        },
        {
            "title": "职业发展规划",
            "content": "技术人员的职业发展需要明确的规划和持续的学习...",
            "description": "技术人员的职业发展路径",
            "cover_url": "https://example.com/covers/career-planning.jpg",
            "is_published": True,
            "is_approved": False,
            "visit_count": random.randint(150, 650)
        },
        {
            "title": "远程工作指南",
            "content": "远程工作已成为新常态，本文分享高效远程工作的方法...",
            "description": "远程工作的最佳实践",
            "cover_url": "https://example.com/covers/remote-work.jpg",
            "is_published": False,
            "is_approved": False,
            "visit_count": 0
        },
        {
            "title": "技术选型决策",
            "content": "技术选型对项目成功至关重要，本文介绍决策框架...",
            "description": "如何做出正确的技术选型",
            "cover_url": "https://example.com/covers/tech-selection.jpg",
            "is_published": True,
            "is_approved": True,
            "visit_count": random.randint(120, 520)
        }
    ]
    
    created_articles = []
    for _i, article_data in enumerate(articles_data):
        # 随机分配作者和分类
        article_data["author_id"] = random.choice(users).id
        article_data["category_id"] = random.choice(article_categories).id
        
        # 随机设置创建时间（过去30天内）
        days_ago = random.randint(0, 30)
        article_data["created_at"] = datetime.utcnow() - timedelta(days=days_ago)
        article_data["updated_at"] = article_data["created_at"] + timedelta(hours=random.randint(1, 24))
        
        article = Article(**article_data)
        db.add(article)
        created_articles.append(article)
    
    await db.commit()
    
    # 刷新以获取ID
    for article in created_articles:
        await db.refresh(article)
    
    print(f"创建了 {len(created_articles)} 篇文章")
    return created_articles


async def create_test_reviews(db: AsyncSession, articles):
    """创建测试审核数据"""
    # 获取审核员（假设有管理员角色的用户）
    result = await db.execute(select(User).join(UserRole).filter(UserRole.name.in_(["admin", "moderator"])))
    reviewers = result.scalars().all()
    if not reviewers:
        # 如果没有特定角色，就使用前几个用户作为审核员
        result = await db.execute(select(User).limit(5))
        reviewers = result.scalars().all()
    
    if not reviewers:
        print("警告: 没有找到审核员用户")
        return []
    
    created_reviews = []
    
    # 为每篇文章创建审核记录
    for article in articles:
        # 只为已发布的文章创建审核记录
        if article.is_published:
            review_data = {
                "content_type": ContentType.ARTICLE,
                "content_id": article.id,
                "reviewer_id": random.choice(reviewers).id,
                "created_at": article.created_at + timedelta(hours=random.randint(1, 48))
            }
            
            # 根据文章的审核状态设置审核记录
            if article.is_approved:
                review_data["status"] = ReviewStatus.APPROVED
                review_data["comment"] = random.choice([
                    "内容质量很好，符合发布标准",
                    "文章结构清晰，内容有价值",
                    "技术内容准确，可以发布",
                    "写作质量不错，通过审核",
                    "内容丰富，对读者有帮助"
                ])
                review_data["reviewed_at"] = review_data["created_at"] + timedelta(hours=random.randint(1, 24))
            else:
                # 随机决定是拒绝还是待审核
                if random.choice([True, False]):
                    review_data["status"] = ReviewStatus.REJECTED
                    review_data["comment"] = random.choice([
                        "内容质量不符合标准，需要改进",
                        "技术内容存在错误，请修正",
                        "文章结构混乱，需要重新整理",
                        "内容过于简单，缺乏深度",
                        "存在抄袭嫌疑，请提供原创证明",
                        "标题与内容不符，需要修改",
                        "格式不规范，请按照要求调整"
                    ])
                    review_data["reviewed_at"] = review_data["created_at"] + timedelta(hours=random.randint(1, 72))
                else:
                    review_data["status"] = ReviewStatus.PENDING
                    review_data["comment"] = None
                    review_data["reviewed_at"] = None
            
            review = Review(**review_data)
            db.add(review)
            created_reviews.append(review)
    
    # 创建一些额外的审核记录（模拟多次审核）
    additional_reviews_data = [
        {
            "content_type": ContentType.ARTICLE,
            "content_id": random.choice(articles).id,
            "status": ReviewStatus.PENDING,
            "reviewer_id": random.choice(reviewers).id,
            "comment": None,
            "reviewed_at": None
        },
        {
            "content_type": ContentType.VIDEO,
            "content_id": random.randint(1, 10),  # 假设的视频ID
            "status": ReviewStatus.APPROVED,
            "reviewer_id": random.choice(reviewers).id,
            "comment": "视频内容优质，通过审核",
            "reviewed_at": datetime.utcnow() - timedelta(days=random.randint(1, 10))
        },
        {
            "content_type": ContentType.VIDEO,
            "content_id": random.randint(1, 10),
            "status": ReviewStatus.REJECTED,
            "reviewer_id": random.choice(reviewers).id,
            "comment": "视频质量不符合要求",
            "reviewed_at": datetime.utcnow() - timedelta(days=random.randint(1, 5))
        },
        {
            "content_type": ContentType.VIDEO,
            "content_id": random.randint(1, 10),
            "status": ReviewStatus.PENDING,
            "reviewer_id": random.choice(reviewers).id,
            "comment": None,
            "reviewed_at": None
        }
    ]
    
    for review_data in additional_reviews_data:
        if "created_at" not in review_data:
            review_data["created_at"] = datetime.utcnow() - timedelta(days=random.randint(0, 15))
        
        review = Review(**review_data)
        db.add(review)
        created_reviews.append(review)
    
    await db.commit()
    
    # 刷新以获取ID
    for review in created_reviews:
        await db.refresh(review)
    
    print(f"创建了 {len(created_reviews)} 条审核记录")
    return created_reviews


async def main():
    """主函数"""
    print("开始创建测试数据...")
    
    db = SessionLocal()
    try:
        # 创建分类数据
        print("\n1. 创建分类数据...")
        categories = await create_test_categories(db)
        
        # 创建文章数据
        print("\n2. 创建文章数据...")
        articles = await create_test_articles(db)
        
        # 创建审核数据
        print("\n3. 创建审核数据...")
        reviews = await create_test_reviews(db, articles)
        
        print("\n测试数据创建完成！")
        print(f"总计创建: {len(categories)} 个分类, {len(articles)} 篇文章, {len(reviews)} 条审核记录")
        
        # 统计各种状态的数据
        print("\n数据统计:")
        print(f"已发布文章: {len([a for a in articles if a.is_published])}")
        print(f"草稿文章: {len([a for a in articles if not a.is_published])}")
        print(f"已审核通过文章: {len([a for a in articles if a.is_approved])}")
        print(f"待审核/被拒绝文章: {len([a for a in articles if not a.is_approved])}")
        
        pending_reviews = len([r for r in reviews if r.status == ReviewStatus.PENDING])
        approved_reviews = len([r for r in reviews if r.status == ReviewStatus.APPROVED])
        rejected_reviews = len([r for r in reviews if r.status == ReviewStatus.REJECTED])
        
        print(f"待审核记录: {pending_reviews}")
        print(f"通过审核记录: {approved_reviews}")
        print(f"拒绝审核记录: {rejected_reviews}")
        
    except Exception as e:
        print(f"创建测试数据时出错: {e}")
        await db.rollback()
        raise
    finally:
        await db.close()


if __name__ == "__main__":
    asyncio.run(main())