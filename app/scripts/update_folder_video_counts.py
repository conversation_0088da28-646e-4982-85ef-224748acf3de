"""更新所有文件夹的视频数量"""

import asyncio
import sys
from pathlib import Path

from app.core.logging import logger
from app.db.session import SessionLocal
from app.services.folder_stats_service import folder_stats_service

# 添加项目根目录到 Python 路径
root_dir = Path(__file__).parent.parent.parent
sys.path.append(str(root_dir))


async def update_all_folders_video_count():
    """更新所有文件夹的视频数量"""
    async with SessionLocal() as db:
        count = await folder_stats_service.update_all_folders_video_count(db)
        logger.info(f"已更新 {count} 个文件夹的视频数量")


if __name__ == "__main__":
    asyncio.run(update_all_folders_video_count())
