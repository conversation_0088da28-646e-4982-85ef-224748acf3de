import asyncio
import logging
import os
import sys

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

# 将项目根目录添加到Python路径中
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import crud, models
from app.db.session import SessionLocal
from app.schemas.user import UserCreate

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def seed_data(db: AsyncSession) -> None:
    """填充模拟数据"""
    logger.info("开始填充模拟数据...")

    # --- 1. 创建角色 ---
    logger.info("创建角色...")
    user_role = await db.execute(select(models.UserRole).filter_by(name="user"))
    role = user_role.scalar_one_or_none()
    if not role:
        role = models.UserRole(name="user", desc="普通用户")
        db.add(role)
        await db.commit()
        await db.refresh(role)

    admin_role = await db.execute(select(models.UserRole).filter_by(name="admin"))
    admin_role_obj = admin_role.scalar_one_or_none()
    if not admin_role_obj:
        admin_role_obj = models.UserRole(name="admin", desc="管理员")
        db.add(admin_role_obj)
        await db.commit()
        await db.refresh(admin_role_obj)

    # --- 2. 创建用户 ---
    logger.info("创建用户...")
    users_data = [
        {"username": "18901011001", "password": "password1", "nickname": "小明"},
        {"username": "18901011002", "password": "password2", "nickname": "小红"},
        {"username": "18901011003", "password": "password3", "nickname": "小刚"},
        {"username": "18901011004", "password": "password4", "nickname": "小华"},
    ]
    created_users = []
    for user_data in users_data:
        result = await db.execute(select(models.User).filter_by(username=user_data["username"]))
        user = result.scalar_one_or_none()
        if not user:
            user_in = UserCreate(
                username=user_data["username"],
                password=user_data["password"],
                nickname=user_data["nickname"],
                role_id=role.id,
            )
            user = await crud.user.create(db, obj_in=user_in)
        created_users.append(user)

    # --- 3. 创建分类和标签 ---
    logger.info("创建分类和标签...")
    categories_data = ["技术", "生活", "财经", "娱乐"]
    tags_data = ["Python", "FastAPI", "旅行", "美食", "股票", "电影", "音乐", "AI"]

    created_categories = {}
    for cat_name in categories_data:
        category = await db.execute(select(models.Category).filter_by(name=cat_name))
        category = category.scalar_one_or_none()
        if not category:
            category = models.Category(name=cat_name)
            db.add(category)
            await db.commit()
            await db.refresh(category)
        created_categories[cat_name] = category

    created_tags = {}
    for tag_name in tags_data:
        tag = await db.execute(select(models.Tag).filter_by(name=tag_name))
        tag = tag.scalar_one_or_none()
        if not tag:
            tag = models.Tag(name=tag_name)
            db.add(tag)
            await db.commit()
            await db.refresh(tag)
        created_tags[tag_name] = tag

    # --- 4. 创建文章 ---
    logger.info("创建文章...")
    articles_data = [
        {
            "title": "FastAPI入门指南",
            "content": "这是一篇关于FastAPI的详细入门文章...",
            "author": created_users[0],
            "category": created_categories["技术"],
            "tags": [created_tags["FastAPI"], created_tags["Python"]],
        },
        {
            "title": "我的云南之旅",
            "content": "记录我在云南的美好时光...",
            "author": created_users[1],
            "category": created_categories["生活"],
            "tags": [created_tags["旅行"]],
        },
        {
            "title": "AI如何改变世界",
            "content": "探讨人工智能的未来发展...",
            "author": created_users[2],
            "category": created_categories["技术"],
            "tags": [created_tags["AI"], created_tags["Python"]],
        },
        {
            "title": "本周电影推荐",
            "content": "几部不容错过的好电影...",
            "author": created_users[3],
            "category": created_categories["娱乐"],
            "tags": [created_tags["电影"]],
        },
        {
            "title": "Python异步编程实践",
            "content": "深入理解asyncio...",
            "author": created_users[0],
            "category": created_categories["技术"],
            "tags": [created_tags["Python"], created_tags["FastAPI"]],
        },
        {
            "title": "美食探店：城市角落的美味",
            "content": "发现那些隐藏在城市深处的美食...",
            "author": created_users[1],
            "category": created_categories["生活"],
            "tags": [created_tags["美食"]],
        },
    ]

    created_articles = []
    for article_data in articles_data:
        article = await db.execute(select(models.Article).filter_by(title=article_data["title"]))
        article = article.scalar_one_or_none()
        if not article:
            article = models.Article(
                title=article_data["title"],
                content=article_data["content"],
                author_id=article_data["author"].id,
                category_id=article_data["category"].id,
                tags=article_data["tags"],
                is_published=True,
                is_approved=True,
            )
            db.add(article)
            await db.commit()
            await db.refresh(article)
        created_articles.append(article)

    # --- 5. 创建交互 (点赞) ---
    logger.info("创建交互数据...")
    likes_data = [
        # 小明喜欢技术文章
        {"user": created_users[0], "article": created_articles[0]},
        {"user": created_users[0], "article": created_articles[2]},
        {"user": created_users[0], "article": created_articles[4]},
        # 小红喜欢生活和娱乐
        {"user": created_users[1], "article": created_articles[1]},
        {"user": created_users[1], "article": created_articles[3]},
        {"user": created_users[1], "article": created_articles[5]},
        # 小刚也喜欢技术
        {"user": created_users[2], "article": created_articles[0]},
        {"user": created_users[2], "article": created_articles[4]},
        # 小华喜欢电影和AI
        {"user": created_users[3], "article": created_articles[3]},
        {"user": created_users[3], "article": created_articles[2]},
    ]

    for like_data in likes_data:
        existing_like = await db.execute(
            select(models.Like).filter_by(
                user_id=like_data["user"].id,
                content_type="article",
                content_id=like_data["article"].id,
            )
        )
        if not existing_like.scalar_one_or_none():
            like = models.Like(
                user_id=like_data["user"].id,
                content_type="article",
                content_id=like_data["article"].id,
            )
            db.add(like)

    await db.commit()
    logger.info("模拟数据填充完成。")


async def main():
    """主函数，用于连接数据库并填充数据"""
    logger.info("正在连接到数据库...")
    db = SessionLocal()
    try:
        await seed_data(db)
    finally:
        await db.close()
        logger.info("数据库连接已关闭。")


if __name__ == "__main__":
    asyncio.run(main())
