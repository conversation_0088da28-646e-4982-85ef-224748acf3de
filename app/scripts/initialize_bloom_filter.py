"""
一次性脚本：初始化视频ID布隆过滤器
从数据库中读取所有视频ID，并将其添加到RedisBloom过滤器中。
"""

import asyncio
import logging

from sqlalchemy import select

from app.db.session import SessionLocal
from app.models.video import Video
from app.utils.bloom_filters import video_bloom_filter

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def initialize_video_bloom_filter():
    """
    从数据库获取所有视频ID并填充到布隆过滤器
    """
    logger.info("开始初始化视频ID布隆过滤器...")

    # 首先，确保过滤器在Redis中已创建
    await video_bloom_filter._reserve_filter_if_not_exists()

    db = SessionLocal()
    try:
        page = 1
        page_size = 1000
        total_added = 0

        while True:
            offset = (page - 1) * page_size
            logger.info(f"正在处理第 {page} 页数据 (offset: {offset})...")

            result = await db.execute(select(Video.id).offset(offset).limit(page_size))
            video_ids = result.scalars().all()

            if not video_ids:
                logger.info("没有更多视频ID需要处理。")
                break

            # 批量添加到布隆过滤器
            str_video_ids = [str(vid) for vid in video_ids]
            await video_bloom_filter.add_multi(str_video_ids)

            total_added += len(video_ids)
            logger.info(f"已将 {len(video_ids)} 个视频ID添加到布隆过滤器。累计总数: {total_added}")

            page += 1

        logger.info(f"视频ID布隆过滤器初始化完成！总共添加了 {total_added} 个ID。")

    except Exception as e:
        logger.error(f"初始化布隆过滤器时发生错误: {e}")
    finally:
        await db.close()


async def main():
    await initialize_video_bloom_filter()


if __name__ == "__main__":
    asyncio.run(main())
