import asyncio
import logging

from app.db.init_permissions import init_permissions
from app.db.session import SessionLocal

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def main():
    """
    手动执行权限初始化脚本
    """
    logger.info("开始手动初始化权限、角色和超级管理员...")
    db = SessionLocal()
    try:
        await init_permissions(db)
        logger.info("权限数据初始化成功。")
    except Exception as e:
        logger.error(f"权限数据初始化失败: {e}", exc_info=True)
    finally:
        await db.close()
        logger.info("数据库连接已关闭。")


if __name__ == "__main__":
    asyncio.run(main())
