import asyncio
import logging

from sqlalchemy.ext.asyncio import AsyncSession

from app import crud
from app.db.session import SessionLocal
from app.schemas.article import ArticleCreate
from app.schemas.category import CategoryCreate

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def seed_data(db: AsyncSession):
    """
    向数据库中填充测试数据
    """
    logger.info("开始填充分类数据（如果不存在）...")
    # 创建分类层级
    tech = await crud.category.get_or_create(
        db, obj_in=CategoryCreate(name="技术", description="技术相关内容")
    )
    life = await crud.category.get_or_create(
        db, obj_in=CategoryCreate(name="生活", description="生活点滴")
    )

    backend = await crud.category.get_or_create(
        db, obj_in=CategoryCreate(name="后端开发", parent_id=tech.id)
    )
    frontend = await crud.category.get_or_create(
        db, obj_in=CategoryCreate(name="前端开发", parent_id=tech.id)
    )
    food = await crud.category.get_or_create(
        db, obj_in=CategoryCreate(name="美食", parent_id=life.id)
    )
    travel = await crud.category.get_or_create(
        db, obj_in=CategoryCreate(name="旅行", parent_id=life.id)
    )

    python = await crud.category.get_or_create(
        db, obj_in=CategoryCreate(name="Python", parent_id=backend.id)
    )

    fastapi = await crud.category.get_or_create(
        db, obj_in=CategoryCreate(name="FastAPI", parent_id=python.id)
    )
    vue = await crud.category.get_or_create(
        db, obj_in=CategoryCreate(name="Vue.js", parent_id=frontend.id)
    )
    logger.info("分类数据填充完毕。")

    logger.info("开始填充文章数据...")
    articles_to_create = [
        ArticleCreate(
            title="FastAPI入门指南",
            content="这是一篇关于FastAPI的入门文章...",
            author_id=8,
            category_id=fastapi.id,
            is_published=True,
            is_approved=True,
        ),
        ArticleCreate(
            title="Python异步编程核心",
            content="深入理解asyncio...",
            author_id=8,
            category_id=python.id,
            is_published=True,
            is_approved=True,
        ),
        ArticleCreate(
            title="Vue3组合式API详解",
            content="学习Vue3的组合式API...",
            author_id=8,
            category_id=vue.id,
            is_published=True,
            is_approved=True,
        ),
        ArticleCreate(
            title="后端架构设计模式",
            content="探讨常见的后端架构模式...",
            author_id=8,
            category_id=backend.id,
            is_published=True,
            is_approved=False,  # 待审核
        ),
        ArticleCreate(
            title="舌尖上的美味",
            content="记录一次难忘的美食之旅...",
            author_id=8,
            category_id=food.id,
            is_published=True,
            is_approved=True,
        ),
        ArticleCreate(
            title="周末短途旅行攻略",
            content="计划一次完美的周末旅行...",
            author_id=8,
            category_id=travel.id,
            is_published=False,  # 草稿
            is_approved=False,
        ),
    ]

    for article_in in articles_to_create:
        await crud.article.create(db, obj_in=article_in)
    await db.commit()
    logger.info("文章数据填充完毕。")


async def main():
    logger.info("开始执行数据填充脚本...")
    db = SessionLocal()
    try:
        await seed_data(db)
    finally:
        await db.close()
    logger.info("脚本执行完毕。")


if __name__ == "__main__":
    asyncio.run(main())
