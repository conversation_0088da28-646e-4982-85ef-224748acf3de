"""
为视频添加标签的脚本
根据视频标题、描述和分类智能分配相关标签
"""

import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import insert, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import async_session_maker
from app.models.tag import Tag, video_tags
from app.models.video import Video


class VideoTagAssigner:
    """视频标签分配器"""

    def __init__(self):
        self.tag_mappings = {
            # 技术相关标签映射
            "python": [1],  # Python
            "javascript": [5],  # React (前端相关)
            "react": [5],  # React
            "vue": [6],  # Vue
            "ai": [7, 8],  # AI, 大模型
            "机器学习": [7, 8],  # AI, 大模型
            "fastapi": [1, 3],  # Python, FastAP<PERSON>
            "docker": [4],  # Docker
            "sqlalchemy": [1, 2],  # Python, SQLAlchemy
            # 游戏相关标签
            "cs:go": [11],  # CS:GO
            "csgo": [11],  # CS:GO
            "dota2": [12],  # DOTA2
            "dota": [12],  # DOTA2
            "游戏": [13],  # 集锦
            "集锦": [13],  # 集锦
            "教学": [14],  # 教学
            "技巧": [14],  # 教学
            "指南": [14],  # 教学
            "教程": [14],  # 教学
            # 项目管理和设计
            "项目": [9],  # 项目管理
            "管理": [9],  # 项目管理
            "设计": [10],  # 设计模式
            "模式": [10],  # 设计模式
            "架构": [10],  # 设计模式
        }

        # 根据分类ID映射标签
        self.category_tag_mappings = {
            1: [1, 14],  # 技术分享 -> Python, 教学
            2: [9],  # 产品动态 -> 项目管理
            4: [9],  # 团队文化 -> 项目管理
            6: [11, 13],  # 游戏集锦 -> CS:GO, 集锦
            7: [14],  # 教学视频 -> 教学
            8: [11, 12, 13],  # 赛事录像 -> CS:GO, DOTA2, 集锦
            9: [1, 14],  # 编程教程 -> Python, 教学
            10: [1, 9],  # 开源项目 -> Python, 项目管理
            11: [9],  # 职业发展 -> 项目管理
            12: [1, 4],  # 开发工具 -> Python, Docker
            13: [1, 14],  # Python开发 -> Python, 教学
            14: [5, 6, 14],  # Web前端 -> React, Vue, 教学
            15: [5, 14],  # 移动开发 -> React, 教学
            16: [7, 8, 14],  # AI机器学习 -> AI, 大模型, 教学
            18: [10],  # 用户体验 -> 设计模式
            19: [10],  # 产品设计 -> 设计模式
        }

    async def get_existing_tags(self, session: AsyncSession) -> dict[int, str]:
        """获取现有标签"""
        result = await session.execute(select(Tag))
        tags = result.scalars().all()
        return {tag.id: tag.name for tag in tags}

    async def get_videos_without_tags(self, session: AsyncSession) -> list[Video]:
        """获取没有标签的视频"""
        # 查询所有未删除的视频
        result = await session.execute(
            select(Video).where(Video.is_deleted == False).order_by(Video.id)
        )
        videos = result.scalars().all()

        # 检查哪些视频没有标签
        videos_without_tags = []
        for video in videos:
            # 检查是否已有标签
            tag_result = await session.execute(
                select(video_tags.c.video_id).where(video_tags.c.video_id == video.id)
            )
            existing_tags = tag_result.fetchall()

            if not existing_tags:
                videos_without_tags.append(video)

        return videos_without_tags

    def analyze_video_content(self, video: Video) -> set[int]:
        """分析视频内容，返回建议的标签ID集合"""
        suggested_tags = set()

        # 分析标题和描述
        content_text = f"{video.title} {video.description or ''}".lower()

        # 根据关键词匹配标签
        for keyword, tag_ids in self.tag_mappings.items():
            if keyword in content_text:
                suggested_tags.update(tag_ids)

        # 根据分类映射标签
        if video.category_id in self.category_tag_mappings:
            suggested_tags.update(self.category_tag_mappings[video.category_id])

        # 确保每个视频至少有一个标签
        if not suggested_tags:
            # 默认给技术分享类视频添加Python和教学标签
            suggested_tags = {1, 14}

        return suggested_tags

    async def assign_tags_to_video(self, session: AsyncSession, video: Video, tag_ids: set[int]):
        """为视频分配标签"""
        for tag_id in tag_ids:
            await session.execute(insert(video_tags).values(video_id=video.id, tag_id=tag_id))
        print(
            f"✅ 为视频 '{video.title}' (ID: {video.id}) 添加了 {len(tag_ids)} 个标签: {list(tag_ids)}"
        )

    async def create_additional_tags(self, session: AsyncSession):
        """创建一些额外的标签"""
        additional_tags = [
            "JavaScript",
            "TypeScript",
            "Node.js",
            "数据库",
            "API设计",
            "微服务",
            "DevOps",
            "测试",
            "性能优化",
            "安全",
            "移动开发",
            "UI设计",
            "UX设计",
            "产品管理",
            "敏捷开发",
            "代码审查",
            "最佳实践",
            "架构设计",
            "系统设计",
            "职业发展",
        ]

        existing_tags = await self.get_existing_tags(session)
        existing_names = set(existing_tags.values())

        new_tags_added = 0
        for tag_name in additional_tags:
            if tag_name not in existing_names:
                new_tag = Tag(name=tag_name, is_default=False)
                session.add(new_tag)
                new_tags_added += 1

        if new_tags_added > 0:
            await session.commit()
            print(f"✅ 创建了 {new_tags_added} 个新标签")

    async def run(self):
        """执行标签分配任务"""
        print("🚀 开始为视频添加标签...")

        async with async_session_maker() as session:
            try:
                # 1. 创建额外的标签
                print("📝 检查并创建额外标签...")
                await self.create_additional_tags(session)

                # 2. 获取现有标签
                existing_tags = await self.get_existing_tags(session)
                print(f"📋 现有标签数量: {len(existing_tags)}")

                # 3. 获取没有标签的视频
                videos_without_tags = await self.get_videos_without_tags(session)
                print(f"🎥 找到 {len(videos_without_tags)} 个没有标签的视频")

                if not videos_without_tags:
                    print("✨ 所有视频都已经有标签了！")
                    return

                # 4. 为每个视频分配标签
                total_assigned = 0
                for video in videos_without_tags:
                    suggested_tags = self.analyze_video_content(video)
                    # 限制每个视频最多5个标签
                    suggested_tags = set(list(suggested_tags)[:5])

                    await self.assign_tags_to_video(session, video, suggested_tags)
                    total_assigned += len(suggested_tags)

                # 5. 提交更改
                await session.commit()
                print(
                    f"🎉 成功完成！共为 {len(videos_without_tags)} 个视频添加了 {total_assigned} 个标签关联"
                )

            except Exception as e:
                await session.rollback()
                print(f"❌ 执行过程中出现错误: {e}")
                raise


async def main():
    """主函数"""
    assigner = VideoTagAssigner()
    await assigner.run()


if __name__ == "__main__":
    print("=" * 60)
    print("🏷️  视频标签自动分配脚本")
    print("=" * 60)

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  脚本被用户中断")
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        sys.exit(1)

    print("\n✅ 脚本执行完成！")
