from datetime import datetime, timedelta

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud
from app.config import settings
from app.core.logging import logger
from app.db.session import SessionLocal
from app.models.user_behavior import UserInteraction
from app.services.user_cache_service import user_cache_service


async def get_active_user_ids(db: AsyncSession, days_ago: int) -> list[int]:
    """
    获取在指定天数内有高价值互动的活跃用户ID列表
    高价值互动定义为: like, favorite, comment, share
    """
    start_date = datetime.utcnow() - timedelta(days=days_ago)
    high_value_interactions = ["like", "favorite", "comment", "share"]

    stmt = (
        select(UserInteraction.user_id)
        .where(
            UserInteraction.created_at >= start_date,
            UserInteraction.interaction_type.in_(high_value_interactions),
        )
        .distinct()
    )
    result = await db.execute(stmt)
    user_ids = result.scalars().all()
    logger.info(f"发现 {len(user_ids)} 个在过去 {days_ago} 天内有高价值互动的活跃用户。")
    return user_ids


async def warm_up_cache_main(days: int):
    """缓存预热主函数"""
    logger.info("--- 开始执行用户缓存预热任务 ---")
    logger.info(f"预热范围: 最近 {days} 天内有高价值互动的用户。")

    db: AsyncSession = SessionLocal()
    try:
        active_user_ids = await get_active_user_ids(db, days_ago=days)
        if not active_user_ids:
            logger.info("没有找到需要预热的活跃用户，任务结束。")
            return

        batch_size = settings.USER_CACHE_WARMUP_BATCH_SIZE
        total_warmed_up = 0

        for i in range(0, len(active_user_ids), batch_size):
            batch_ids = active_user_ids[i : i + batch_size]
            logger.info(f"正在处理批次: {i // batch_size + 1}，包含 {len(batch_ids)} 个用户ID。")

            users = await crud.user.get_multi_by_ids(db=db, ids=batch_ids)
            if users:
                await user_cache_service.set_users_batch(users)
                total_warmed_up += len(users)
                logger.info(f"成功预热 {len(users)} 个用户缓存。")

        logger.info("--- 用户缓存预热任务完成 ---")
        logger.info(f"总共成功预热了 {total_warmed_up} / {len(active_user_ids)} 个用户。")

    except Exception as e:
        logger.error(f"缓存预热过程中发生错误: {e}", exc_info=True)
    finally:
        await db.close()
