#!/usr/bin/env python3
"""
简单的测试数据生成脚本
直接使用SQL插入，避免MPTT复杂性
"""

import asyncio
import os
import random
import sys
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text

from app.db.session import SessionLocal


async def create_simple_categories(db):
    """直接使用SQL创建分类数据"""
    print("开始创建分类数据...")

    # 检查现有分类数量
    result = await db.execute(text("SELECT COUNT(*) FROM categories"))
    count = result.scalar()

    if count >= 10:
        print(f"已存在 {count} 个分类，跳过创建")
        return

    # 简单的分类数据
    categories = [
        ("技术教程", "编程技术相关教程", "article"),
        ("产品设计", "产品设计与用户体验", "article"),
        ("行业分析", "行业趋势和分析", "article"),
        ("创业故事", "创业经验分享", "article"),
        ("职场发展", "职业规划与发展", "article"),
        ("编程教学", "编程技术视频教程", "video"),
        ("设计分享", "设计作品展示", "video"),
        ("生活记录", "日常生活视频", "video"),
        ("科技评测", "数码产品评测", "video"),
        ("知识科普", "科普知识分享", "video"),
    ]

    created_count = 0
    for name, description, category_type in categories:
        try:
            # 检查是否已存在
            check_sql = text("SELECT id FROM categories WHERE name = :name")
            result = await db.execute(check_sql, {"name": name})
            if result.scalar():
                continue

            # 插入新分类
            insert_sql = text("""
                INSERT INTO categories (name, description, category_type, article_count, video_count, created_at, updated_at, lft, rgt, level, tree_id)
                VALUES (:name, :description, :category_type, 0, 0, :created_at, :updated_at, 1, 2, 0, NULL)
            """)

            await db.execute(
                insert_sql,
                {
                    "name": name,
                    "description": description,
                    "category_type": category_type,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow(),
                },
            )
            created_count += 1

        except Exception as e:
            print(f"创建分类 {name} 时出错: {e}")
            continue

    await db.commit()
    print(f"成功创建 {created_count} 个分类")


async def create_simple_articles(db):
    """创建文章数据"""
    print("开始创建文章数据...")

    # 获取文章分类
    result = await db.execute(text("SELECT id FROM categories WHERE category_type = 'article'"))
    article_categories = [row[0] for row in result.fetchall()]

    if not article_categories:
        print("没有找到文章分类，跳过创建文章")
        return

    # 检查现有文章数量
    result = await db.execute(text("SELECT COUNT(*) FROM articles"))
    count = result.scalar()

    if count >= 20:
        print(f"已存在 {count} 篇文章，跳过创建")
        return

    # 文章数据模板
    articles_data = [
        (
            "Python异步编程入门",
            "详细介绍Python异步编程的基础概念和实践",
            "本文将从基础概念开始，逐步介绍Python异步编程的核心思想...",
        ),
        (
            "React Hooks最佳实践",
            "React Hooks的使用技巧和最佳实践",
            "React Hooks改变了我们编写React组件的方式...",
        ),
        (
            "微服务架构设计原则",
            "微服务架构的设计原则和实施策略",
            "微服务架构已成为现代软件开发的重要模式...",
        ),
        (
            "数据库性能优化技巧",
            "提升数据库查询性能的实用技巧",
            "数据库性能优化是后端开发的重要技能...",
        ),
        ("前端工程化实践", "现代前端工程化工具和流程", "前端工程化能够显著提升开发效率..."),
        (
            "Docker容器化部署",
            "使用Docker进行应用容器化部署",
            "Docker已成为现代应用部署的标准工具...",
        ),
        ("API设计最佳实践", "RESTful API设计的原则和技巧", "良好的API设计是系统集成的基础..."),
        ("代码重构技巧", "如何安全有效地进行代码重构", "代码重构是保持代码质量的重要手段..."),
        ("测试驱动开发", "TDD开发模式的实践和思考", "测试驱动开发能够提升代码质量..."),
        ("性能监控与调优", "应用性能监控和优化策略", "性能监控是保障系统稳定运行的关键..."),
        ("云原生应用开发", "云原生架构的设计和实现", "云原生应用具有更好的可扩展性..."),
        ("DevOps实践指南", "DevOps文化和工具链实践", "DevOps能够加速软件交付流程..."),
        ("机器学习入门", "机器学习基础概念和算法", "机器学习正在改变各个行业..."),
        ("区块链技术解析", "区块链技术原理和应用场景", "区块链技术具有去中心化的特点..."),
        ("移动端开发趋势", "移动应用开发的最新趋势", "移动端开发技术不断演进..."),
        ("网络安全防护", "常见网络安全威胁和防护措施", "网络安全是数字化时代的重要议题..."),
        ("大数据处理技术", "大数据处理框架和技术选型", "大数据处理需要合适的技术栈..."),
        ("人工智能应用", "AI技术在实际业务中的应用", "人工智能正在深入各个领域..."),
        ("开源项目贡献", "如何参与开源项目开发", "参与开源项目是提升技能的好方法..."),
        ("技术团队管理", "技术团队的组建和管理经验", "技术团队管理需要平衡技术和人文..."),
        ("产品思维培养", "技术人员的产品思维训练", "产品思维能够帮助技术人员更好地理解业务..."),
        ("职业规划指南", "程序员的职业发展路径", "明确的职业规划有助于个人成长..."),
    ]

    created_count = 0
    for _i, (title, description, content) in enumerate(articles_data):
        if created_count >= 25:  # 限制创建数量
            break

        try:
            # 随机选择分类和状态
            category_id = random.choice(article_categories)
            is_published = random.choice([True, True, True, False])  # 75%概率发布
            is_approved = (
                random.choice([True, True, False]) if is_published else False
            )  # 发布的文章66%概率通过审核

            # 随机生成时间
            days_ago = random.randint(1, 30)
            created_at = datetime.utcnow() - timedelta(days=days_ago)

            insert_sql = text("""
                INSERT INTO articles (title, content, description, author_id, category_id, 
                                    is_published, is_approved, visit_count, created_at, updated_at)
                VALUES (:title, :content, :description, 1, :category_id, 
                       :is_published, :is_approved, :visit_count, :created_at, :updated_at)
            """)

            await db.execute(
                insert_sql,
                {
                    "title": title,
                    "content": content,
                    "description": description,
                    "category_id": category_id,
                    "is_published": is_published,
                    "is_approved": is_approved,
                    "visit_count": random.randint(0, 1000),
                    "created_at": created_at,
                    "updated_at": created_at,
                },
            )
            created_count += 1

        except Exception as e:
            print(f"创建文章 {title} 时出错: {e}")
            continue

    await db.commit()
    print(f"成功创建 {created_count} 篇文章")


async def create_simple_reviews(db):
    """创建审核数据"""
    print("开始创建审核数据...")

    # 获取文章ID
    result = await db.execute(text("SELECT id FROM articles"))
    article_ids = [row[0] for row in result.fetchall()]

    if not article_ids:
        print("没有找到文章，跳过创建审核记录")
        return

    # 检查现有审核数量
    result = await db.execute(text("SELECT COUNT(*) FROM reviews"))
    count = result.scalar()

    if count >= 20:
        print(f"已存在 {count} 条审核记录，跳过创建")
        return

    # 审核状态和评论
    review_data = [
        ("pending", "待审核"),
        ("approved", "内容质量良好，通过审核"),
        ("approved", "文章结构清晰，内容有价值"),
        ("rejected", "内容不符合平台规范"),
        ("approved", "技术内容准确，推荐发布"),
        ("pending", "需要进一步审核"),
        ("approved", "原创内容，质量较高"),
        ("rejected", "内容重复度较高"),
        ("approved", "实用性强，对读者有帮助"),
        ("pending", "等待专家审核"),
    ]

    created_count = 0
    for i in range(min(25, len(article_ids))):  # 为每篇文章创建审核记录
        try:
            article_id = article_ids[i]
            status, comment = random.choice(review_data)

            # 随机生成审核时间
            days_ago = random.randint(0, 15)
            created_at = datetime.utcnow() - timedelta(days=days_ago)
            reviewed_at = (
                created_at + timedelta(hours=random.randint(1, 48)) if status != "pending" else None
            )

            insert_sql = text("""
                INSERT INTO reviews (content_type, content_id, status, reviewer_id, 
                                   comment, reviewed_at, created_at, updated_at)
                VALUES ('article', :content_id, :status, 1, :comment, :reviewed_at, :created_at, :updated_at)
            """)

            await db.execute(
                insert_sql,
                {
                    "content_id": article_id,
                    "status": status,
                    "comment": comment,
                    "reviewed_at": reviewed_at,
                    "created_at": created_at,
                    "updated_at": reviewed_at or created_at,
                },
            )
            created_count += 1

        except Exception as e:
            print(f"创建审核记录时出错: {e}")
            continue

    await db.commit()
    print(f"成功创建 {created_count} 条审核记录")


async def main():
    """主函数"""
    print("开始创建测试数据...")

    async with SessionLocal() as db:
        try:
            # 1. 创建分类
            await create_simple_categories(db)

            # 2. 创建文章
            await create_simple_articles(db)

            # 3. 创建审核记录
            await create_simple_reviews(db)

            print("\n=== 数据创建完成 ===")

            # 统计最终数据
            result = await db.execute(text("SELECT COUNT(*) FROM categories"))
            categories_count = result.scalar()

            result = await db.execute(text("SELECT COUNT(*) FROM articles"))
            articles_count = result.scalar()

            result = await db.execute(text("SELECT COUNT(*) FROM reviews"))
            reviews_count = result.scalar()

            print(f"分类数量: {categories_count}")
            print(f"文章数量: {articles_count}")
            print(f"审核记录数量: {reviews_count}")

            # 统计各状态数据
            result = await db.execute(
                text("""
                SELECT is_published, is_approved, COUNT(*) 
                FROM articles 
                GROUP BY is_published, is_approved
            """)
            )
            print("\n文章状态分布:")
            for row in result.fetchall():
                published, approved, count = row
                status = f"发布: {published}, 审核: {approved}"
                print(f"  {status}: {count}篇")

            result = await db.execute(
                text("""
                SELECT status, COUNT(*) 
                FROM reviews 
                GROUP BY status
            """)
            )
            print("\n审核状态分布:")
            for row in result.fetchall():
                status, count = row
                print(f"  {status}: {count}条")

        except Exception as e:
            print(f"创建测试数据时出错: {e}")
            await db.rollback()
            raise


if __name__ == "__main__":
    asyncio.run(main())
