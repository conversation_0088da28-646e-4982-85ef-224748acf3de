"""图片和视频处理相关的Celery任务"""

import asyncio
import os
import shutil
from io import BytesIO

import ffmpeg
from ffmpeg_progress_yield import FfmpegProgress
from PIL import Image

from app.config import get_settings
from app.core.celery import app
from app.core.logging import logger
from app.crud import video as crud_video
from app.db.session import SessionLocal
from app.schemas.upload import UploadStatus
from app.schemas.video import VideoUpdate
from app.services.partial_upload import PartialUpload
from app.services.task_status_service import TaskStatusService

settings = get_settings()

# 与 upload.py 中定义一致的临时目录
TEMP_UPLOAD_DIR = "/tmp/uploads"
os.makedirs(TEMP_UPLOAD_DIR, exist_ok=True)


async def process_and_upload_image(
    file_data: bytes, file_hash: str, quality: int = 80
) -> str | None:
    """将图片转换为webp格式并上传到OSS（GIF文件保持原格式）

    Args:
        file_data: 图片二进制数据
        file_hash: 文件哈希值
        quality: webp质量，默认80

    Returns:
        str: OSS文件URL，如果处理或上传失败返回None
    """
    try:
        # 检查图片格式
        with Image.open(BytesIO(file_data)) as img:
            # 如果是GIF格式，直接上传原文件
            if img.format == "GIF" or img.format == "gif":
                partial_client = PartialUpload()
                await asyncio.to_thread(
                    partial_client.upload, file_data, file_hash, file_type="gif"
                )
                return f"/steam/images/{file_hash}.gif"

            # 其他格式转换为webp
            output_buffer = BytesIO()
            img.save(output_buffer, "webp", quality=quality)
            webp_data = output_buffer.getvalue()

        partial_client = PartialUpload()
        await asyncio.to_thread(partial_client.upload, webp_data, file_hash)
        return f"/steam/images/{file_hash}.webp"
    except Exception as e:
        logger.error(f"Failed to process and upload image: {e}")
        return None


async def _process_video_core(
    temp_file_path: str,
    original_file_hash: str,
    video_quality: str,
    original_filename: str,
    task_id: str | None = None,
) -> dict | None:
    """视频处理核心逻辑，转换为HLS格式，可被多个Celery任务复用"""
    input_path = temp_file_path
    # HLS输出是一个目录
    output_dir = f"/tmp/{original_file_hash}_hls"
    os.makedirs(output_dir, exist_ok=True)
    cover_output_path = f"/tmp/{original_file_hash}_cover.jpg"

    try:
        # 提取元数据
        probe = ffmpeg.probe(input_path)
        video_stream = next(
            (stream for stream in probe["streams"] if stream["codec_type"] == "video"), None
        )
        if not video_stream:
            logger.error(f"No video stream found for hash {original_file_hash}")
            return None
        duration = float(probe["format"]["duration"])
        width = int(video_stream["width"])
        height = int(video_stream["height"])
        logger.info(f"Video metadata - Duration: {duration}s, Resolution: {width}x{height}")

        # --- 截取封面图 ---
        cover_url = None
        try:
            capture_time = min(5.0, duration * 0.1)
            (
                ffmpeg.input(input_path, ss=capture_time)
                .output(cover_output_path, vframes=1, format="image2", vcodec="mjpeg")
                .run(capture_stdout=True, capture_stderr=True, overwrite_output=True)
            )
            with open(cover_output_path, "rb") as f:
                cover_data = f.read()
            cover_hash = f"{original_file_hash}_cover"
            cover_url = await process_and_upload_image(cover_data, cover_hash)
            logger.info(f"Successfully generated and uploaded cover: {cover_url}")
        except Exception as cover_e:
            logger.error(f"Failed to generate video cover for hash {original_file_hash}: {cover_e}")
            cover_url = None

        # --- FFmpeg转码为HLS与实时进度更新 ---
        quality_presets = {
            "high": {"b:v": "2000k", "b:a": "192k"},  # 网速良好
            "low": {"b:v": "800k", "b:a": "128k"},  # 网速不佳
        }

        # 构建多码率输出命令
        args = (
            ffmpeg.input(input_path)
            .output(
                "dummy.m3u8",  # a dummy file name, will be replaced by HLS options
                # Video streams
                map="0:v:0",
                c_v_0="libx264",
                b_v_0=quality_presets["low"]["b:v"],
                map_1="0:v:0",
                c_v_1="libx264",
                b_v_1=quality_presets["high"]["b:v"],
                # Audio streams
                map_2="0:a:0",
                c_a_0="aac",
                b_a_0=quality_presets["low"]["b:a"],
                map_3="0:a:0",
                c_a_1="aac",
                b_a_1=quality_presets["high"]["b:a"],
                # HLS options
                f="hls",
                var_stream_map="v:0,a:0 v:1,a:1",
                master_pl_name="master.m3u8",
                hls_time="4",
                hls_list_size="0",
                hls_segment_filename=f"{output_dir}/stream_%v/segment%03d.ts",
            )
            .global_args("-progress", "pipe:1")
        )
        # Manually add the stream-specific playlist names at the end
        cmd = args.get_args() + [f"{output_dir}/stream_%v.m3u8"]

        # 只有在提供了task_id时才报告进度
        if task_id:
            ff = FfmpegProgress(cmd)
            last_reported_progress = 0
            for progress in ff.run_command_with_progress():
                current_progress = 0.1 + (progress / 100.0) * 0.7
                if current_progress - last_reported_progress > 0.05:
                    await TaskStatusService.update_status(
                        task_id,
                        status=UploadStatus.PROCESSING,
                        progress=round(current_progress, 2),
                    )
                    last_reported_progress = current_progress
        else:
            ffmpeg.run(cmd, overwrite_output=True, capture_stdout=True, capture_stderr=True)

        # 上传整个HLS目录
        partial_client = PartialUpload()
        oss_target_path = f"steam/hls/{original_file_hash}"
        upload_success = await asyncio.to_thread(
            partial_client.upload_directory, output_dir, oss_target_path
        )

        if not upload_success:
            raise Exception("Failed to upload HLS directory to OSS.")

        file_url = f"/{oss_target_path}/master.m3u8"

        return {
            "file_url": file_url,
            "cover_url": cover_url,
            "duration": int(duration),
            "width": width,
            "height": height,
        }
    except Exception as e:
        logger.error(f"Core video processing failed for hash {original_file_hash}: {e}")
        if isinstance(e, ffmpeg.Error):
            logger.error(f"FFmpeg stderr: {e.stderr.decode()}")
        return None
    finally:
        # 清理临时文件和目录
        if os.path.exists(output_dir):
            shutil.rmtree(output_dir)
        if os.path.exists(cover_output_path):
            os.remove(cover_output_path)


@app.task(bind=True)
def process_batch_video_and_update_db(
    self,
    video_id: int,
    temp_file_path: str,
    original_file_hash: str,
    original_filename: str,
    video_quality: str = "medium",
) -> int | None:
    """
    (新) 处理视频，上传后更新数据库，并实时播报状态
    使用同步包装器处理异步操作
    """
    import asyncio

    task_id = self.request.id

    async def _async_process():
        try:
            # 1. 开始处理，更新状态为 'processing'
            await TaskStatusService.update_status(
                task_id, status=UploadStatus.PROCESSING, progress=0.1
            )

            # 2. 调用核心处理逻辑，并传入task_id以报告进度
            result = await _process_video_core(
                temp_file_path,
                original_file_hash,
                video_quality,
                original_filename,
                task_id,
            )

            if not result:
                error_msg = f"Video processing failed for video_id: {video_id}"
                logger.error(error_msg)
                await TaskStatusService.update_status(
                    task_id, status=UploadStatus.UPLOAD_ERROR, message=error_msg
                )
                return None

            await TaskStatusService.update_status(
                task_id, status=UploadStatus.PROCESSING, progress=0.8
            )

            # 3. 更新数据库
            await TaskStatusService.update_status(task_id, status=UploadStatus.CREATING)
            db = SessionLocal()
            try:
                video = db.get(crud_video.model, video_id)
                if not video:
                    error_msg = f"Video with id {video_id} not found in DB for update."
                    logger.error(error_msg)
                    await TaskStatusService.update_status(
                        task_id, status=UploadStatus.CREATE_ERROR, message=error_msg
                    )
                    return None

                update_data = VideoUpdate(
                    url=result["file_url"],
                    cover_url=result.get("cover_url"),
                    duration=result["duration"],
                    width=result["width"],
                    height=result["height"],
                )
                crud_video.update(db, db_obj=video, obj_in=update_data)
                await db.commit()  # 先提交video的更新

                # 再写入 file_hashes 表
                from app.crud.file_hash import file_hash as crud_file_hash
                from app.schemas.file_hash import FileHashCreate

                file_hash_obj = FileHashCreate(
                    file_path=result["file_url"],
                    file_hash=original_file_hash,  # 存入原始文件哈希
                    file_metadata=result,
                )
                await crud_file_hash.create(db, obj_in=file_hash_obj)
                await db.commit()

                logger.info(f"Successfully processed and updated video_id: {video_id}")

                # 4. 全部完成
                await TaskStatusService.update_status(
                    task_id, status=UploadStatus.COMPLETE, file_metadata=result
                )
                return video_id
            except Exception as e:
                error_msg = f"Failed to update video_id {video_id} in DB: {e}"
                logger.error(error_msg)
                await TaskStatusService.update_status(
                    task_id, status=UploadStatus.CREATE_ERROR, message=error_msg
                )
                db.rollback()
                return None
            finally:
                db.close()
        except Exception as e:
            error_msg = f"An unexpected error occurred in task for video_id {video_id}: {e}"
            logger.error(error_msg, exc_info=True)
            await TaskStatusService.update_status(
                task_id, status=UploadStatus.UPLOAD_ERROR, message=error_msg
            )
            return None

    # 使用asyncio.run执行异步函数
    try:
        return asyncio.run(_async_process())
    except Exception as e:
        logger.error(f"Failed to run async task: {e}")
        return None


@app.task(bind=True)
def link_video_to_existing_file(self, video_id: int, existing_file_hash_id: int) -> int | None:
    """
    (新) 秒传任务：将新的视频记录链接到已存在的物理文件
    使用同步包装器处理异步操作
    """
    import asyncio

    task_id = self.request.id

    async def _async_link():
        db = SessionLocal()
        try:
            # 1. 更新状态为"处理中"，虽然这个过程很快
            await TaskStatusService.update_status(
                task_id, status=UploadStatus.PROCESSING, progress=0.5
            )

            # 2. 从 file_hash 表获取已存在文件的信息
            from app.crud.file_hash import file_hash as crud_file_hash

            existing_file = db.get(crud_file_hash.model, existing_file_hash_id)
            if not existing_file or not existing_file.file_metadata:
                error_msg = (
                    f"File hash record {existing_file_hash_id} not found or has no file_metadata."
                )
                logger.error(error_msg)
                await TaskStatusService.update_status(
                    task_id, status=UploadStatus.CREATE_ERROR, message=error_msg
                )
                return None

            # 3. 更新新的 video 记录
            video = db.get(crud_video.model, video_id)
            if not video:
                error_msg = f"Video with id {video_id} not found for linking."
                logger.error(error_msg)
                await TaskStatusService.update_status(
                    task_id, status=UploadStatus.CREATE_ERROR, message=error_msg
                )
                return None

            update_data = VideoUpdate(
                url=existing_file.file_path,
                cover_url=existing_file.file_metadata.get("cover_url"),
                duration=existing_file.file_metadata.get("duration"),
                width=existing_file.file_metadata.get("width"),
                height=existing_file.file_metadata.get("height"),
            )
            crud_video.update(db, db_obj=video, obj_in=update_data)

            # 4. 标记任务完成
            await TaskStatusService.update_status(
                task_id, status=UploadStatus.COMPLETE, file_metadata=existing_file.file_metadata
            )
            logger.info(f"Successfully linked video_id: {video_id} to existing file.")
            return video_id
        except Exception as e:
            error_msg = f"Failed to link video_id {video_id}: {e}"
            logger.error(error_msg, exc_info=True)
            await TaskStatusService.update_status(
                task_id, status=UploadStatus.CREATE_ERROR, message=error_msg
            )
            return None
        finally:
            db.close()

    # 使用asyncio.run执行异步函数
    try:
        return asyncio.run(_async_link())
    except Exception as e:
        logger.error(f"Failed to run async link task: {e}")
        return None


@app.task
def process_image_task(file_data: bytes, file_hash: str, quality: int = 80) -> str | None:
    """Celery task wrapper for processing and uploading an image."""
    import asyncio

    # 由于此任务是从同步的API端点调用的，我们在这里使用asyncio.run
    # 这与此文件中其他新任务（如process_batch_video_and_update_db）的模式保持一致
    return asyncio.run(process_and_upload_image(file_data, file_hash, quality))
