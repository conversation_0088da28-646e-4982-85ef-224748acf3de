from sqlalchemy import select

from app.core.celery import app
from app.core.logging import logger
from app.db import redis as redis_client
from app.db.session import SessionLocal
from app.models.article import Article
from app.utils.bloom_filters import article_bloom_filter


@app.task(
    name="app.tasks.bloom_tasks.task_rebuild_article_bloom_filter",
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=60,
    retry_jitter=True,
)
async def task_rebuild_article_bloom_filter(
    drop_existing: bool = True, page_size: int = 2000
) -> int:
    """
    全量重建文章ID布隆过滤器

    流程：
    1) 可选：删除现有的 Bloom 过滤器键，确保误判率与容量重新初始化
    2) 重新创建（RESERVE）过滤器
    3) 分页扫描 Article.id，批量写入（BF.MADD）

    Args:
        drop_existing: 是否先删除现有 Bloom 过滤器键（建议在低峰时段执行，全量重建场景建议 True）
        page_size: 每页扫描的行数

    Returns:
        int: 本次重建共计写入的文章ID数量
    """
    logger.info("开始重建文章ID布隆过滤器...")
    db = SessionLocal()
    total_added = 0
    try:
        if drop_existing:
            try:
                # 直接删除旧的 Bloom 键，便于完全重建
                await redis_client.delete_key(article_bloom_filter.filter_key)  # type: ignore[attr-defined]
                logger.info(f"已删除旧的布隆过滤器键: {article_bloom_filter.filter_key}")  # type: ignore[attr-defined]
            except Exception as e:
                logger.warning(f"删除旧的布隆过滤器键失败（忽略继续）: {e}")

        # 无论是否删除，都确保过滤器存在（RESERVE幂等处理）
        await article_bloom_filter._reserve_filter_if_not_exists()  # type: ignore[attr-defined]

        page = 0
        while True:
            offset = page * page_size
            logger.info(f"扫描文章ID，page={page}, offset={offset}, page_size={page_size}")
            result = await db.execute(select(Article.id).offset(offset).limit(page_size))
            ids: list[int] = result.scalars().all()

            if not ids:
                break

            str_ids = [str(i) for i in ids]

            # 分块批量写入，避免一次 MADD 参数过长
            chunk_size = 1000
            for i in range(0, len(str_ids), chunk_size):
                chunk = str_ids[i : i + chunk_size]
                try:
                    await article_bloom_filter.add_multi(chunk)  # type: ignore[attr-defined]
                except Exception as e:
                    logger.warning(f"BF.MADD 写入分块失败（继续下一块）: {e}")

            total_added += len(ids)
            logger.info(f"本页写入 {len(ids)} 个ID，累计 {total_added}")
            page += 1

        logger.info(f"文章ID布隆过滤器重建完成，总计写入 {total_added} 个ID")
        return total_added

    except Exception as e:
        logger.error(f"重建文章布隆过滤器失败: {e}")
        return total_added
    finally:
        await db.close()
