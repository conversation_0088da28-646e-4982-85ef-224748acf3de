"""
推荐系统相关的Celery任务
"""

import asyncio
from collections import defaultdict

from sklearn.feature_extraction import DictVectorizer
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.celery import app
from app.crud import article as crud_article
from app.crud import user as crud_user
from app.crud import user_behavior as crud_user_behavior
from app.crud import video as crud_video
from app.db.redis import (
    delete_key,
    hash_get,
    hash_get_all,
    hash_set,
    sorted_set_add,
)
from app.db.session import SessionLocal


@app.task
def update_user_profiles():
    """
    定时任务：更新用户画像
    """

    async def update():
        async with SessionLocal() as db:
            users = await crud_user.get_multi(db)
            for user in users:
                await update_user_profile(db, user.id)

    asyncio.run(update())


async def update_user_profile(db: AsyncSession, user_id: int):
    """
    更新单个用户画像
    """
    behaviors = await crud_user_behavior.get_by_user(db, user_id=user_id)
    profile = defaultdict(float)
    for behavior in behaviors:
        # 根据行为类型给予不同权重
        weight = {
            "click": 1.0,
            "like": 2.0,
            "favorite": 3.0,
            "comment": 4.0,
            "share": 5.0,
        }.get(behavior.behavior_type, 0.5)
        # 这里简化处理，实际应用中会更复杂
        profile[f"category:{behavior.content_type}:{behavior.content_id}"] += weight

    # 将用户画像存入Redis
    await hash_set("user_profiles", str(user_id), str(profile))


@app.task
def update_content_features():
    """
    定时任务：更新内容特征
    """

    async def update():
        async with SessionLocal() as db:
            articles = await crud_article.get_multi(db)
            for article in articles:
                await update_content_feature(db, "article", article.id)
            videos = await crud_video.get_multi(db)
            for video in videos:
                await update_content_feature(db, "video", video.id)

    asyncio.run(update())


async def update_content_feature(db: AsyncSession, content_type: str, content_id: int):
    """
    更新单个内容特征
    """
    # 简化处理，实际应用中会从内容中提取关键词、标签等
    feature = {"category": content_type}
    await hash_set("content_features", f"{content_type}:{content_id}", str(feature))


@app.task
def generate_recommendations():
    """
    定时任务：为所有用户生成推荐
    """

    async def generate():
        async with SessionLocal() as db:
            users = await crud_user.get_multi(db)
            for user in users:
                await generate_recommendation_for_user(db, user.id)

    asyncio.run(generate())


async def generate_recommendation_for_user(db: AsyncSession, user_id: int):
    """
    为单个用户生成推荐
    """
    user_profile_raw = await hash_get("user_profiles", str(user_id))
    if not user_profile_raw:
        return
    user_profile = eval(user_profile_raw)

    content_features_raw = await hash_get_all("content_features")
    if not content_features_raw:
        return

    content_features = {
        k.decode("utf-8"): eval(v.decode("utf-8")) for k, v in content_features_raw.items()
    }

    # 使用DictVectorizer转换特征
    vectorizer = DictVectorizer(sparse=True)
    user_vector = vectorizer.fit_transform([user_profile])
    content_vectors = vectorizer.transform(content_features.values())

    # 计算相似度（这里用点积简化）
    scores = user_vector.dot(content_vectors.T).toarray()[0]

    # 生成推荐列表
    recommendations = sorted(
        zip(content_features.keys(), scores, strict=False), key=lambda x: x[1], reverse=True
    )

    # 存储推荐结果到Redis
    rec_key = f"recommendations:{user_id}"
    await delete_key(rec_key)
    rec_to_store = dict(recommendations[:100])  # 只存前100个
    if rec_to_store:
        await sorted_set_add(rec_key, rec_to_store)


@app.task
def update_hot_recommendations():
    """
    定时任务：更新热门推荐
    """

    async def update():
        async with SessionLocal() as db:
            # 聚合用户行为数据，计算热门内容
            hot_articles = await crud_user_behavior.get_hot_content(db, content_type="article")
            hot_videos = await crud_user_behavior.get_hot_content(db, content_type="video")

            hot_rec_key_article = "rec_pool:hot:article"
            hot_rec_key_video = "rec_pool:hot:video"

            await delete_key(hot_rec_key_article)
            if hot_articles:
                await sorted_set_add(
                    hot_rec_key_article,
                    {str(item.content_id): item.hot_score for item in hot_articles},
                )

            await delete_key(hot_rec_key_video)
            if hot_videos:
                await sorted_set_add(
                    hot_rec_key_video,
                    {str(item.content_id): item.hot_score for item in hot_videos},
                )

    asyncio.run(update())


@app.task
def update_latest_recommendations():
    """
    定时任务：更新最新推荐
    """

    async def update():
        async with SessionLocal() as db:
            latest_articles = await crud_article.get_multi(db, limit=100)
            latest_videos = await crud_video.get_multi(db, limit=100)

            latest_rec_key_article = "rec_pool:latest:article"
            latest_rec_key_video = "rec_pool:latest:video"

            await delete_key(latest_rec_key_article)
            if latest_articles:
                await sorted_set_add(
                    latest_rec_key_article,
                    {str(item.id): item.created_at.timestamp() for item in latest_articles},
                )

            await delete_key(latest_rec_key_video)
            if latest_videos:
                await sorted_set_add(
                    latest_rec_key_video,
                    {str(item.id): item.created_at.timestamp() for item in latest_videos},
                )

    asyncio.run(update())
