"""celery 发送短信任务"""

from app.core.celery import app
from app.core.logging import logger
from app.core.sms_client import SmsClient
from app.schemas.sms import SMSIn


@app.task(bind=True)
def send_sms_task(self, sms_data: dict) -> None:
    """发送短信任务

    该任务使用tenacity库实现了重试机制，包括：
    - 业务错误（如模板不存在）不重试
    - 其他错误（如网络错误）最多重试3次
    - 使用指数退避算法计算重试间隔（4s, 8s, 10s）

    Args:
        sms_data: 短信发送请求数据

    Returns:
        None

    Raises:
        SmsSendError: 短信发送失败
    """
    try:
        sms_in = SMSIn(**sms_data)
        result = SmsClient.send_sms(sms_in)
        logger.info(f"短信发送成功，手机号：{sms_in.phone_numbers}，响应：{result}")
    except Exception as e:
        logger.error(f"短信发送最终失败：{str(e)}")
        # 由于SmsClient已经实现了重试逻辑，这里直接抛出异常
        raise
