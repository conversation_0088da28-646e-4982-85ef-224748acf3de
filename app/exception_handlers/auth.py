"""认证异常处理器"""

from typing import Any
from fastapi import Request
from fastapi.responses import JSONResponse

from app.core.response_wrapper import error_response
from app.core.logging import logger
from app.exceptions.auth import (
    AuthenticationError,
    UserNotFoundError,
    InvalidCredentialsError,
    UserDisabledError,
    UserAlreadyExistsError,
    DeviceNotTrustedError,
    DeviceVerificationRequiredError,
    SMSInvalidCodeError,
    SMSCodeExpiredError,
    SMSRateLimitError,
    WeChatAPIError,
    WeChatInvalidOpenIDError
)


async def authentication_exception_handler(request: Request, exc: AuthenticationError) -> JSONResponse:
    """处理认证基础异常"""
    
    logger.warning(
        f"Authentication error: {exc.error_code} - {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
            "method": request.method,
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.response_code,
            message=exc.message,
            data=exc.details
        )
    )


async def user_not_found_exception_handler(request: Request, exc: UserNotFoundError) -> JSONResponse:
    """处理用户不存在异常"""
    
    logger.warning(
        f"User not found error: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.response_code,
            message=exc.message,
            data=exc.details
        )
    )


async def invalid_credentials_exception_handler(request: Request, exc: InvalidCredentialsError) -> JSONResponse:
    """处理无效凭据异常"""
    
    logger.warning(
        f"Invalid credentials error: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.response_code,
            message=exc.message,
            data=exc.details
        )
    )


async def user_disabled_exception_handler(request: Request, exc: UserDisabledError) -> JSONResponse:
    """处理用户已禁用异常"""
    
    logger.warning(
        f"User disabled error: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.response_code,
            message=exc.message,
            data=exc.details
        )
    )


async def user_already_exists_exception_handler(request: Request, exc: UserAlreadyExistsError) -> JSONResponse:
    """处理用户已存在异常"""
    
    logger.warning(
        f"User already exists error: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.response_code,
            message=exc.message,
            data=exc.details
        )
    )


async def device_not_trusted_exception_handler(request: Request, exc: DeviceNotTrustedError) -> JSONResponse:
    """处理设备不受信任异常"""
    
    logger.warning(
        f"Device not trusted error: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.response_code,
            message=exc.message,
            data=exc.details
        )
    )


async def device_verification_required_exception_handler(request: Request, exc: DeviceVerificationRequiredError) -> JSONResponse:
    """处理需要设备验证异常"""
    
    logger.warning(
        f"Device verification required error: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.response_code,
            message=exc.message,
            data=exc.details
        )
    )


async def sms_invalid_code_exception_handler(request: Request, exc: SMSInvalidCodeError) -> JSONResponse:
    """处理无效验证码异常"""
    
    logger.warning(
        f"SMS invalid code error: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
        }
    )
    
    # 脱敏处理手机号
    details = exc.details.copy() if exc.details else {}
    if "phone" in details and details["phone"]:
        phone = details["phone"]
        if len(phone) == 11 and phone.isdigit():
            details["phone"] = f"{phone[:3]}****{phone[7:]}"
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.response_code,
            message=exc.message,
            data=details
        )
    )


async def sms_code_expired_exception_handler(request: Request, exc: SMSCodeExpiredError) -> JSONResponse:
    """处理验证码已过期异常"""
    
    logger.warning(
        f"SMS code expired error: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
        }
    )
    
    # 脱敏处理手机号
    details = exc.details.copy() if exc.details else {}
    if "phone" in details and details["phone"]:
        phone = details["phone"]
        if len(phone) == 11 and phone.isdigit():
            details["phone"] = f"{phone[:3]}****{phone[7:]}"
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.response_code,
            message=exc.message,
            data=details
        )
    )


async def sms_rate_limit_exception_handler(request: Request, exc: SMSRateLimitError) -> JSONResponse:
    """处理短信频率限制异常"""
    
    logger.warning(
        f"SMS rate limit error: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
            "client_ip": request.client.host if request.client else None,
        }
    )
    
    details = exc.details.copy() if exc.details else {}
    if "phone" in details and details["phone"]:
        phone = details["phone"]
        if len(phone) == 11 and phone.isdigit():
            details["phone"] = f"{phone[:3]}****{phone[7:]}"
    
    response = JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.response_code,
            message=exc.message,
            data=details
        )
    )
    
    # 设置 Retry-After 头
    if "retry_after" in details:
        response.headers["Retry-After"] = str(details["retry_after"])
    
    return response


async def wechat_api_exception_handler(request: Request, exc: WeChatAPIError) -> JSONResponse:
    """处理微信API调用异常"""
    
    logger.warning(
        f"WeChat API error: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.response_code,
            message=exc.message,
            data=exc.details
        )
    )


async def wechat_invalid_openid_exception_handler(request: Request, exc: WeChatInvalidOpenIDError) -> JSONResponse:
    """处理无效的OpenID异常"""
    
    logger.warning(
        f"WeChat invalid openid error: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.response_code,
            message=exc.message,
            data=exc.details
        )
    )