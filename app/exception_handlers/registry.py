"""异常处理器注册模块"""

from fastapi import FastAPI

from app.exception_handlers.general import (
    http_exception_handler,
    starlette_http_exception_handler,
    validation_exception_handler,
    generic_exception_handler
)
from app.exception_handlers.auth import (
    authentication_exception_handler,
    user_not_found_exception_handler,
    invalid_credentials_exception_handler,
    user_disabled_exception_handler,
    user_already_exists_exception_handler,
    device_not_trusted_exception_handler,
    device_verification_required_exception_handler,
    sms_invalid_code_exception_handler,
    sms_code_expired_exception_handler,
    sms_rate_limit_exception_handler,
    wechat_api_exception_handler,
    wechat_invalid_openid_exception_handler
)
from app.exception_handlers.article import (
    article_exception_handler,
    article_not_found_exception_handler,
    article_permission_denied_exception_handler,
    article_already_exists_exception_handler
)
from app.exception_handlers.video import (
    video_exception_handler,
    video_not_found_exception_handler,
    video_permission_denied_exception_handler,
    video_folder_not_found_exception_handler
)
from app.exceptions.auth import (
    AuthenticationError,
    UserNotFoundError,
    InvalidCredentialsError,
    UserDisabledError,
    UserAlreadyExistsError,
    DeviceNotTrustedError,
    DeviceVerificationRequiredError,
    SMSInvalidCodeError,
    SMSCodeExpiredError,
    SMSRateLimitError,
    WeChatAPIError,
    WeChatInvalidOpenIDError
)
from app.exceptions.article import (
    ArticleError,
    ArticleNotFoundError,
    ArticlePermissionDeniedError,
    ArticleAlreadyExistsError
)
from app.exceptions.video import (
    VideoError,
    VideoNotFoundError,
    VideoPermissionDeniedError,
    VideoFolderNotFoundError
)
from fastapi import HTTPException
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException


def register_exception_handlers(app: FastAPI) -> None:
    """注册所有异常处理器到FastAPI应用"""
    
    # 认证异常处理器
    app.add_exception_handler(AuthenticationError, authentication_exception_handler)
    app.add_exception_handler(UserNotFoundError, user_not_found_exception_handler)
    app.add_exception_handler(InvalidCredentialsError, invalid_credentials_exception_handler)
    app.add_exception_handler(UserDisabledError, user_disabled_exception_handler)
    app.add_exception_handler(UserAlreadyExistsError, user_already_exists_exception_handler)
    app.add_exception_handler(DeviceNotTrustedError, device_not_trusted_exception_handler)
    app.add_exception_handler(DeviceVerificationRequiredError, device_verification_required_exception_handler)
    app.add_exception_handler(SMSInvalidCodeError, sms_invalid_code_exception_handler)
    app.add_exception_handler(SMSCodeExpiredError, sms_code_expired_exception_handler)
    app.add_exception_handler(SMSRateLimitError, sms_rate_limit_exception_handler)
    app.add_exception_handler(WeChatAPIError, wechat_api_exception_handler)
    app.add_exception_handler(WeChatInvalidOpenIDError, wechat_invalid_openid_exception_handler)
    
    # 文章异常处理器
    app.add_exception_handler(ArticleError, article_exception_handler)
    app.add_exception_handler(ArticleNotFoundError, article_not_found_exception_handler)
    app.add_exception_handler(ArticlePermissionDeniedError, article_permission_denied_exception_handler)
    app.add_exception_handler(ArticleAlreadyExistsError, article_already_exists_exception_handler)
    
    # 视频异常处理器
    app.add_exception_handler(VideoError, video_exception_handler)
    app.add_exception_handler(VideoNotFoundError, video_not_found_exception_handler)
    app.add_exception_handler(VideoPermissionDeniedError, video_permission_denied_exception_handler)
    app.add_exception_handler(VideoFolderNotFoundError, video_folder_not_found_exception_handler)
    
    # 通用异常处理器
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, starlette_http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(Exception, generic_exception_handler)