"""视频异常处理器"""

from fastapi import Request
from fastapi.responses import JSONResponse

from app.core.response_wrapper import error_response
from app.core.logging import logger
from app.exceptions.video import (
    VideoError,
    VideoNotFoundError,
    VideoPermissionDeniedError,
    VideoFolderNotFoundError
)


async def video_exception_handler(request: Request, exc: VideoError) -> JSONResponse:
    """处理视频基础异常"""
    
    logger.warning(
        f"Video error: {exc.error_code} - {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.response_code,
            message=exc.message,
            data=exc.details
        )
    )


async def video_not_found_exception_handler(request: Request, exc: VideoNotFoundError) -> JSONResponse:
    """处理视频不存在异常"""
    
    logger.warning(
        f"Video not found error: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.response_code,
            message=exc.message,
            data=exc.details
        )
    )


async def video_permission_denied_exception_handler(request: Request, exc: VideoPermissionDeniedError) -> JSONResponse:
    """处理视频权限异常"""
    
    logger.warning(
        f"Video permission denied error: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.response_code,
            message=exc.message,
            data=exc.details
        )
    )


async def video_folder_not_found_exception_handler(request: Request, exc: VideoFolderNotFoundError) -> JSONResponse:
    """处理视频文件夹不存在异常"""
    
    logger.warning(
        f"Video folder not found error: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.response_code,
            message=exc.message,
            data=exc.details
        )
    )