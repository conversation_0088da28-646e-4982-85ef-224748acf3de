"""文章异常处理器"""

from fastapi import Request
from fastapi.responses import JSONResponse

from app.core.response_wrapper import error_response
from app.core.logging import logger
from app.exceptions.article import (
    ArticleError,
    ArticleNotFoundError,
    ArticlePermissionDeniedError,
    ArticleAlreadyExistsError
)


async def article_exception_handler(request: Request, exc: ArticleError) -> JSONResponse:
    """处理文章基础异常"""
    
    logger.warning(
        f"Article error: {exc.error_code} - {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.response_code,
            message=exc.message,
            data=exc.details
        )
    )


async def article_not_found_exception_handler(request: Request, exc: ArticleNotFoundError) -> JSONResponse:
    """处理文章不存在异常"""
    
    logger.warning(
        f"Article not found error: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.response_code,
            message=exc.message,
            data=exc.details
        )
    )


async def article_permission_denied_exception_handler(request: Request, exc: ArticlePermissionDeniedError) -> JSONResponse:
    """处理文章权限异常"""
    
    logger.warning(
        f"Article permission denied error: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.response_code,
            message=exc.message,
            data=exc.details
        )
    )


async def article_already_exists_exception_handler(request: Request, exc: ArticleAlreadyExistsError) -> JSONResponse:
    """处理文章已存在异常"""
    
    logger.warning(
        f"Article already exists error: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.response_code,
            message=exc.message,
            data=exc.details
        )
    )