"""
消息通知服务模块
提供实时消息推送功能
"""

from .models import Notification, NotificationPriority, NotificationStatus, NotificationType
from .schemas import (
    BroadcastNotificationCreate,
    BroadcastResponse,
    NewNotificationMessage,
    NotificationCreate,
    NotificationListResponse,
    NotificationResponse,
    NotificationUpdate,
    SystemNotificationMessage,
    UnreadCountMessage,
    UnreadCountResponse,
    WebSocketMessage,
)

__all__ = [
    "Notification",
    "NotificationType",
    "NotificationPriority",
    "NotificationStatus",
    "BroadcastNotificationCreate",
    "BroadcastResponse",
    "NotificationCreate",
    "NotificationUpdate",
    "NotificationResponse",
    "NotificationListResponse",
    "UnreadCountResponse",
    "WebSocketMessage",
    "NewNotificationMessage",
    "UnreadCountMessage",
    "SystemNotificationMessage",
]
