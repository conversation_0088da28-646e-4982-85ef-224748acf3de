from datetime import datetime
from typing import Any

from pydantic import BaseModel, Field, validator

from app.notifications.models import NotificationPriority, NotificationStatus, NotificationType


class NotificationBase(BaseModel):
    type: NotificationType
    priority: NotificationPriority = NotificationPriority.NORMAL
    title: str = Field(..., min_length=1, max_length=200)
    message: str = Field(..., min_length=1)
    data: dict[str, Any] | None = None
    action_url: str | None = Field(None, max_length=500)


class NotificationCreate(NotificationBase):
    user_id: int
    expires_in_hours: int | None = Field(None, ge=1, le=168)  # 最多7天


class NotificationUpdate(BaseModel):
    status: NotificationStatus | None = None
    read_at: datetime | None = None


class NotificationResponse(NotificationBase):
    id: int
    status: NotificationStatus
    created_at: datetime
    read_at: datetime | None = None
    expires_at: datetime | None = None

    @validator("type", pre=True, always=True)
    @classmethod
    def validate_type(cls, v):
        """处理从数据库读取时的枚举类型转换"""
        if isinstance(v, str):
            return NotificationType(v)
        elif isinstance(v, NotificationType):
            return v
        return v

    @validator("priority", pre=True, always=True)
    @classmethod
    def validate_priority(cls, v):
        """处理从数据库读取时的优先级枚举转换"""
        if isinstance(v, str):
            return NotificationPriority(v)
        elif isinstance(v, NotificationPriority):
            return v
        return v

    @validator("status", pre=True, always=True)
    @classmethod
    def validate_status(cls, v):
        """处理从数据库读取时的状态枚举转换"""
        if isinstance(v, str):
            return NotificationStatus(v)
        elif isinstance(v, NotificationStatus):
            return v
        return v

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            NotificationType: lambda v: v.value if v else None,
            NotificationPriority: lambda v: v.value if v else None,
            NotificationStatus: lambda v: v.value if v else None,
        }


class NotificationListResponse(BaseModel):
    notifications: list[NotificationResponse]
    total: int
    unread_count: int


class UnreadCountResponse(BaseModel):
    unread_count: int


class WebSocketMessage(BaseModel):
    type: str
    data: dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class NewNotificationMessage(WebSocketMessage):
    type: str = "new_notification"
    data: NotificationResponse


class UnreadCountMessage(WebSocketMessage):
    type: str = "unread_count"
    data: dict[str, int]


class SystemNotificationMessage(WebSocketMessage):
    type: str = "system_notification"
    data: dict[str, Any]


class BroadcastNotificationCreate(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    message: str = Field(..., min_length=1)
    type: NotificationType = NotificationType.SYSTEM_UPDATE
    priority: NotificationPriority = NotificationPriority.HIGH
    data: dict[str, Any] | None = None
    action_url: str | None = Field(None, max_length=500)
    expires_in_hours: int | None = Field(168, ge=1, le=168)  # 默认7天
    target_user_type: str = Field("all", description="目标用户类型: all, active, premium等")


class BroadcastResponse(BaseModel):
    message: str
    total_users: int
    notifications_created: int
    online_users_notified: int
