import uuid
from enum import Enum as PyEnum

from sqlalchemy import (
    BigInteger,
    Column,
    Enum,
    ForeignKey,
    Integer,
    String,
)
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import text

from app.db.session import Base
from app.db.timestamp import Timestamp, now_utc


class BackpackItemType(str, PyEnum):
    """背包项目类型枚举"""

    COSTUME = "costume"
    SOUND = "sound"
    SPRITE = "sprite"
    SCRIPT = "script"


class BackpackItem(Base):
    """背包项目模型"""

    __tablename__ = "backpack_items"

    # 主键使用UUID
    id = Column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True, comment="项目唯一标识"
    )

    # 用户标识
    username = Column(String(50), nullable=False, index=True, comment="所属用户名")

    # 项目基本信息
    type = Column(
        Enum(BackpackItemType),
        nullable=False,
        index=True,
        comment="项目类型：costume/sound/sprite/script",
    )

    mime = Column(String(100), nullable=False, comment="资源MIME类型")

    name = Column(String(255), nullable=False, comment="用户自定义名称")

    # 文件路径
    body_path = Column(String(512), nullable=False, comment="资源文件相对路径")

    thumbnail_path = Column(String(512), nullable=False, comment="缩略图路径")

    # 文件大小（字节）
    size_bytes = Column(BigInteger, nullable=False, default=0, comment="主体资源大小（字节）")

    # 预留扩展字段
    extra_data = Column(JSONB, nullable=True, comment="预留字段（如分辨率、持续时长等）")

    # 时间戳
    created_at = Column(
        Timestamp,
        nullable=False,
        default=now_utc,
        server_default=text("CURRENT_TIMESTAMP"),
        comment="创建时间",
    )

    def __repr__(self):
        return f"<BackpackItem {self.name} ({self.type})>"

    @property
    def thumbnail(self) -> str:
        """返回缩略图相对路径，用于前端拼接"""
        return self.thumbnail_path

    @property
    def body(self) -> str:
        """返回资源文件相对路径，用于前端拼接"""
        return self.body_path

    @property
    def size(self) -> int:
        """返回文件大小"""
        return self.size_bytes
