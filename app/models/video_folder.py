from datetime import UTC, datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Foreign<PERSON>ey, Integer, String, Text
from sqlalchemy.orm import relationship

from app.db.session import Base
from app.db.timestamp import Timestamp


class VideoFolder(Base):
    """视频文件夹数据模型"""

    __tablename__ = "video_folders"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    path = Column(
        String(255),
        nullable=True,
        index=True,
        comment="存储路径，格式为：/user_{user_id}/{folder_name}",
    )
    user_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)
    # 父文件夹ID，实现文件夹层级结构
    parent_id = Column(Integer, ForeignKey("video_folders.id"), nullable=True, index=True)
    # 是否为默认文件夹（每个用户有且仅有一个默认文件夹）
    is_default = Column(Boolean, default=False, nullable=False)
    # 文件夹权限控制
    access_level = Column(
        Integer, default=0, comment="访问级别：0-私有，1-公开，2-链接可见，3-指定用户可见"
    )
    # 文件夹封面
    cover_url = Column(String(512), nullable=True, comment="文件夹封面图URL")
    # 排序权重
    sort_order = Column(Integer, default=0, comment="排序权重，值越大越靠前")
    # 文件夹描述
    description = Column(Text, nullable=True, comment="文件夹描述")
    # 文件夹颜色
    color = Column(String(20), nullable=True, comment="文件夹颜色，如#FF5733")
    # 软删除标记
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    created_at = Column(Timestamp, default=datetime.now(UTC), nullable=True)
    updated_at = Column(
        Timestamp, default=datetime.now(UTC), onupdate=datetime.now(UTC), nullable=True
    )
    deleted_at = Column(Timestamp, nullable=True)

    # 关联关系
    user = relationship("User", back_populates="video_folders")
    videos = relationship("Video", back_populates="folder")
    # 自引用关系，实现父子文件夹
    parent = relationship("VideoFolder", remote_side=[id], backref="children")

    # 计算属性：视频数量 - 存储值而非动态计算，避免异步环境问题
    _video_count = Column(Integer, default=0, comment="文件夹中的视频数量")

    @property
    def video_count(self):
        # 返回存储的值，避免在异步环境中访问关系属性
        return self._video_count

    # 提供一个方法来更新视频数量
    def update_video_count(self, count=None):
        if count is not None:
            self._video_count = count
        else:
            # 仅在显式调用更新方法时计算
            self._video_count = len([v for v in self.videos if not getattr(v, "is_deleted", False)])
        return self._video_count

    def can_access(self, user_id=None, link_token=None):
        """检查是否可以访问此文件夹"""
        if self.is_deleted:
            return False

        # 所有者总是有权限
        if user_id and self.user_id == user_id:
            return True

        if self.access_level == 0:  # 私有
            return False
        elif self.access_level == 1:  # 公开
            return True
        elif self.access_level == 2:  # 链接可见
            return link_token is not None
        elif self.access_level == 3:  # 指定用户可见
            return user_id is not None

        return False

    @property
    def is_public(self):
        """向后兼容的is_public属性"""
        return self.access_level == 1

    @is_public.setter
    def is_public(self, value):
        """向后兼容的is_public设置器"""
        self.access_level = 1 if value else 0

    @property
    def access_level_name(self):
        """返回访问级别的可读名称"""
        level_names = {0: "私有", 1: "公开", 2: "链接可见", 3: "指定用户可见"}
        return level_names.get(self.access_level, "未知")

    def __repr__(self):
        return f"<VideoFolder {self.name}>"
