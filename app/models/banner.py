from datetime import UTC, datetime

from sqlalchemy import (
    <PERSON><PERSON>an,
    <PERSON>umn,
    Foreign<PERSON>ey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship

from app.db.session import Base
from app.db.timestamp import Timestamp


class Banner(Base):
    """轮播图数据模型"""

    __tablename__ = "banners"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), index=True, nullable=False)
    description = Column(Text, nullable=True)
    image_url = Column(String(512), nullable=False, comment="轮播图片URL")
    link_url = Column(String(512), nullable=True, comment="点击跳转链接")
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=True, comment="关联的视频ID")
    # 排序权重
    sort_order = Column(Integer, default=0, comment="排序权重，值越大越靠前")
    # 是否启用
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    # 软删除标记
    is_deleted = Column(<PERSON><PERSON><PERSON>, default=False, nullable=False, index=True)
    created_at = Column(Timestamp, default=datetime.now(UTC), nullable=True)
    updated_at = Column(
        Timestamp, default=datetime.now(UTC), onupdate=datetime.now(UTC), nullable=True
    )
    deleted_at = Column(Timestamp, nullable=True)
    # 轮播图类型：首页、分类页等
    banner_type = Column(
        String(50), default="home", comment="轮播图类型：home-首页, category-分类页"
    )

    # 关联关系
    video = relationship("Video", back_populates="banners")

    def __repr__(self):
        return f"<Banner {self.title}>"
