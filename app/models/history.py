from datetime import UTC, datetime

from sqlalchemy import (
    Column,
    Foreign<PERSON>ey,
    Integer,
    String,
    UniqueConstraint,
)
from sqlalchemy.orm import relationship

from app.db.session import Base
from app.db.timestamp import Timestamp


class History(Base):
    """历史数据模型"""

    __tablename__ = "history"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)
    content_type = Column(String(20), nullable=False, index=True)
    content_id = Column(Integer, nullable=False, index=True)
    created_at = Column(Timestamp, default=datetime.now(UTC), nullable=True, index=True)
    updated_at = Column(
        Timestamp, default=datetime.now(UTC), onupdate=datetime.now(UTC), nullable=True
    )
    last_visited_at = Column(
        Timestamp, default=datetime.now(UTC), nullable=True
    )  # 新增：最后访问时间
    visit_count = Column(Integer, default=1)  # 新增：访问次数

    # 关联关系
    user = relationship("User")

    # 联合唯一约束：同一用户对同一内容只能记录一次
    __table_args__ = (
        UniqueConstraint("user_id", "content_type", "content_id", name="uq_user_content_history"),
    )

    def __repr__(self):
        return f"<History user_id={self.user_id} {self.content_type}:{self.content_id}>"
