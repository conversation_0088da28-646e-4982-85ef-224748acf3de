"""用户设备记录模型"""

from datetime import UTC, datetime

from sqlalchemy import (
    Boolean,
    Column,
    ForeignKey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship

from app.db.session import Base
from app.db.timestamp import Timestamp, now_utc


def ensure_utc(dt: datetime) -> datetime:
    """确保datetime对象有UTC时区信息"""
    if dt is None:
        return dt
    if dt.tzinfo is None:
        return dt.replace(tzinfo=UTC)
    return dt.astimezone(UTC)


class UserDevice(Base):
    """用户设备记录表"""

    __tablename__ = "user_devices"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)

    # 设备标识信息
    device_fingerprint = Column(String(255), nullable=False, index=True, comment="设备指纹")
    device_name = Column(String(100), nullable=True, comment="设备名称")
    device_type = Column(String(20), nullable=True, comment="设备类型：mobile, tablet, desktop")

    # 网络信息
    ip_address = Column(String(45), nullable=True, comment="IP地址")
    location = Column(String(100), nullable=True, comment="地理位置")

    # 浏览器信息
    user_agent = Column(Text, nullable=True, comment="用户代理字符串")
    browser_name = Column(String(50), nullable=True, comment="浏览器名称")
    browser_version = Column(String(20), nullable=True, comment="浏览器版本")
    os_name = Column(String(50), nullable=True, comment="操作系统名称")
    os_version = Column(String(20), nullable=True, comment="操作系统版本")

    # 信任状态
    is_trusted = Column(Boolean, default=False, comment="是否为信任设备")
    trust_score = Column(Integer, default=0, comment="信任分数(0-100)")

    # 使用统计
    login_count = Column(Integer, default=1, comment="登录次数")
    last_login_at = Column(Timestamp, nullable=False, default=now_utc, comment="最后登录时间")
    first_login_at = Column(Timestamp, nullable=False, default=now_utc, comment="首次登录时间")

    # 状态管理
    is_active = Column(Boolean, default=True, comment="是否活跃")
    is_blocked = Column(Boolean, default=False, comment="是否被阻止")
    blocked_reason = Column(String(200), nullable=True, comment="阻止原因")

    # 时间戳
    created_at = Column(Timestamp, default=now_utc)
    updated_at = Column(Timestamp, default=now_utc, onupdate=now_utc)

    # 关联关系
    user = relationship("User", back_populates="devices")

    def __repr__(self):
        return f"<UserDevice {self.device_fingerprint} for user {self.user_id}>"

    @property
    def is_new_device(self) -> bool:
        """判断是否为新设备（首次登录不超过24小时）"""
        if not self.first_login_at:
            return True

        delta = now_utc() - ensure_utc(self.first_login_at)
        return delta.total_seconds() < 24 * 3600

    @property
    def is_frequent_device(self) -> bool:
        """判断是否为常用设备（登录次数>=5次且最近30天内有登录）"""
        if self.login_count < 5:
            return False
        if not self.last_login_at:
            return False
        delta = now_utc() - ensure_utc(self.last_login_at)
        return delta.total_seconds() < 30 * 24 * 3600

    def update_login_info(self, ip_address: str = None, location: str = None):
        """更新登录信息"""
        self.login_count += 1
        self.last_login_at = now_utc()
        if ip_address:
            self.ip_address = ip_address
        if location:
            self.location = location

    def calculate_trust_score(self) -> int:
        """计算信任分数"""
        score = 0

        # 基于登录次数（最多40分）
        login_count = self.login_count or 1  # 确保不为None
        score += min(login_count * 2, 40)

        # 基于使用时长（最多30分）
        if self.first_login_at:
            days_used = (now_utc() - ensure_utc(self.first_login_at)).days
            score += min(days_used, 30)

        # 基于最近活跃度（最多20分）
        if self.last_login_at:
            days_since_last = (now_utc() - ensure_utc(self.last_login_at)).days
            if days_since_last <= 7:
                score += 20
            elif days_since_last <= 30:
                score += 10

        # 手动信任加分（最多10分）
        if self.is_trusted:
            score += 10

        self.trust_score = min(score, 100)
        return self.trust_score
