import httpx


async def get_wechat_access_token(app_id: str, app_secret: str) -> dict:
    url = "https://api.weixin.qq.com/cgi-bin/token"
    params = {
        "grant_type": "client_credential",
        "appid": app_id,
        "secret": app_secret,
    }

    async with httpx.AsyncClient() as client:
        response = await client.get(url, params=params)
        return response


async def get_wechat_qr_code(access_token: str, scene_str: str, expire_seconds: int = 600):
    url = f"https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token={access_token}"
    data = {
        "expire_seconds": expire_seconds,
        "action_name": "QR_STR_SCENE",
        "action_info": {"scene": {"scene_str": scene_str}},
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(url, json=data)
        return response


async def get_wechat_user_info(access_token: str, openid: str) -> dict:
    url = "https://api.weixin.qq.com/cgi-bin/user/info"
    params = {"access_token": access_token, "openid": openid, "lang": "zh_CN"}

    async with httpx.AsyncClient() as client:
        response = await client.get(url, params=params)
        return response
