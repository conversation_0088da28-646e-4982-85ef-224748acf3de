"""认证辅助工具函数"""

import re
from typing import Optional


class AuthValidator:
    """认证数据验证器"""
    
    @staticmethod
    def validate_phone_number(phone: str) -> bool:
        """验证手机号格式"""
        if not phone or len(phone) != 11:
            return False
        return phone.isdigit() and phone.startswith(("13", "14", "15", "16", "17", "18", "19"))
    
    @staticmethod
    def validate_verification_code(code: str) -> bool:
        """验证验证码格式"""
        if not code or len(code) != 6:
            return False
        return code.isdigit()
    
    @staticmethod
    def validate_openid(openid: str) -> bool:
        """验证微信OpenID格式"""
        if not openid:
            return False
        # 微信OpenID通常是28位字符串
        return len(openid) >= 20 and len(openid) <= 32
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """验证邮箱格式"""
        if not email:
            return False
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def validate_password(password: str) -> bool:
        """验证密码强度"""
        if not password or len(password) < 8:
            return False
        # 至少包含字母和数字
        has_letter = re.search(r'[a-zA-Z]', password) is not None
        has_digit = re.search(r'\d', password) is not None
        return has_letter and has_digit
    
    @staticmethod
    def sanitize_phone_for_logging(phone: str) -> str:
        """脱敏处理手机号用于日志记录"""
        if not phone or len(phone) != 11:
            return phone
        return f"{phone[:3]}****{phone[7:]}"
    
    @staticmethod
    def sanitize_openid_for_logging(openid: str) -> str:
        """脱敏处理OpenID用于日志记录"""
        if not openid or len(openid) < 8:
            return openid
        return f"{openid[:4]}****{openid[-4:]}"
    
    @staticmethod
    def sanitize_email_for_logging(email: str) -> str:
        """脱敏处理邮箱用于日志记录"""
        if not email or '@' not in email:
            return email
        username, domain = email.split('@', 1)
        if len(username) <= 2:
            return f"{username[0]}***@{domain}"
        return f"{username[:2]}***@{domain}"


class AuthConstants:
    """认证相关常量"""
    
    # 认证方法
    AUTH_METHOD_SMS = "sms"
    AUTH_METHOD_WECHAT = "wechat"
    AUTH_METHOD_PASSWORD = "password"
    AUTH_METHOD_DEVICE_VERIFICATION = "device_verification"
    
    # 设备信任级别
    DEVICE_TRUST_LEVEL_UNKNOWN = 0
    DEVICE_TRUST_LEVEL_LOW = 1
    DEVICE_TRUST_LEVEL_MEDIUM = 2
    DEVICE_TRUST_LEVEL_HIGH = 3
    DEVICE_TRUST_LEVEL_TRUSTED = 4
    
    # 验证码类型
    SMS_TYPE_LOGIN = "login"
    SMS_TYPE_REGISTER = "register"
    SMS_TYPE_BIND_PHONE = "bind_phone"
    SMS_TYPE_DEVICE_VERIFICATION = "device_verification"
    
    # 微信扫码状态
    WECHAT_SCAN_STATUS_PENDING = "pending"
    WECHAT_SCAN_STATUS_SCANNED = "scanned"
    WECHAT_SCAN_STATUS_CONFIRMED = "confirmed"
    WECHAT_SCAN_STATUS_EXPIRED = "expired"
    WECHAT_SCAN_STATUS_CANCELLED = "cancelled"
    
    # 缓存键前缀
    CACHE_PREFIX_SMS_CODE = "sms_code:"
    CACHE_PREFIX_SMS_ATTEMPTS = "sms_attempts:"
    CACHE_PREFIX_DEVICE_VERIFICATION = "device_verification:"
    CACHE_PREFIX_WECHAT_SCENE = "wechat_scene:"
    CACHE_PREFIX_LOGIN_ATTEMPTS = "login_attempts:"
    
    # 过期时间（秒）
    SMS_CODE_EXPIRE_TIME = 300  # 5分钟
    DEVICE_VERIFICATION_EXPIRE_TIME = 1800  # 30分钟
    WECHAT_QR_CODE_EXPIRE_TIME = 600  # 10分钟
    LOGIN_ATTEMPT_WINDOW = 3600  # 1小时
    
    # 限制次数
    MAX_SMS_ATTEMPTS_PER_PHONE = 5
    MAX_LOGIN_ATTEMPTS_PER_IP = 10
    MAX_DEVICE_VERIFICATION_ATTEMPTS = 3


class AuthResponseBuilder:
    """认证响应构建器"""
    
    @staticmethod
    def build_initiation_response(data: dict) -> dict:
        """构建认证发起响应"""
        return {
            "success": data.get("success", False),
            "message": data.get("message", ""),
            "auth_type": data.get("auth_type", ""),
            **{k: v for k, v in data.items() if k not in ["success", "message", "auth_type"]}
        }
    
    @staticmethod
    def build_success_response(user, access_token: str, auth_method: str, message: str = "认证成功"):
        """构建成功响应"""
        from datetime import datetime
        return {
            "success": True,
            "message": message,
            "user": user.model_dump() if hasattr(user, 'model_dump') else user,
            "access_token": access_token,
            "token_type": "bearer",
            "auth_method": auth_method,
            "timestamp": datetime.utcnow(),
        }
    
    @staticmethod
    def build_device_verification_response(verification_token: str, message: str = "需要设备验证"):
        """构建设备验证响应"""
        from datetime import datetime
        return {
            "success": False,
            "message": message,
            "requires_device_verification": True,
            "verification_token": verification_token,
            "timestamp": datetime.utcnow(),
        }
    
    @staticmethod
    def build_failure_response(error_code: str, auth_method: str, message: str = None):
        """构建失败响应"""
        from app.core.auth_error_codes import AuthErrorMessages
        from datetime import datetime
        
        if message is None:
            message = AuthErrorMessages.get_message(error_code)
            
        return {
            "success": False,
            "message": message,
            "auth_method": auth_method,
            "timestamp": datetime.utcnow(),
        }