from datetime import timed<PERSON><PERSON>
from fastapi import Response

from app.config import get_settings


def _cookie_attrs():
    settings = get_settings()
    if getattr(settings, "MODE", "dev") == "dev":
        return {"secure": False, "samesite": "lax"}
    return {"secure": True, "samesite": "none"}


def set_auth_cookies(
    response: Response,
    access_token: str | None = None,
    refresh_token: str | None = None,
):
    settings = get_settings()
    attrs = _cookie_attrs()

    if access_token:
        max_age = int(getattr(settings, "ACCESS_TOKEN_EXPIRE_MINUTES", 60) * 60)
        response.set_cookie(
            key="access_token",
            value=access_token,
            httponly=True,
            secure=attrs["secure"],
            samesite=attrs["samesite"],
            max_age=max_age,
            path="/",
        )

    if refresh_token:
        max_age = int(getattr(settings, "REFRESH_TOKEN_EXPIRE_MINUTES", 60 * 24 * 30) * 60)
        response.set_cookie(
            key="refresh_token",
            value=refresh_token,
            httponly=True,
            secure=attrs["secure"],
            samesite=attrs["samesite"],
            max_age=max_age,
            path="/",
        )


def clear_auth_cookies(response: Response):
    response.delete_cookie(key="access_token", path="/")
    response.delete_cookie(key="refresh_token", path="/")

