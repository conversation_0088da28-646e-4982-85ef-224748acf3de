"""手机号处理工具类 - 统一手机号格式处理"""

import re


class PhoneUtils:
    """手机号处理工具类"""

    # 中国大陆手机号正则表达式
    CHINA_MOBILE_PATTERN = r"^1[3-9]\d{9}$"

    @classmethod
    def validate_phone(cls, phone: str) -> bool:
        """
        验证手机号格式

        Args:
            phone: 手机号字符串

        Returns:
            是否为有效的手机号格式
        """
        if not phone:
            return False

        # 移除所有非数字字符
        clean_phone = re.sub(r"\D", "", phone)

        return bool(re.match(cls.CHINA_MOBILE_PATTERN, clean_phone))

    @classmethod
    def normalize_phone(cls, phone: str) -> str | None:
        """
        标准化手机号格式

        Args:
            phone: 原始手机号

        Returns:
            标准化后的手机号，如果格式无效则返回None
        """
        if not phone:
            return None

        # 移除所有非数字字符
        clean_phone = re.sub(r"\D", "", phone)

        # 验证格式
        if not re.match(cls.CHINA_MOBILE_PATTERN, clean_phone):
            return None

        return clean_phone

    @classmethod
    def mask_phone(cls, phone: str, mask_char: str = "*") -> str:
        """
        掩码手机号用于显示

        Args:
            phone: 完整手机号
            mask_char: 掩码字符，默认为*

        Returns:
            掩码后的手机号，格式：138****1234
        """
        if not phone or len(phone) != 11:
            return phone

        return phone[:3] + mask_char * 4 + phone[7:]

    @classmethod
    def unmask_phone(cls, masked_phone: str, full_phone: str) -> str | None:
        """
        从掩码手机号恢复完整手机号

        Args:
            masked_phone: 掩码手机号
            full_phone: 候选的完整手机号

        Returns:
            如果匹配则返回完整手机号，否则返回None
        """
        if not masked_phone or not full_phone:
            return None

        # 标准化完整手机号
        normalized_full = cls.normalize_phone(full_phone)
        if not normalized_full:
            return None

        # 生成该完整手机号的掩码版本
        generated_mask = cls.mask_phone(normalized_full)

        # 比较掩码版本
        if generated_mask == masked_phone:
            return normalized_full

        return None

    @classmethod
    def is_masked_phone(cls, phone: str) -> bool:
        """
        判断是否为掩码手机号

        Args:
            phone: 手机号字符串

        Returns:
            是否为掩码格式
        """
        if not phone:
            return False

        # 检查是否包含掩码字符且格式类似 138****1234
        pattern = r"^1[3-9]\d\*{4}\d{4}$"
        return bool(re.match(pattern, phone))

    @classmethod
    def extract_phone_info(cls, phone: str) -> tuple[bool, bool, str | None]:
        """
        提取手机号信息

        Args:
            phone: 手机号字符串

        Returns:
            (是否有效, 是否掩码, 标准化手机号或None)
        """
        if not phone:
            return False, False, None

        is_masked = cls.is_masked_phone(phone)

        if is_masked:
            return True, True, None

        normalized = cls.normalize_phone(phone)
        is_valid = normalized is not None

        return is_valid, False, normalized

    @classmethod
    def compare_phones(cls, phone1: str, phone2: str) -> bool:
        """
        比较两个手机号是否相同（支持掩码比较）

        Args:
            phone1: 手机号1
            phone2: 手机号2

        Returns:
            是否为同一手机号
        """
        if not phone1 or not phone2:
            return False

        # 如果都是完整手机号，直接比较标准化版本
        norm1 = cls.normalize_phone(phone1)
        norm2 = cls.normalize_phone(phone2)

        if norm1 and norm2:
            return norm1 == norm2

        # 如果其中一个是掩码，尝试匹配
        if cls.is_masked_phone(phone1) and norm2:
            return cls.unmask_phone(phone1, norm2) is not None

        if cls.is_masked_phone(phone2) and norm1:
            return cls.unmask_phone(phone2, norm1) is not None

        return False


class PhoneValidationError(Exception):
    """手机号验证错误"""

    def __init__(self, message: str, phone: str | None = None, error_code: str | None = None):
        super().__init__(message)
        self.message = message
        self.phone = phone
        self.error_code = error_code


def validate_and_normalize_phone(phone: str, allow_masked: bool = False) -> str:
    """
    验证并标准化手机号的便捷函数

    Args:
        phone: 原始手机号
        allow_masked: 是否允许掩码手机号

    Returns:
        标准化后的手机号

    Raises:
        PhoneValidationError: 手机号格式错误
    """
    if not phone:
        raise PhoneValidationError("手机号不能为空", phone=phone, error_code="EMPTY_PHONE")

    is_valid, is_masked, normalized = PhoneUtils.extract_phone_info(phone)

    if not is_valid:
        raise PhoneValidationError("手机号格式不正确", phone=phone, error_code="INVALID_FORMAT")

    if is_masked and not allow_masked:
        raise PhoneValidationError(
            "不允许使用掩码手机号", phone=phone, error_code="MASKED_NOT_ALLOWED"
        )

    if is_masked:
        return phone  # 保持掩码格式

    return normalized


def mask_phone_for_display(phone: str) -> str:
    """
    为显示目的掩码手机号的便捷函数

    Args:
        phone: 完整手机号

    Returns:
        掩码后的手机号
    """
    return PhoneUtils.mask_phone(phone)


def resolve_phone_number(input_phone: str, stored_phone: str | None = None) -> str | None:
    """
    解析手机号，处理掩码情况

    Args:
        input_phone: 输入的手机号（可能是掩码）
        stored_phone: 存储的完整手机号

    Returns:
        解析后的完整手机号，失败返回None
    """
    if not input_phone:
        return None

    # 如果输入是完整手机号，直接标准化
    normalized = PhoneUtils.normalize_phone(input_phone)
    if normalized:
        return normalized

    # 如果输入是掩码且提供了存储的手机号，尝试匹配
    if PhoneUtils.is_masked_phone(input_phone) and stored_phone:
        return PhoneUtils.unmask_phone(input_phone, stored_phone)

    return None
