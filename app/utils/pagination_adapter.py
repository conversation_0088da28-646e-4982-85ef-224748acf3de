"""
分页适配器 - 将传统分页结果转换为游标分页格式
"""

from typing import Any

from app.core.pagination import CursorPaginationParams, CursorPaginationResponse
from app.schemas.recommendation import PaginatedRecommendationResponse


class PaginationAdapter:
    """分页适配器，用于统一不同分页方式的响应格式"""

    @staticmethod
    def cursor_to_page(cursor: str | None) -> int:
        """将游标转换为页码（从1开始）"""
        if not cursor:
            return 1
        try:
            return max(1, int(cursor))
        except (ValueError, TypeError):
            return 1

    @staticmethod
    def cursor_to_offset(cursor: str | None) -> int:
        """将游标转换为偏移量（从0开始）"""
        if not cursor:
            return 0
        try:
            return max(0, int(cursor))
        except (ValueError, TypeError):
            return 0

    @staticmethod
    def adapt_page_based_to_cursor(
        items: list[Any],
        pagination: CursorPaginationParams,
        has_more_items: bool,
        total_count: int | None = None,
    ) -> CursorPaginationResponse:
        """
        将基于页码的分页结果适配为游标分页响应
        
        Args:
            items: 实际的数据项列表
            pagination: 原始的游标分页参数
            has_more_items: 是否还有更多数据
            total_count: 总数量（可选）
            
        Returns:
            游标分页响应
        """
        current_page = PaginationAdapter.cursor_to_page(pagination.cursor)
        
        # 计算分页状态
        has_next = has_more_items
        has_previous = current_page > 1
        
        # 计算游标
        next_cursor = str(current_page + 1) if has_next else None
        previous_cursor = str(current_page - 1) if has_previous else None
        
        return CursorPaginationResponse(
            items=items,
            total_count=total_count or len(items),
            has_next=has_next,
            has_previous=has_previous,
            next_cursor=next_cursor,
            previous_cursor=previous_cursor,
        )

    @staticmethod
    def adapt_offset_based_to_cursor(
        items: list[Any],
        pagination: CursorPaginationParams,
        has_more_items: bool,
        total_count: int | None = None,
    ) -> CursorPaginationResponse:
        """
        将基于偏移量的分页结果适配为游标分页响应
        
        Args:
            items: 实际的数据项列表
            pagination: 原始的游标分页参数
            has_more_items: 是否还有更多数据
            total_count: 总数量（可选）
            
        Returns:
            游标分页响应
        """
        current_offset = PaginationAdapter.cursor_to_offset(pagination.cursor)
        
        # 计算分页状态
        has_next = has_more_items
        has_previous = current_offset > 0
        
        # 计算游标
        next_cursor = str(current_offset + pagination.size) if has_next else None
        previous_cursor = str(max(0, current_offset - pagination.size)) if has_previous else None
        if previous_cursor == "0":
            previous_cursor = None
        
        return CursorPaginationResponse(
            items=items,
            total_count=total_count or len(items),
            has_next=has_next,
            has_previous=has_previous,
            next_cursor=next_cursor,
            previous_cursor=previous_cursor,
        )

    @staticmethod
    def adapt_recommendation_to_cursor(
        items: list[Any],
        pagination: CursorPaginationParams,
        recommendation_result: PaginatedRecommendationResponse,
    ) -> CursorPaginationResponse:
        """
        将推荐服务的分页结果适配为游标分页响应
        
        Args:
            items: 聚合后的实际数据项列表
            pagination: 原始的游标分页参数
            recommendation_result: 推荐服务返回的分页结果
            
        Returns:
            游标分页响应
        """
        # 推荐服务使用的是偏移量分页
        return PaginationAdapter.adapt_offset_based_to_cursor(
            items=items,
            pagination=pagination,
            has_more_items=recommendation_result.has_next,
            total_count=len(items),  # 简化处理，不提供总数
        )

    @staticmethod
    def empty_cursor_response() -> CursorPaginationResponse:
        """返回空的游标分页响应"""
        return CursorPaginationResponse(
            items=[],
            total_count=0,
            has_next=False,
            has_previous=False,
            next_cursor=None,
            previous_cursor=None,
        )
