"""时间工具类 - 统一时间处理"""

from datetime import UTC, datetime, timedelta

import pytz


class TimeUtils:
    """统一时间处理工具类"""

    # 默认时区设置
    DEFAULT_TIMEZONE = UTC
    LOCAL_TIMEZONE = pytz.timezone("Asia/Shanghai")  # 根据项目需要调整

    @classmethod
    def get_utc_now(cls) -> datetime:
        """获取当前UTC时间"""
        return datetime.now(cls.DEFAULT_TIMEZONE)

    @classmethod
    def get_local_now(cls) -> datetime:
        """获取当前本地时间"""
        return datetime.now(cls.LOCAL_TIMEZONE)

    @classmethod
    def to_utc(cls, dt: datetime) -> datetime:
        """转换为UTC时间"""
        if dt.tzinfo is None:
            # 如果没有时区信息，假设是UTC
            return dt.replace(tzinfo=cls.DEFAULT_TIMEZONE)
        return dt.astimezone(cls.DEFAULT_TIMEZONE)

    @classmethod
    def to_local(cls, dt: datetime) -> datetime:
        """转换为本地时间"""
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=cls.DEFAULT_TIMEZONE)
        return dt.astimezone(cls.LOCAL_TIMEZONE)

    @classmethod
    def calculate_expiry(cls, minutes: int, base_time: datetime | None = None) -> datetime:
        """
        计算过期时间

        Args:
            minutes: 过期分钟数
            base_time: 基准时间，默认为当前UTC时间

        Returns:
            过期时间（UTC）
        """
        base_time = cls.get_utc_now() if base_time is None else cls.to_utc(base_time)

        return base_time + timedelta(minutes=minutes)

    @classmethod
    def calculate_expiry_seconds(cls, seconds: int, base_time: datetime | None = None) -> datetime:
        """
        计算过期时间（秒）

        Args:
            seconds: 过期秒数
            base_time: 基准时间，默认为当前UTC时间

        Returns:
            过期时间（UTC）
        """
        base_time = cls.get_utc_now() if base_time is None else cls.to_utc(base_time)

        return base_time + timedelta(seconds=seconds)

    @classmethod
    def is_expired(cls, expiry_time: datetime, check_time: datetime | None = None) -> bool:
        """
        检查是否已过期

        Args:
            expiry_time: 过期时间
            check_time: 检查时间，默认为当前UTC时间

        Returns:
            是否已过期
        """
        check_time = cls.get_utc_now() if check_time is None else cls.to_utc(check_time)

        expiry_time = cls.to_utc(expiry_time)
        return check_time >= expiry_time

    @classmethod
    def format_for_jwt(cls, dt: datetime) -> str:
        """格式化时间用于JWT"""
        utc_dt = cls.to_utc(dt)
        return utc_dt.isoformat()

    @classmethod
    def parse_jwt_time(cls, time_str: str) -> datetime:
        """解析JWT中的时间字符串"""
        dt = datetime.fromisoformat(time_str.replace("Z", "+00:00"))
        return cls.to_utc(dt)

    @classmethod
    def timestamp_to_datetime(cls, timestamp: float) -> datetime:
        """时间戳转换为datetime对象"""
        return datetime.fromtimestamp(timestamp, tz=cls.DEFAULT_TIMEZONE)

    @classmethod
    def datetime_to_timestamp(cls, dt: datetime) -> float:
        """datetime对象转换为时间戳"""
        utc_dt = cls.to_utc(dt)
        return utc_dt.timestamp()

    @classmethod
    def format_display(cls, dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
        """格式化时间用于显示"""
        local_dt = cls.to_local(dt)
        return local_dt.strftime(format_str)

    @classmethod
    def get_time_diff_minutes(cls, start_time: datetime, end_time: datetime) -> int:
        """获取两个时间的分钟差"""
        start_utc = cls.to_utc(start_time)
        end_utc = cls.to_utc(end_time)
        diff = end_utc - start_utc
        return int(diff.total_seconds() / 60)

    @classmethod
    def get_time_diff_seconds(cls, start_time: datetime, end_time: datetime) -> int:
        """获取两个时间的秒差"""
        start_utc = cls.to_utc(start_time)
        end_utc = cls.to_utc(end_time)
        diff = end_utc - start_utc
        return int(diff.total_seconds())

    @classmethod
    def add_minutes(cls, dt: datetime, minutes: int) -> datetime:
        """为时间添加分钟"""
        return dt + timedelta(minutes=minutes)

    @classmethod
    def add_seconds(cls, dt: datetime, seconds: int) -> datetime:
        """为时间添加秒"""
        return dt + timedelta(seconds=seconds)

    @classmethod
    def subtract_minutes(cls, dt: datetime, minutes: int) -> datetime:
        """为时间减去分钟"""
        return dt - timedelta(minutes=minutes)

    @classmethod
    def subtract_seconds(cls, dt: datetime, seconds: int) -> datetime:
        """为时间减去秒"""
        return dt - timedelta(seconds=seconds)

    @classmethod
    def subtract_days(cls, dt: datetime, days: int) -> datetime:
        """为时间减去天数"""
        return dt - timedelta(days=days)

    @classmethod
    def get_naive_utc_now(cls) -> datetime:
        """获取时区无关的UTC时间，用于数据库字段"""
        from datetime import datetime
        return datetime.utcnow()


class JWTTimeHelper:
    """JWT时间处理辅助类"""

    @staticmethod
    def create_jwt_payload_time(expire_minutes: int, issued_at: datetime | None = None) -> dict:
        """
        创建JWT载荷的时间字段

        Args:
            expire_minutes: 过期分钟数
            issued_at: 签发时间，默认为当前时间

        Returns:
            包含iat和exp字段的字典
        """
        issued_at = TimeUtils.get_utc_now() if issued_at is None else TimeUtils.to_utc(issued_at)

        expire_time = TimeUtils.calculate_expiry(expire_minutes, issued_at)

        return {
            "iat": TimeUtils.datetime_to_timestamp(issued_at),
            "exp": TimeUtils.datetime_to_timestamp(expire_time),
            "issued_at": TimeUtils.format_for_jwt(issued_at),
        }

    @staticmethod
    def validate_jwt_time(payload: dict) -> tuple[bool, str | None]:
        """
        验证JWT时间字段

        Args:
            payload: JWT载荷

        Returns:
            (是否有效, 错误信息)
        """
        try:
            exp = payload.get("exp")
            if not exp:
                return False, "缺少过期时间"

            expire_time = TimeUtils.timestamp_to_datetime(exp)
            if TimeUtils.is_expired(expire_time):
                return False, "令牌已过期"

            return True, None

        except Exception as e:
            return False, f"时间验证失败: {str(e)}"


# 便捷函数
def utc_now() -> datetime:
    """获取当前UTC时间的便捷函数"""
    return TimeUtils.get_utc_now()


def local_now() -> datetime:
    """获取当前本地时间的便捷函数"""
    return TimeUtils.get_local_now()


def calculate_expiry(minutes: int, base_time: datetime | None = None) -> datetime:
    """计算过期时间的便捷函数"""
    return TimeUtils.calculate_expiry(minutes, base_time)


def is_expired(expiry_time: datetime) -> bool:
    """检查是否过期的便捷函数"""
    return TimeUtils.is_expired(expiry_time)
